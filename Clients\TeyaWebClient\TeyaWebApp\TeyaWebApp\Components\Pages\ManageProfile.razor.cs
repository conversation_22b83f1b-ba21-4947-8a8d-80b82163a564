﻿using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using Unity;
//using FastMember;
//using Microsoft.Azure.Amqp.Framing;
//using Microsoft.Azure.Amqp.Framing;

namespace TeyaWebApp.Components.Pages
{
    public partial class ManageProfile
    {
        private Dictionary<string, object> userDetails;
        private UpdatedUserViewModel userProfile = new UpdatedUserViewModel();
        private bool Subscription = false;

        private Color SaveButtonColor { get; set; } = Color.Primary;
        [Inject] public HttpClient Http { get; set; }
        private bool _isLoading = false;
        private Guid activeUser;
        private bool _isThemeLoaded = false;
        private MudTheme CurrentTheme;
        private ThemeItem SelectedTheme;
        private List<UserTheme> UsersTheme;
        private List<ThemeItem> Themes = new();
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserThemeService UserThemeService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] public GraphApiService GraphApiService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [Inject]
        private ILogger<ManageProfile> Logger { get; set; } = default!;

        [Inject]
        private IMemberService memberService { get; set; } = default!;
        [Inject] UserContext usercontext { get; set; }
        [Inject] IAddressService AddressService { get; set; }
        [Inject] ISnackbar Snackbar {get;set;}
        [Inject] private ICountryService countryService { get; set; }
        private Member memberDetails;
        private Guid organizationId;

        private Member LoggedInUser { get; set; }
        private TeyaUIModels.Model.Address User_Address { get; set; }
        private Guid? User_AddressID { get; set; }
        private Guid User_OrganizationID { get; set; } 
        private bool User_Subscription { get; set; } 
        private string User_UserName { get; set; } 
        private string User_FirstName { get; set; }
        private string User_LastName { get; set; }
        private string User_Email { get; set; }
        private string User_Role { get; set; }
        private string User_OrganizationName { get; set; }
        private string User_AddressLine1 { get; set; }
        private string User_AddressLine2 { get; set; }
        private string User_City { get; set; }
        private string User_State { get; set; }
        private string User_PostalCode { get; set; }
        private string User_Country { get; set; }

        private List<Country> _allCountries = new();
        private string[] _countryNames = Array.Empty<string>();
       
        private MudForm? form;
        private bool isFormValid;
        protected override async Task OnInitializedAsync()
        {
            _allCountries = (await countryService.GetAllCountriesAsync()).ToList();
            _countryNames = _allCountries.Select(c => c.CountryName)
                             .OrderBy(name => name)
                             .ToArray();
            User_OrganizationID = usercontext.ActiveUserOrganizationID;
            User_Subscription = usercontext.ActiveUserSubscription;
            LoggedInUser = await memberService.GetMemberByIdAsync(Guid.Parse(User.id), User_OrganizationID, User_Subscription);
            if (LoggedInUser.AddressId == null)
            {
                User_Address = new TeyaUIModels.Model.Address(); 
                User_Address.AddressId = Guid.NewGuid();
                User_Address.OrganizationID = User_OrganizationID;
                var addressAdded = await AddressService.AddAddressAsync(User_Address);
                if (!addressAdded)
                {
                    Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                    return;
                }
                LoggedInUser.AddressId = User_Address.AddressId;
                await memberService.UpdateMemberByIdAsync(LoggedInUser.Id, LoggedInUser);
            }
            var addressID = LoggedInUser.AddressId ?? Guid.NewGuid();
            var useraddress = await AddressService.GetAddressByIdAsync(addressID, User_OrganizationID, User_Subscription);
            User_Address = useraddress;
            SetUserDetails(LoggedInUser);
            SetUserAddress(User_Address);
            try
            {
                var themesJson = await Http.GetStringAsync($"{Navigation.BaseUri}themes.json");
                var themes = JsonSerializer.Deserialize<Dictionary<string, ThemeModel>>(JsonDocument.Parse(themesJson).RootElement.GetProperty("themes").ToString());
                foreach (var themeData in themes)
                {
                    var themeModel = themeData.Value;
                    var mudTheme = MapToMudTheme(themeModel);

                    Themes.Add(new ThemeItem
                    {
                        Theme = MapToThemeModel(mudTheme),
                        Name = themeData.Key
                    });
                }
                organizationId = User_OrganizationID;
                Subscription = User_Subscription;
                UsersTheme = (await UserThemeService.GetUserThemesAsync(organizationId, Subscription)).ToList();

                if (string.IsNullOrEmpty(User?.id) || !Guid.TryParse(User.id, out Guid parsedUserId))
                {
                    SelectedTheme = Themes.FirstOrDefault();
                    CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
                    _isThemeLoaded = true;
                    return;
                }
                activeUser = parsedUserId;
                var existingUserTheme = UsersTheme.FirstOrDefault(ut => activeUser == ut.UserId);
                SelectedTheme = Themes.FirstOrDefault(t => t.Name == existingUserTheme?.ThemeName);

                if (existingUserTheme == null)
                {
                    SelectedTheme = Themes.FirstOrDefault();
                    var newUserTheme = new UserTheme { UserId = activeUser, ThemeName = SelectedTheme.Name,OrganizationID = organizationId, Subscription=Subscription };
                    await UserThemeService.AddUserThemeAsync(newUserTheme);
                }
                CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error initializing theme: {ex.Message}");
            }
            finally
            {
                _isThemeLoaded = true;
                StateHasChanged();
            }
        }


        private void SetUserDetails(Member member)
        {
            User_UserName = member.UserName;
            User_FirstName = member.FirstName;
            User_LastName = member.LastName;
            User_OrganizationName = member.OrganizationName;
            User_Role = member.RoleName;
            User_Email = member.Email;
        }

        private void SetUserAddress(Address address)
        {
            User_AddressLine1 = address.AddressLine1;
            User_AddressLine2 = address.AddressLine2;
            User_City = address.City;
            User_State = address.State;
            User_PostalCode = address.PostalCode;
            User_Country = address.Country;
        }
        private async Task Save()
        {
            // 1. Validate required fields (excluding AddressLine2)
            var nullFields = new List<string>();

            if (string.IsNullOrWhiteSpace(User_UserName))
                nullFields.Add("Username");
            if (string.IsNullOrWhiteSpace(User_FirstName))
                nullFields.Add("First Name");
            if (string.IsNullOrWhiteSpace(User_LastName))
                nullFields.Add("Last Name");
            if (string.IsNullOrWhiteSpace(User_AddressLine1))
                nullFields.Add("Address Line 1");
            if (string.IsNullOrWhiteSpace(User_City))
                nullFields.Add("City");
            if (string.IsNullOrWhiteSpace(User_State))
                nullFields.Add("State/Province");
            if (string.IsNullOrWhiteSpace(User_PostalCode))
                nullFields.Add("Postal Code");
            if (string.IsNullOrWhiteSpace(User_Country))
                nullFields.Add("Country");

            if (nullFields.Count > 0)
            {
                await DialogService.ShowMessageBox(
                    title: Localizer["Validation Error"],
                    message: Localizer["The following fields are required:"] + "\n\n• " + string.Join("\n• ", nullFields),
                    yesText: Localizer["OK"]);
                return;
            }

            await form!.Validate();

            if (!isFormValid)
            {
                Snackbar.Add(Localizer["Please correct the errors before saving."], Severity.Error);
                return;
            }
            

            // 2. Check if any changes exist
            bool hasChanges = HasUserChanges() || HasAddressChanges();

            if (!hasChanges)
            {
                // No changes - nothing to save
                await DialogService.ShowMessageBox(
                    title: Localizer["No Changes"],
                    message: Localizer["No Changes Detected"]);
                return;
            }

            // 3. Show confirmation dialog if changes exist
            var confirmed = await DialogService.ShowMessageBox(
                title: Localizer["Confirm Save"],
                message: Localizer["Save Changes Confirmation"],
                yesText: Localizer["Save"],
                noText: Localizer["Cancel"]);

            if (confirmed != true) return;

            // 4. Proceed with saving
            try
            {
                // Track which fields need to be updated in Microsoft Entra
                var updateFields = new Dictionary<string, object>();

                // Update user fields if they changed
                if (HasUserChanges())
                {
                    // Only add changed fields to Entra update
                    if (LoggedInUser.UserName != User_UserName)
                    {
                        updateFields["displayName"] = User_UserName;
                        LoggedInUser.UserName = User_UserName;
                    }

                    if (LoggedInUser.FirstName != User_FirstName)
                    {
                        updateFields["givenName"] = User_FirstName;
                        LoggedInUser.FirstName = User_FirstName;
                    }

                    if (LoggedInUser.LastName != User_LastName)
                    {
                        updateFields["surname"] = User_LastName;
                        LoggedInUser.LastName = User_LastName;
                    }
                }

                // Update address fields if they changed
                if (HasAddressChanges())
                {
                    // Build street address (combine line1 and line2)
                    string newStreetAddress = User_AddressLine1;
                    if (!string.IsNullOrWhiteSpace(User_AddressLine2))
                    {
                        newStreetAddress += "\n" + User_AddressLine2;
                    }

                    // Only add changed address fields to Entra update
                    if (User_Address.AddressLine1 != User_AddressLine1 ||
                        User_Address.AddressLine2 != User_AddressLine2)
                    {
                        updateFields["streetAddress"] = newStreetAddress;
                    }

                    if (User_Address.City != User_City)
                    {
                        updateFields["city"] = User_City;
                    }

                    if (User_Address.State != User_State)
                    {
                        updateFields["state"] = User_State;
                    }

                    if (User_Address.PostalCode != User_PostalCode)
                    {
                        updateFields["postalCode"] = User_PostalCode;
                    }

                    if (User_Address.Country != User_Country)
                    {
                        updateFields["country"] = User_Country;
                    }

                    // Update local address object
                    User_Address.AddressLine1 = User_AddressLine1;
                    User_Address.AddressLine2 = User_AddressLine2;
                    User_Address.City = User_City;
                    User_Address.State = User_State;
                    User_Address.PostalCode = User_PostalCode;
                    User_Address.Country = User_Country;
                }

                var addressID = LoggedInUser.AddressId ?? Guid.NewGuid();
                await memberService.UpdateMemberByIdAsync(LoggedInUser.Id, LoggedInUser);
                bool isAddressUpdated = await AddressService.UpdateAddressAsync(addressID, User_Address);

                // Only call Graph API if there are fields to update
                if (updateFields.Count > 0)
                {
                    await GraphApiService.UpdateUserProfileAsync(User.id, updateFields);
                }

                await DialogService.ShowMessageBox(
                    title: Localizer["Success"],
                    message: Localizer["Profile Updated Successfully"]);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorUpdatingProfile"]);
                await DialogService.ShowMessageBox(
                    title: Localizer["Error"],
                    message: Localizer["Failed To Update Profile"]);
            }
        }

        // Helper method to detect user changes
        private bool HasUserChanges()
        {
            return LoggedInUser.UserName != User_UserName ||
                   LoggedInUser.FirstName != User_FirstName ||
                   LoggedInUser.LastName != User_LastName;
        }

        // Helper method to detect address changes
        private bool HasAddressChanges()
        {
            return User_Address.AddressLine1 != User_AddressLine1 ||
                   User_Address.AddressLine2 != User_AddressLine2 ||
                   User_Address.City != User_City ||
                   User_Address.State != User_State ||
                   User_Address.PostalCode != User_PostalCode ||
                   User_Address.Country != User_Country;
        }

        private async Task Cancel()
        {
            // Check if there are any unsaved changes
            bool hasChanges = HasUserChanges() || HasAddressChanges();

            if (hasChanges)
            {
                // Show confirmation dialog if changes exist
                var confirmed = await DialogService.ShowMessageBox(
                    title: Localizer["Confirm Cancel"],
                    message: Localizer["You have unsaved changes. Are you sure you want to cancel?"],
                    yesText: Localizer["Discard Changes"],
                    noText: Localizer["Continue Editing"]);

                if (confirmed != true)
                {
                    return; // User chose to continue editing
                }
            }

            // Reset the form values
            SetUserAddress(User_Address);
            SetUserDetails(LoggedInUser);
        }



        [GeneratedRegex("^[A-Za-z][A-Za-z0-9]{4,14}$")]
        private static partial Regex DisplayNameRegex();

        [GeneratedRegex("^[A-Za-z]+$")]
        private static partial Regex NameRegex();

        [GeneratedRegex("^[A-Za-z]+$")]
        private static partial Regex AlphabetRegex();

        [GeneratedRegex("^\\d+$")]
        private static partial Regex PostalCodeRegex();

        [GeneratedRegex("^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$")]
        private static partial Regex EmailRegex();

        private IEnumerable<string> DisplayNameValidation(string displayName)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(displayName))
            {
                validator.Add(Localizer["Res1UName"]);
            }
            else if (!DisplayNameRegex().IsMatch(displayName))
            {
                validator.Add(Localizer["Res2UName"]);
            }
            return validator;
        }

        private IEnumerable<string> FirstNameValidation(string name)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(name))
            {
                validator.Add(Localizer["Res1UName"]);
            }
            else if (!NameRegex().IsMatch(name))
            {
                validator.Add(Localizer["Res2UName"]);
            }
            return validator;
        }

        private IEnumerable<string> LastNameValidation(string name)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(name))
            {
                validator.Add(Localizer["Res1LName"]);
            }
            else if (!NameRegex().IsMatch(name))
            {
                validator.Add(Localizer["Res2LName"]);
            }
            return validator;
        }

        private IEnumerable<string> AlphabetValidation(string value)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(value))
            {
                validator.Add(Localizer["Res1Field"]);
            }
            else if (!AlphabetRegex().IsMatch(value))
            {
                validator.Add(Localizer["Res2Field"]);
            }
            return validator;
        }

        private IEnumerable<string> PostalCodeValidation(string postalCode)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(postalCode))
            {
                validator.Add(Localizer["Res1PCode"]);
            }
            else if (!PostalCodeRegex().IsMatch(postalCode))
            {
                validator.Add(Localizer["Res2PCode"]);
            }
            return validator;
        }

        private IEnumerable<string> EmailValidation(string email)
        {
            var validator = new List<string>();
            if (string.IsNullOrWhiteSpace(email))
            {
                validator.Add(Localizer["Res1Email"]);
            }
            else if (!EmailRegex().IsMatch(email))
            {
                validator.Add(Localizer["Res2Email"]);
            }
            return validator;
        }

        private async Task ManageProfileSubmit()
        {
            try
            {
                bool isUpdated = await AuthService.UpdateUserAsync(userProfile);
                if (isUpdated)
                {
                    userDetails[Localizer["Display_Name"]] = userProfile.DisplayName;
                    userDetails[Localizer["Given Name"]] = userProfile.GivenName;
                    userDetails[Localizer["SurName1"]] = userProfile.Surname;
                    userDetails[Localizer["Mail"]] = userProfile.Mail;
                    userDetails[Localizer["UserType"]] = userProfile.UserType;
                    userDetails[Localizer["JobTitle"]] = userProfile.JobTitle;
                    userDetails[Localizer["StreetAddress"]] = userProfile.StreetAddress;
                    userDetails[Localizer["City"]] = userProfile.City;
                    userDetails[Localizer["State"]] = userProfile.State;
                    userDetails[Localizer["PostalCode"]] = userProfile.PostalCode;
                    userDetails[Localizer["Country"]] = userProfile.Country;

                    TokenService.UserDetails = JsonSerializer.Serialize(userDetails);

                    var updatedData = new UpdatedUserViewModel
                    {
                        id = userProfile.id,
                        DisplayName = userProfile.DisplayName,
                        GivenName = userProfile.GivenName,
                        Surname = userProfile.Surname,
                        Mail = userProfile.Mail,
                        StreetAddress = userProfile.StreetAddress,
                        Country = userProfile.Country,
                        City = userProfile.City,
                        State = userProfile.State,
                        PostalCode = userProfile.PostalCode,
                        JobTitle = userProfile.JobTitle,
                        UserType = userProfile.UserType,
                    };

                    Guid userId = Guid.Parse(userProfile.id);
                    var member = new Member
                    {
                        Id = userId,
                        UserName = userProfile.DisplayName,
                        FirstName = userProfile.GivenName,
                        LastName = userProfile.Surname,
                        Email = userProfile.Mail,
                        IsActive = true,
                        Address = new Address
                        {
                            AddressLine1 = userProfile.StreetAddress,
                            PostalCode = userProfile.PostalCode,
                            State = userProfile.State,
                            Country = userProfile.Country,
                            OrganizationID = memberDetails.OrganizationID.Value,
                        },
                        Country = userProfile.Country,
                        AccessControl = userProfile.UserType,
                        AddressId = userId,
                        RoleID = memberDetails.RoleID,
                        RoleName = memberDetails.RoleName,
                        OrganizationID = memberDetails.OrganizationID,
                        OrganizationName = memberDetails.OrganizationName,
                        InsuranceId = memberDetails.InsuranceId
                    };

                    await memberService.UpdateMemberByIdAsync(member.Id, member);
                    await AuthService.UpdateAsync(updatedData.id, updatedData);

                    Logger.LogInformation(Localizer["UserProfileUpdated"]);
                    StateHasChanged();
                }
                else
                {
                    Logger.LogWarning(Localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["UpdateError"]);
            }
        }



        private async Task OnThemeChanged(ThemeItem themeItem)
        {
            if (themeItem == null || string.IsNullOrEmpty(themeItem.Name))
            {
                return;
            }
            SelectedTheme = themeItem;
            CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
            var userTheme = UsersTheme.FirstOrDefault(ut => activeUser == ut.UserId);
            try
            {
                if (userTheme != null)
                {
                    userTheme.ThemeName = themeItem.Name;
                    await UserThemeService.UpdateUserThemeAsync(userTheme);
                }
                else
                {
                    userTheme = new UserTheme
                    {
                        UserId = activeUser,
                        ThemeName = themeItem.Name,
                        OrganizationID = organizationId
                    };
                    await UserThemeService.AddUserThemeAsync(userTheme);
                    UsersTheme.Add(userTheme);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error updating theme: {ex.Message}");
                SelectedTheme = Themes.FirstOrDefault();
            }
            finally
            {
                _isThemeLoaded = true;
                StateHasChanged();
                Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
            }
        }

        private MudTheme MapToMudTheme(ThemeModel themeModel)
        {
            return new MudTheme
            {
                PaletteLight = new PaletteLight
                {
                    Primary = themeModel.PaletteLight.Primary,
                    Secondary = themeModel.PaletteLight.Secondary,
                    AppbarBackground = themeModel.PaletteLight.AppbarBackground,
                    Background = themeModel.PaletteLight.Background,
                    Surface = themeModel.PaletteLight.Surface,
                    DrawerBackground = themeModel.PaletteLight.DrawerBackground,
                    TextPrimary = themeModel.PaletteLight.TextPrimary,
                    TextSecondary = themeModel.PaletteLight.TextSecondary,
                    ActionDefault = themeModel.PaletteLight.ActionDefault,
                    ActionDisabled = themeModel.PaletteLight.ActionDisabled,
                    ActionDisabledBackground = themeModel.PaletteLight.ActionDisabledBackground
                },
                PaletteDark = new PaletteDark
                {
                    Primary = themeModel.PaletteDark.Primary,
                    Secondary = themeModel.PaletteDark.Secondary,
                    AppbarBackground = themeModel.PaletteDark.AppbarBackground,
                    Background = themeModel.PaletteDark.Background,
                    Surface = themeModel.PaletteDark.Surface,
                    DrawerBackground = themeModel.PaletteDark.DrawerBackground,
                    TextPrimary = themeModel.PaletteDark.TextPrimary,
                    TextSecondary = themeModel.PaletteDark.TextSecondary,
                    ActionDefault = themeModel.PaletteDark.ActionDefault,
                    ActionDisabled = themeModel.PaletteDark.ActionDisabled,
                    ActionDisabledBackground = themeModel.PaletteDark.ActionDisabledBackground
                },
                LayoutProperties = new LayoutProperties
                {
                    DrawerWidthLeft = themeModel.LayoutProperties.DrawerWidthLeft,
                    DrawerWidthRight = themeModel.LayoutProperties.DrawerWidthRight,
                    AppbarHeight = themeModel.LayoutProperties.AppbarHeight
                }
            };
        }

        private ThemeModel MapToThemeModel(MudTheme mudTheme)
        {
            return new ThemeModel
            {
                PaletteLight = new ThemePalette
                {
                    Primary = mudTheme.PaletteLight.Primary.Value,
                    Secondary = mudTheme.PaletteLight.Secondary.Value,
                    AppbarBackground = mudTheme.PaletteLight.AppbarBackground.Value,
                    Background = mudTheme.PaletteLight.Background.Value,
                    Surface = mudTheme.PaletteLight.Surface.Value,
                    DrawerBackground = mudTheme.PaletteLight.DrawerBackground.Value,
                    TextPrimary = mudTheme.PaletteLight.TextPrimary.Value,
                    TextSecondary = mudTheme.PaletteLight.TextSecondary.Value,
                    ActionDefault = mudTheme.PaletteLight.ActionDefault.Value,
                    ActionDisabled = mudTheme.PaletteLight.ActionDisabled.Value,
                    ActionDisabledBackground = mudTheme.PaletteLight.ActionDisabledBackground.Value
                },
                PaletteDark = new ThemePalette
                {
                    Primary = mudTheme.PaletteDark.Primary.Value,
                    Secondary = mudTheme.PaletteDark.Secondary.Value,
                    AppbarBackground = mudTheme.PaletteDark.AppbarBackground.Value,
                    Background = mudTheme.PaletteDark.Background.Value,
                    Surface = mudTheme.PaletteDark.Surface.Value,
                    DrawerBackground = mudTheme.PaletteDark.DrawerBackground.Value,
                    TextPrimary = mudTheme.PaletteDark.TextPrimary.Value,
                    TextSecondary = mudTheme.PaletteDark.TextSecondary.Value,
                    ActionDefault = mudTheme.PaletteDark.ActionDefault.Value,
                    ActionDisabled = mudTheme.PaletteDark.ActionDisabled.Value,
                    ActionDisabledBackground = mudTheme.PaletteDark.ActionDisabledBackground.Value
                },
                LayoutProperties = new ThemeLayoutProperties
                {
                    DrawerWidthLeft = mudTheme.LayoutProperties.DrawerWidthLeft,
                    DrawerWidthRight = mudTheme.LayoutProperties.DrawerWidthRight,
                    AppbarHeight = mudTheme.LayoutProperties.AppbarHeight
                }
            };
        }



        /// <summary>
        /// Validates the Alphabetic input only alphabetic characters and spaces are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAlphabetic(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetter(c) || c == ' '))
                {
                    result = "Invalid input. Only alphabetic characters and spaces are allowed.";
                }
            }

            return result;
        }   /// <summary>
            /// Validates the Postal Code 
            /// </summary>
            /// <param name="value"></param>
            /// <returns></returns>

        private string? ValidatePostalCode(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = value.Replace("-", "");

                if (!Regex.IsMatch(digitsOnly, @"^\d{5}(\d{4})?$"))
                {
                    result = Localizer["InvalidPostalCodeMessage"];
                }
                else if (digitsOnly.Length == 9)
                {

                    User_PostalCode = $"{digitsOnly.Substring(0, 5)}-{digitsOnly.Substring(5)}";
                   
                }
                else if (digitsOnly.Length == 5)
                {
                    // Accept as-is 
                }
                else
                {
                    result = Localizer["InvalidPostalCodeFormat"];
                }
            }

            return result;
        }




        /// <summary>
        /// Validates the Address input only letters, numbers, spaces, commas, periods, and hyphens are allowed
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string? ValidateAddress(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!Regex.IsMatch(value, @"^[a-zA-Z0-9\s,.\-+/\\]+$"))
                {
                    result = Localizer["InvalidAddressMessage"];
                }
            }

            return result;
        }
    // Validate username
      
        private string ValidateUsername(string username)
        {
            string pattern = @"^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$";
            bool isValid = Regex.IsMatch(username, pattern);

            if (!isValid)
            {
                return Localizer["InvalidUserName"];
            }

            return null;
        }

        //Search Country for country dropdown
        private async Task<IEnumerable<string>> SearchCountries(string value, CancellationToken cancellationToken)
        {
            if (_allCountries.Count == 0)
            {
                try
                {
                    _allCountries = (await countryService.GetAllCountriesAsync()).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching countries: {ex.Message}");
                    return new List<string>();
                }
            }

            if (string.IsNullOrWhiteSpace(value))
            {
                return _allCountries
                    .Select(c => c.CountryName)
                    .OrderBy(name => name)
                    .ToList();
            }
            else
            {
                return _allCountries
                    .Select(c => c.CountryName)
                    .Where(name => name.Contains(value, StringComparison.OrdinalIgnoreCase))
                    .OrderBy(name => name)
                    .ToList();
            }
        }


        private string? ValidateFirstName(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetter(c) || c == ' ' || c == '.'))
                {
                    result = "Invalid input. Only alphabetic characters and spaces are allowed.";
                }
            }

            return result;
        }


    }
}