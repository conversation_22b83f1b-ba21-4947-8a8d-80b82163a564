﻿@page "/Flowsheets"
@using Microsoft.Extensions.Localization
@using TeyaWebApp.Components.Layout
@layout Admin
@using TeyaUIModels.Model
@using Syncfusion.Blazor.Grids
@using MudBlazor.Utilities
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Charts
@using Syncfusion.Blazor
@using System.Linq.Expressions
@using MudBlazor
@inject IDiagnosticImagingService DiagnosticImagingService
@inject IVitalService VitalService
@inject IFamilyMemberService FamilyMemberService
@inject ICurrentMedicationService CurrentMedicationService
@inject IMedicalHistoryService MedicalHistoryService
@inject IAllergyService AllergyService

<div class="py-2">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pl-0 pt-0 pb-3 pr-3">
        <MudGrid Spacing="2">
            <MudItem xs="12" >
                <MudPaper Class="pa-4" Style="height: 100vh; overflow-y: auto;">
                    <MudGrid Spacing="2">
                        <MudItem xs="12">
                            <MudGrid Spacing="2">
                                <MudItem xs="12">
                                    <MudItem xs="12">
                                        <div class="d-flex justify-content-between align-items-center w-100 mb-2">

                                            <div>
                                                <SfDropDownList TValue="string" TItem="string"
                                                                Placeholder="Select a Flowsheet"
                                                                DataSource="@FlowsheetList.Select(f => f.FlowsheetName).ToList()"
                                                                @bind-Value="@_selectedFlowsheet"
                                                                AllowFiltering="true"
                                                                Width="240px">
                                                    <DropDownListEvents TValue="string" TItem="string" OnValueSelect="@OnFlowsheetSelected" />
                                                </SfDropDownList>
                                            </div>

                                            <div>
                                                <MudButton Variant="Variant.Filled"
                                                           Color="Color.Primary"
                                                           Class="mx-1"
                                                           OnClick="AddNewFlowsheet"
                                                           StartIcon="@Icons.Material.Filled.Add"
                                                           Style="margin-bottom: 5px; height: 33px;">
                                                    New Flowsheet
                                                </MudButton>
                                            </div>

                                        </div>
                                    </MudItem>

                                </MudItem>
                                @if(_selectedFlowsheet != null){
                                    <MudItem xs="12" >
                                         <div class="d-flex justify-content-between align-items-center w-100 mb-2">
                                            @if (selectedView == "Graph")
                                            {
                                                    <div>
                                                    <MudButton Color="Color.Primary"
                                                               Disabled="@_isExporting"
                                                               Variant="Variant.Filled"
                                                               Size="MudBlazor.Size.Small"
                                                               Class="me-2"
                                                               Style="height:35px; width:180px;"
                                                               OnClick="ExportAllToExcel">
                                                        @if (_isExporting)
                                                        {
                                                            <MudProgressCircular Indeterminate="true"
                                                                                 Color="Color.Inherit"
                                                                                 Size="MudBlazor.Size.Small"
                                                                                 Class="me-2" />
                                                            <span>Exporting...</span>
                                                        }
                                                        else
                                                        {
                                                            <MudIcon Icon="@Icons.Material.Filled.FileDownload" />
                                                            <span>Export to Excel</span>
                                                        }
                                                    </MudButton>
                                                    <MudButton Color="Color.Error"
                                                               Variant="Variant.Filled"
                                                               Size="MudBlazor.Size.Small"
                                                               Style="height:35px; width:100px;"
                                                               OnClick="OnDeleteClicked">
                                                        <MudIcon Icon="@Icons.Material.Filled.Delete" />
                                                        <span>Delete</span>
                                                    </MudButton>
                                                    </div>
                                            }
                                            else
                                            {
                                                <div></div>
                                            }
                                                <div>
                                                <MudButtonGroup Color="Color.Primary" Variant="Variant.Outlined">
                                                    <MudButton StartIcon="@Icons.Material.Filled.Edit"
                                                               Color="@((selectedView == "Edit") ? Color.Primary : Color.Default)"
                                                               OnClick="@(() => OnEditViewClicked())">
                                                        Edit View
                                                    </MudButton>

                                                    <MudButton StartIcon="@Icons.Material.Filled.BarChart"
                                                    Color="@((selectedView == "Graph") ? Color.Primary : Color.Default)"
                                                    OnClick="@(() => selectedView = "Graph")">
                                                        Graph View
                                                    </MudButton>
                                                    <MudButton StartIcon="@Icons.Material.Filled.TableChart"
                                                    Color="@((selectedView == "Grid") ? Color.Primary : Color.Default)"
                                                    OnClick="@(() => selectedView = "Grid")">
                                                        Grid View
                                                    </MudButton>
                                                </MudButtonGroup>
                                            </div>
                                        </div>
                                    </MudItem>
                                    @if (selectedView == "Graph")
                                    {

                                        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">


                                            <MudGrid>
                                                @foreach (var Data in flowsheetData)
                                                {
                                                    @if (!string.IsNullOrWhiteSpace(Data.Component?.Name))
                                                    {
                                                        @if (Data.Component?.Name == "Vitals")
                                                        {
                                                            var elements = Data.Component.Element?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries) ?? Array.Empty<string>();
                                                            <MudItem xs="12">
                                                                <MudPaper Class="pa-2" Style="background-color: #f9f9f9;">
                                                                    <MudStack Spacing="1">
                                                                        <MudText Typo="Typo.h5"
                                                                        Color="Color.Primary"
                                                                        Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                            @Localizer["Vitals"]
                                                                        </MudText>

                                                                        @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                                        {
                                                                            <MudDivider Class="my-1" />
                                                                            <MudText Typo="Typo.subtitle1"
                                                                            Class="ml-1"
                                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                @Data.Heading
                                                                            </MudText>
                                                                            <MudDivider Class="my-1" />
                                                                        }
                                                                    </MudStack>
                                                                    <MudGrid Spacing="2">

                                                                        @foreach (var ele in elements)
                                                                        {
                                                                            @if (ele.ToLower() == "temperature")
                                                                            {
                                                                                <MudItem xs="4">
                                                                                    <div id="TemperatureChart">
                                                                                        <SfChart @ref="TemperatureChart" width="240px" Height="200px" SubTitle="Temperature">
                                                                                            <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                                                            LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                                                                            </ChartPrimaryXAxis>
                                                                                            <ChartPrimaryYAxis Title="@Localizer["Temperature"]">
                                                                                                <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                            </ChartPrimaryYAxis>
                                                                                            <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Temperature: ${point.y}" />
                                                                                            <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                            <ChartSeriesCollection>
                                                                                                <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Temperature" Type="ChartSeriesType.Line" Name="Temperature">
                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                            </ChartSeriesCollection>
                                                                                        </SfChart>
                                                                                    </div>
                                                                                </MudItem>
                                                                            }
                                                                            @if (ele.ToLower() == "height")
                                                                            {
                                                                                <MudItem xs="4">
                                                                                    <div id="HeightChart">
                                                                                        <SfChart @ref="HeightChart" Width="240px" Height="200px">
                                                                                            <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                                                            LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                                                                            </ChartPrimaryXAxis>
                                                                                            <ChartPrimaryYAxis Title="Height"><ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle></ChartPrimaryYAxis>
                                                                                            <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                            <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Height: ${point.y}" />
                                                                                            <ChartSeriesCollection>
                                                                                                <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Height"
                                                                                                Type="ChartSeriesType.Line" Name="Height">

                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />

                                                                                                </ChartSeries>
                                                                                            </ChartSeriesCollection>
                                                                                        </SfChart>
                                                                                    </div>
                                                                                </MudItem>
                                                                            }
                                                                            @if (ele.ToLower() == "pulse")
                                                                            {
                                                                                <MudItem xs="4">
                                                                                    <div id="PulseChart">
                                                                                        <SfChart @ref="PulseChart" width="240px" Height="200px">
                                                                                            <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                                                            LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                                                                            </ChartPrimaryXAxis>
                                                                                            <ChartPrimaryYAxis Title="@Localizer["Pulse"]">
                                                                                                <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle> 
                                                                                            </ChartPrimaryYAxis>
                                                                                            <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                            <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Pulse: ${point.y}" />
                                                                                            <ChartSeriesCollection>
                                                                                                <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Pulse" Type="ChartSeriesType.Line" Name="Pulse">
                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                            </ChartSeriesCollection>
                                                                                        </SfChart>
                                                                                    </div>
                                                                                </MudItem>
                                                                            }
                                                                            @if (ele.ToLower() == "weight")
                                                                            {
                                                                                <MudItem xs="4">
                                                                                    <div id="WeightChart">
                                                                                        <SfChart @ref="WeightChart" width="240px" Height="200px">
                                                                                            <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                                                            LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                                                                            </ChartPrimaryXAxis>
                                                                                            <ChartPrimaryYAxis Title="@Localizer["Weight"]">
                                                                                                <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                            </ChartPrimaryYAxis>
                                                                                            <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                            <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>Weight: ${point.y}" />
                                                                                            <ChartSeriesCollection>
                                                                                                <ChartSeries DataSource="@patientVitals" XName="CreatedDate" YName="Weight" Type="ChartSeriesType.Line" Name="Weight">
                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                            </ChartSeriesCollection>
                                                                                        </SfChart>
                                                                                    </div>
                                                                                </MudItem>
                                                                            }
                                                                            @if (ele.ToLower() == "blood pressure")
                                                                            {
                                                                                <MudItem xs="4">
                                                                                    <div id="BloodPressureChart">
                                                                                        <SfChart @ref="BloodPressureChart" Width="240px" Height="200px">
                                                                                            <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.DateTime"
                                                                                            LabelFormat="MMM dd" IntervalType="IntervalType.Hours">
                                                                                            </ChartPrimaryXAxis>
                                                                                            <ChartPrimaryYAxis Title="@Localizer["Blood Pressure"]">
                                                                                                <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                            </ChartPrimaryYAxis>
                                                                                            <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                            <ChartTooltipSettings Duration="200" Enable="true" Format="Date: ${point.x}<br/>BP: ${point.y}" />
                                                                                            <ChartSeriesCollection>
                                                                                                <ChartSeries DataSource="@formattedVitals" XName="CreatedDate" YName="Systolic" Type="ChartSeriesType.Line" Name="Systolic">
                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                                <ChartSeries DataSource="@formattedVitals" XName="CreatedDate" YName="Diastolic" Type="ChartSeriesType.Line" Name="Diastolic">
                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                            </ChartSeriesCollection>
                                                                                        </SfChart>
                                                                                    </div>
                                                                                </MudItem>
                                                                            }
                                                                        }
                                                                    </MudGrid>
                                                                </MudPaper>
                                                            </MudItem>
                                                        }
                                                        else{
                                                            @if (Data.Component.Name == "Medication")
                                                            {

                                                                <MudItem xs="6">
                                                                    <MudPaper Class="pa-1" Style="background-color: #f9f9f9;">
                                                                        <MudStack Spacing="1">
                                                                            <MudText Typo="Typo.h5"
                                                                            Color="Color.Primary"
                                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                @Localizer["Medication"]
                                                                            </MudText>

                                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                                            {
                                                                                <MudDivider Class="my-1" />
                                                                                <MudText Typo="Typo.subtitle1"
                                                                                Class="ml-1"
                                                                                Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                    @Data.Heading
                                                                                </MudText>
                                                                                <MudDivider Class="my-1" />
                                                                            }
                                                                        </MudStack>
                                                                        <MudGrid Spacing="2" Class="pa-1">
                                                                            <MudItem xs="12">
                                                                                <div id="MedicationChart" style="display: flex; justify-content: center; align-items: center;">
                                                                                    <SfChart @ref="MedicationChart" Width="240px" Height="200px">
                                                                                        <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category"
                                                                                        LabelRotation="45"
                                                                                        EdgeLabelPlacement="EdgeLabelPlacement.Shift">
                                                                                        </ChartPrimaryXAxis>

                                                                                        <ChartPrimaryYAxis Title="@Localizer["Medication"]">
                                                                                            <ChartAxisTitleStyle FontFamily="@fontFamily" />
                                                                                        </ChartPrimaryYAxis>
                                                                                        <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                        <ChartTooltipSettings Enable="true" Format="Date: ${point.x}" />

                                                                                        <ChartSeriesCollection>
                                                                                            @foreach (var group in medicationLine.GroupBy(m => m.Y))
                                                                                            {
                                                                                                var name = group.FirstOrDefault()?.name;
                                                                                                <ChartSeries DataSource="@group.ToList()"
                                                                                                XName="date"

                                                                                                YName="Y"
                                                                                                Type="ChartSeriesType.Line"
                                                                                                Name="@name">
                                                                                                    <ChartMarker Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                            }
                                                                                        </ChartSeriesCollection>
                                                                                    </SfChart>
                                                                                </div>
                                                                            </MudItem>
                                                                        </MudGrid>
                                                                    </MudPaper>
                                                                </MudItem>
                                                            }
                                                            @if (Data.Component.Name == "Labs")
                                                            {
                                                                <MudItem xs="6">
                                                                    <MudPaper Class="pa-1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;background-color: #f9f9f9;">
                                                                        <MudStack Spacing="1">
                                                                            <MudText Typo="Typo.h5"
                                                                            Color="Color.Primary"
                                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                @Localizer["Lab Test"]
                                                                            </MudText>

                                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                                            {
                                                                                <MudDivider Class="my-1" />
                                                                                <MudText Typo="Typo.subtitle1"
                                                                                Class="ml-1"
                                                                                Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                    @Data.Heading
                                                                                </MudText>
                                                                                <MudDivider Class="my-1" />
                                                                            }
                                                                        </MudStack>
                                                                        <MudGrid Spacing="2" Class="pa-1">

                                                                            <MudItem xs="12">
                                                                                <div id="LabChart" style="display: flex; justify-content: center; align-items: center;">
                                                                                    <SfChart @ref="LabChart" Width="240px" Height="200px">
                                                                                        <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category"
                                                                                        LabelRotation="45"
                                                                                        EdgeLabelPlacement="EdgeLabelPlacement.Shift">
                                                                                        </ChartPrimaryXAxis>
                                                                                        <ChartPrimaryYAxis Title="@Localizer["Lab Test"]">
                                                                                            <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                        </ChartPrimaryYAxis>
                                                                                        <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                        <ChartTooltipSettings Enable="true" Format="Date: ${point.x}" />

                                                                                        <ChartSeriesCollection>
                                                                                            @{
                                                                                                foreach (var group in LabTestLine.GroupBy(l => l.name))
                                                                                                {
                                                                                                    <ChartSeries DataSource="@group.ToList()"
                                                                                                    XName="date"
                                                                                                    YName="Y"
                                                                                                    Name="@group.Key"
                                                                                                    Type="ChartSeriesType.Line">
                                                                                                        <ChartMarker Shape="ChartShape.Circle" Visible="true" Height="10" Width="10" />
                                                                                                    </ChartSeries>
                                                                                                }
                                                                                            }
                                                                                        </ChartSeriesCollection>

                                                                                    </SfChart>
                                                                                </div>
                                                                            </MudItem>
                                                                        </MudGrid>
                                                                    </MudPaper>
                                                                </MudItem>
                                                            }
                                                            @if (Data.Component.Name == "Immunization")
                                                            {
                                                                <MudItem xs="6">
                                                                    <MudPaper Class="pa-1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;background-color: #f9f9f9;">
                                                                        <MudStack Spacing="1">
                                                                            <MudText Typo="Typo.h5"
                                                                            Color="Color.Primary"
                                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                @Localizer["Immunization"]
                                                                            </MudText>

                                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                                            {
                                                                                <MudDivider Class="my-1" />
                                                                                <MudText Typo="Typo.subtitle1"
                                                                                Class="ml-1"
                                                                                Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                    @Data.Heading
                                                                                </MudText>
                                                                                <MudDivider Class="my-1" />
                                                                            }
                                                                        </MudStack>
                                                                        <MudGrid Spacing="2" Class="pa-1">

                                                                            <MudItem xs="12">
                                                                                <div id="ImmunizationChart" style="display: flex; justify-content: center; align-items: center;">
                                                                                    <SfChart @ref="ImmunizationChart" Width="240px" Height="200px">
                                                                                        <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category"
                                                                                        LabelRotation="45"
                                                                                        EdgeLabelPlacement="EdgeLabelPlacement.Shift">
                                                                                        </ChartPrimaryXAxis>
                                                                                        <ChartPrimaryYAxis Title="@Localizer["Immunization Test"]">
                                                                                            <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                        </ChartPrimaryYAxis>
                                                                                        <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                        <ChartTooltipSettings Enable="true" Format="Date: ${point.x}" />

                                                                                        <ChartSeriesCollection>
                                                                                            @{
                                                                                                foreach (var group in ImmunizationTestLine.GroupBy(l => l.name))
                                                                                                {
                                                                                                    <ChartSeries DataSource="@group.ToList()"
                                                                                                    XName="date"
                                                                                                    YName="Y"
                                                                                                    Name="@group.Key"
                                                                                                    Type="ChartSeriesType.Line">
                                                                                                        <ChartMarker Shape="ChartShape.Circle" Visible="true" Height="10" Width="10" />
                                                                                                    </ChartSeries>
                                                                                                }
                                                                                            }
                                                                                        </ChartSeriesCollection>

                                                                                    </SfChart>
                                                                                    </div>
                                                                            </MudItem>
                                                                        </MudGrid>
                                                                    </MudPaper>
                                                                </MudItem>
                                                            }
                                                            @if (Data.Component.Name == "DI")
                                                            {
                                                                <MudItem xs="6">
                                                                    <MudPaper Class="pa-1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;background-color: #f9f9f9;">
                                                                        <MudStack Spacing="1">
                                                                            <MudText Typo="Typo.h5"
                                                                            Color="Color.Primary"
                                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                @Localizer["Diagnostic Imaging"]
                                                                            </MudText>

                                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                                            {
                                                                                <MudDivider Class="my-1" />
                                                                                <MudText Typo="Typo.subtitle1"
                                                                                Class="ml-1"
                                                                                Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                    @Data.Heading
                                                                                </MudText>
                                                                                <MudDivider Class="my-1" />
                                                                            }
                                                                        </MudStack>
                                                                        <MudGrid Spacing="2" Class="pa-1">

                                                                            <MudItem xs="12">
                                                                                <div id="DIChart" style="display: flex; justify-content: center; align-items: center;">
                                                                                    <SfChart @ref="DIChart" Width="240px" Height="200px">
                                                                                        <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category"
                                                                                        LabelRotation="45"
                                                                                        EdgeLabelPlacement="EdgeLabelPlacement.Shift">
                                                                                        </ChartPrimaryXAxis>
                                                                                        <ChartPrimaryYAxis Title="@Localizer["DI Test"]">
                                                                                            <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                        </ChartPrimaryYAxis>
                                                                                        <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                        <ChartTooltipSettings Enable="true" Format="Date: ${point.x}" />

                                                                                        <ChartSeriesCollection>
                                                                                            @{
                                                                                                foreach (var group in DITestLine.GroupBy(l => l.name))
                                                                                                {
                                                                                                    <ChartSeries DataSource="@group.ToList()"
                                                                                                    XName="date"
                                                                                                    YName="Y"
                                                                                                    Name="@group.Key"
                                                                                                    Type="ChartSeriesType.Line">
                                                                                                        <ChartMarker Shape="ChartShape.Circle" Visible="true" Height="10" Width="10" />
                                                                                                    </ChartSeries>
                                                                                                }
                                                                                            }
                                                                                        </ChartSeriesCollection>


                                                                                    </SfChart>
                                                                                </div>
                                                                            </MudItem>
                                                                        </MudGrid>
                                                                    </MudPaper>
                                                                </MudItem>
                                                            }
                                                            @if (Data.Component.Name == "Procedure")
                                                            {
                                                                <MudItem xs="6">
                                                                    <MudPaper Class="pa-1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;background-color: #f9f9f9;">
                                                                        <MudStack Spacing="1">
                                                                            <MudText Typo="Typo.h5"
                                                                            Color="Color.Primary"
                                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                @Localizer["Procedure"]
                                                                            </MudText>

                                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                                            {
                                                                                <MudDivider Class="my-1" />
                                                                                <MudText Typo="Typo.subtitle1"
                                                                                Class="ml-1"
                                                                                Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                                    @Data.Heading
                                                                                </MudText>
                                                                                <MudDivider Class="my-1" />
                                                                            }
                                                                        </MudStack>

                                                                        <MudGrid Spacing="2" Class="pa-1"> 
                                                                             <MudItem xs="12">
                                                                            <div id="ProcedureChart" style="display: flex; justify-content: center; align-items: center;">
                                                                                <SfChart @ref="ProcedureChart" Width="240px" Height="200px">
                                                                                    <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category"
                                                                                    LabelRotation="45"
                                                                                    EdgeLabelPlacement="EdgeLabelPlacement.Shift">
                                                                                    </ChartPrimaryXAxis>
                                                                                    <ChartPrimaryYAxis Title="@Localizer["Procedure Test"]">
                                                                                        <ChartAxisTitleStyle FontFamily="@fontFamily"></ChartAxisTitleStyle>
                                                                                    </ChartPrimaryYAxis>
                                                                                    <ChartLegendSettings Visible="false"></ChartLegendSettings>
                                                                                    <ChartTooltipSettings Enable="true" Format="Date: ${point.x}" />

                                                                                    <ChartSeriesCollection>
                                                                                        @{
                                                                                            foreach (var group in ProcedureTestLine.GroupBy(l => l.name))
                                                                                            {
                                                                                                <ChartSeries DataSource="@group.ToList()"
                                                                                                XName="date"
                                                                                                YName="Y"
                                                                                                Name="@group.Key"
                                                                                                Type="ChartSeriesType.Line">
                                                                                                    <ChartMarker Shape="ChartShape.Circle" Visible="true" Height="10" Width="10" />
                                                                                                </ChartSeries>
                                                                                            }
                                                                                        }
                                                                                    </ChartSeriesCollection>
                                                                                </SfChart>
                                                                            </div>
                                                                            </MudItem>
                                                                        </MudGrid>
                                                                    </MudPaper>
                                                                </MudItem>
                                                            }

                                                        }
                                                    }
                                                }
                                            </MudGrid>
                                        </MudContainer>
                                    }
                                    else if (selectedView == "Edit")
                                    {
                                        <MudGrid>
                                            <MudItem xs="12">
                                                <MudPaper Class="pa-2" Style="min-height: 400px; background-color: #f0f4f8;">
                                                    <MudDropContainer T="DropItem"
                                                                      @ref="_container"
                                                                      Items="AvailableItems"
                                                                      ItemDropped="ItemUpdated"
                                                                      ItemsSelector="@((item, dropzone) => dropzone == "Drop Zone 1")"
                                                                      Class="d-flex flex-wrap flex-grow-1">
                                                        <ChildContent>
                                                            <MudGrid>
                                                                <MudItem xs="3">
                                                                    <div style="height: 400px; overflow-y: auto;">
                                                                        <MudDropZone T="DropItem"
                                                                                     Identifier="Drop Zone 1"
                                                                                     CanDrop="@(args => false)"
                                                                                     Class="@($"dropzone dropzone-left {(IsDropDisabled ? "disabled-dropzone" : "")}")"
                                                                                     DraggingClass="dropzone-hover"
                                                                                     ItemDisabled="@(item => IsDropDisabled)"
                                                                                     Style="min-height: 400px;">
                                                                            <MudText Class="mb-4 text-primary font-weight-bold">Components</MudText>
                                                                        </MudDropZone>
                                                                    </div>
                                                                </MudItem>
                                                                <MudItem xs="9">
                                                                    <div style="height: 400px; overflow-y: auto;">
                                                                        <MudDropZone T="DropItem"
                                                                                     Identifier="Drop Zone 2"
                                                                                     Class="dropzone dropzone-right"
                                                                                     DraggingClass="dropzone-hover"
                                                                                     Style="min-height: 400px;">
                                                                            <MudText Class="mb-4 text-success font-weight-bold" Color="Color.Primary">Configured Flowsheet</MudText>
                                                                            @if (!DroppedItems.Any(x => x.Identifier == "Drop Zone 2") && !flowsheetData.Any())
                                                                            {
                                                                                <MudPaper Class="pa-4 ma-2 mud-background-default d-flex flex-column align-items-center justify-center" Style="height: 100px; border: 1px dashed #ccc;">
                                                                                    <MudIcon Icon="@Icons.Material.Filled.DragIndicator" Color="Color.Primary" Size="MudBlazor.Size.Large" Class="mb-2" />
                                                                                    <MudText Typo="Typo.body1" Color="Color.Secondary">Drag components from the left panel to build your flowsheet</MudText>
                                                                                </MudPaper>
                                                                            }
                                                                            else
                                                                            {
                                                                                @if (flowsheetData.Any())
                                                                                {
                                                                                    @foreach (var sections in flowsheetData)
                                                                                    {
                                                                                        <MudPaper Class="pa-2 my-2" Elevation="1">
                                                                                            @if (!sections.IsEditing)
                                                                                            {
                                                                                                @if (!string.IsNullOrWhiteSpace(sections.Heading))
                                                                                                {
                                                                                                    <MudText Color="Color.Primary"><b>Heading</b></MudText>
                                                                                                    <MudText Typo="Typo.body1">@sections.Heading</MudText>
                                                                                                }
                                                                                                else
                                                                                                {
                                                                                                    <MudText Color="Color.Primary"><b>Heading</b></MudText>
                                                                                                }

                                                                                                @if (!string.IsNullOrWhiteSpace(sections.Component?.Name))
                                                                                                {
                                                                                                     if(currentFlowsheet.IsEditable)
                                                                                                     {
                                                                                                        <MudStack Row="true" AlignItems="AlignItems.Center" JustifyContent="SpaceBetween">
                                                                                                            <MudText Typo="Typo.h6">@sections.Component.Name</MudText>
                                                                                                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                                                                                           Color="Color.Primary"
                                                                                                                           OnClick="@(() => StartEdit(sections))" />
                                                                                                        </MudStack>
                                                                                                    }
                                                                                                    @if (!string.IsNullOrWhiteSpace(sections.Component.Element))
                                                                                                    {
                                                                                                        @foreach (var el in sections.Component.Element.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries))
                                                                                                        {
                                                                                                            <MudText>• @el</MudText>
                                                                                                        }
                                                                                                    }
                                                                                                }
                                                                                                @if (sections.FreeText != null && sections.FreeText.Any())
                                                                                                {
                                                                                                    <MudText Class="mt-2"><b>Comments</b></MudText>
                                                                                                    @foreach (var el in sections.FreeText)
                                                                                                    {
                                                                                                        <MudText>• @el</MudText>
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                                <!-- EDIT MODE -->
                                                                                                @RenderEditSection(sections)

                                                                                                <MudStack Row="true" Class="mt-2">
                                                                                                    <MudButton Variant="Variant.Outlined" OnClick="@(() =>  { sections.IsEditing = false ; IsDropDisabled=false;})">Cancel</MudButton>
                                                                                                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(() => SaveSection(sections))">Save</MudButton>
                                                                                                </MudStack>
                                                                                            }
                                                                                        </MudPaper>
                                                                                    }
                                                                                }

                                                                                @foreach (var item in DroppedItems.Where(x => x.Identifier == "Drop Zone 2"))
                                                                                {
                                                                                    @RenderDroppedComponent(item)
                                                                                }
                                                                            }
                                                                        </MudDropZone>
                                                                    </div>
                                                                </MudItem>
                                                            </MudGrid>
                                                        </ChildContent>
                                                        <ItemRenderer>
                                                            <MudPaper Class="dragging-item pa-3 my-2">
                                                                <MudText Typo="Typo.body1">@context.Name</MudText>
                                                            </MudPaper>
                                                        </ItemRenderer>
                                                    </MudDropContainer>
                                                </MudPaper>
                                            </MudItem>
                                        </MudGrid>
                                         @if(currentFlowsheet.IsEditable)
                                            {
                                            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;width:100%">
                                                    <MudButton Color="Color.Secondary"
                                                               Variant="Variant.Outlined"
                                                               OnClick="LockChanges"
                                                               Dense="true"
                                                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                                                        @Localizer["Lock"]
                                                    </MudButton>
                                                <MudButton Color="Color.Secondary"
                                                           Variant="Variant.Outlined"
                                                           OnClick="CancelChanges"
                                                           Dense="true"
                                                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                                                    @Localizer["Cancel"]
                                                </MudButton>
                                                <MudButton Color="Color.Primary"
                                                           Variant="Variant.Filled"
                                                           OnClick="UpdateFlowsheet"
                                                           Dense="true"
                                                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                                                    @Localizer["Save"]
                                                </MudButton>
                                           
                                            </div>
                                            }
                                    }
                                    else
                                    {
                                        @foreach (var Data in flowsheetData)
                                        {
                                            @if (!string.IsNullOrWhiteSpace(Data.Component?.Name))
                                            {
                                                @if (Data.Component?.Name == "Vitals")
                                                {
                                                    <MudPaper Class="mb-4 ml-2" Style="padding: 10px; overflow-x: hidden; background-color: #f9f9f9;">
                                                        <MudStack Spacing="1">
                                                            <MudText Typo="Typo.h5"
                                                            Color="Color.Primary"
                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                @Localizer["Vitals"]
                                                            </MudText>

                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                            {
                                                                <MudDivider Class="my-1" />
                                                                <MudPaper Class="pa-3" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Data.Heading</MudPaper>
                                                            }
                                                        </MudStack>
                                                        <SfGrid @ref="VitalsGrid"
                                                        TValue="PatientVitals"
                                                        Style="font-size: 0.85rem; margin-top: 24px;"
                                                        DataSource="@patientVitals"
                                                        AllowPaging="true"
                                                        GridLines="GridLine.Both"
                                                        PageSettings-PageSize="5">
                                                            <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                            <GridPageSettings PageSize="10"></GridPageSettings>
                                                            <GridEvents TValue="PatientVitals"></GridEvents>
                                                            <GridColumns>
                                                                <GridColumn Field="VitalId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                                <GridColumn Field="CreatedDate" HeaderText="@Localizer["Date"]" Width="100"
                                                                TextAlign="TextAlign.Center" Format="MM/dd/yy">
                                                                    <EditTemplate>
                                                                        <SfDatePicker @bind-Value="@((context as PatientVitals).CreatedDate)"
                                                                        Max="@DateTime.Now"
                                                                        Placeholder="Select a date"
                                                                        ShowClearButton="false">
                                                                        </SfDatePicker>
                                                                    </EditTemplate>
                                                                </GridColumn>
                                                                <GridColumn Field="Temperature" HeaderText="@Localizer["Temperature"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>

                                                                <GridColumn Field="BP" HeaderText="@Localizer["BloodPressure"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                                                                <GridColumn Field="Pulse" HeaderText="@Localizer["Pulse"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                                                                <GridColumn Field="Weight" HeaderText="@Localizer["Weight"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                                                                <GridColumn Field="Height" HeaderText="@Localizer["Height"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudPaper>
                                                }
                                                @if (Data.Component?.Name == "Medication")
                                                {
                                                    <MudPaper Class="ml-2 mb-4" Style="padding: 10px; overflow-x: hidden; background-color: #f9f9f9;">
                                                        <MudStack Spacing="1">
                                                            <MudText Typo="Typo.h5"
                                                            Color="Color.Primary"
                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                @Localizer["Medication"]
                                                            </MudText>

                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                            {
                                                                <MudDivider Class="my-1" />
                                                                <MudPaper Class="pa-3" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Data.Heading</MudPaper>
                                                            }
                                                        </MudStack>
                                                        <SfGrid @ref="MedicinesGrid"
                                                        TValue="ActiveMedication"
                                                        DataSource="@activeMedications"
                                                        AllowPaging="true"
                                                        PageSettings-PageSize="5"
                                                        GridLines="GridLine.Both"
                                                                Style="font-size: 0.85rem;margin-top: 24px;">
                                                            <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="true" Mode="EditMode.Normal" />
                                                            <GridPageSettings PageSize="5" />
                                                            <GridColumns>
                                                                <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false" />
                                                                <GridColumn Field="BrandName" ClipMode="ClipMode.EllipsisWithTooltip" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="120" />
                                                                <GridColumn Field="DrugDetails" ClipMode="ClipMode.EllipsisWithTooltip" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200" />
                                                                <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1" />
                                                                <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120" />
                                                                <GridColumn Field="Route" HeaderText="@Localizer["Route"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="Take" HeaderText="@Localizer["Take"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="Strength" HeaderText="@Localizer["Strength"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" Format="MM/dd/y" TextAlign="TextAlign.Center" Width="100" />
                                                                <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" Format="MM/dd/y" TextAlign="TextAlign.Center" Width="100" />
                                                                <GridColumn Field="@nameof(ActiveMedication.CheifComplaint)" HeaderText="@Localizer["Chief Complaint"]" TextAlign="TextAlign.Center" Width="200" EditTemplate="@ChiefComplaintEditTemplate" />
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudPaper>
                                                }
                                                @if (Data.Component?.Name == "Immunization")
                                                {
                                                    <MudPaper Class="ml-2 mb-4" Style="padding: 10px; overflow-x: hidden; background-color: #f9f9f9;">
                                                        <MudStack Spacing="1">
                                                            <MudText Typo="Typo.h5"
                                                            Color="Color.Primary"
                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                @Localizer["Immunization"]
                                                            </MudText>

                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                            {
                                                                <MudDivider Class="my-1" />
                                                                <MudPaper Class="pa-3" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Data.Heading</MudPaper>
                                                            }
                                                        </MudStack>
                                                        <SfGrid TValue="ImmunizationData" Style="font-size: 0.85rem; margin-top: 24px; width: 100%;"
                                                        DataSource="@immunization" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                                                            <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                            <GridPageSettings PageSize="10"></GridPageSettings>
                                                            <GridColumns>
                                                                <GridColumn Field="ImmunizationId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                                <GridColumn Field="GivenDate" HeaderText="@Localizer["Date"]" Width="100" Format="MM/dd/y" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                                                <GridColumn Field="Immunizations" HeaderText="@Localizer["Vaccine"]" Width="200" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="CVXCode" HeaderText="@Localizer["CVX Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="Comments" HeaderText="@Localizer["Comments"]" Width="180" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudPaper>
                                                }
                                                @if (Data.Component?.Name == "Labs")
                                                {
                                                    <MudPaper Class="mb-4 ml-2" Style="padding: 10px; overflow-x: hidden; background-color: #f9f9f9;">
                                                        <MudStack Spacing="1">
                                                            <MudText Typo="Typo.h5"
                                                            Color="Color.Primary"
                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                @Localizer["Lab Test"]
                                                            </MudText>

                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                            {
                                                                <MudDivider Class="my-1" />
                                                                <MudPaper Class="pa-3" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Data.Heading</MudPaper>
                                                            }
                                                        </MudStack>
                                                        <SfGrid @ref="PlanLabsGrid" TValue="LabTests" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@planlabs" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                                                            <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                            <GridPageSettings PageSize="10"></GridPageSettings>
                                                            <GridEvents TValue="LabTests"></GridEvents>
                                                            <GridColumns>
                                                                <GridColumn Field="LabTestsId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                                <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Width="100" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                                                                <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Udated Date"]" Width="100" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                                                                <GridColumn Field="LabTest1" HeaderText="@Localizer["Primary Test"]" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                                                                <GridColumn Field="TestOrganization" HeaderText="@Localizer["Organization"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"
                                                                EditTemplate="@OrganizationEditTemplate"></GridColumn>
                                                                <GridColumn Field="@nameof(LabTests.AssessmentData)"
                                                                HeaderText="@Localizer["RelatedAssessment"]"
                                                                TextAlign="TextAlign.Center"
                                                                Width="150"
                                                                EditTemplate="@AssessmentEditTemplate">
                                                                </GridColumn>
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudPaper>
                                                }
                                                @if (Data.Component?.Name == "DI")
                                                {
                                                    <MudPaper Class="mb-4 ml-2" Style="padding: 10px; overflow-x: hidden; background-color: #f9f9f9;">
                                                        <MudStack Spacing="1">
                                                            <MudText Typo="Typo.h5"
                                                            Color="Color.Primary"
                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                @Localizer["Diagnostic Imaging"]
                                                            </MudText>

                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                            {
                                                                <MudDivider Class="my-1" />
                                                                <MudPaper Class="pa-3" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Data.Heading</MudPaper>
                                                            }
                                                        </MudStack>
                                                        <SfGrid @ref="futureImagingGrid" TValue="DiagnosticImage" Style="font-size: 0.85rem;margin-top: 24px;" DataSource="@DiagnosticImagingList" AllowPaging="true" GridLines="GridLine.Both">
                                                            <GridEditSettings AllowDeleting="true" AllowAdding="true" AllowEditing="false" Mode="EditMode.Normal"></GridEditSettings>
                                                            <GridPageSettings PageSize="5"></GridPageSettings>
                                                            <GridColumns>
                                                                <GridColumn Field="RecordID" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                                <GridColumn Field="DiCompany" HeaderText="DI Company" Width="150" TextAlign="TextAlign.Left"></GridColumn>
                                                                <GridColumn Field="Type" HeaderText="Type" Width="120" TextAlign="TextAlign.Left"></GridColumn>
                                                                <GridColumn Field="Lookup" HeaderText="Lookup" Width="120" TextAlign="TextAlign.Left"></GridColumn>
                                                                <GridColumn Field="OrderName" HeaderText="Order Name" Width="150" TextAlign="TextAlign.Left"></GridColumn>
                                                                <GridColumn Field="StartsWith" HeaderText="Starts With" Width="120" TextAlign="TextAlign.Left"></GridColumn>
                                                                <GridColumn Field="ccResults" HeaderText="CC Results To" Width="150" TextAlign="TextAlign.Left"></GridColumn>
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudPaper>
                                                }
                                                @if (Data.Component?.Name == "Procedure")
                                                {
                                                    <MudPaper Class="mb-4 ml-2" Style="padding: 10px; overflow-x: hidden; background-color: #f9f9f9;">
                                                        <MudStack Spacing="1">
                                                            <MudText Typo="Typo.h5"
                                                            Color="Color.Primary"
                                                            Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
                                                                @Localizer["Procedure"]
                                                            </MudText>

                                                            @if (!string.IsNullOrWhiteSpace(Data?.Heading))
                                                            {
                                                                <MudDivider Class="my-1" />
                                                                <MudPaper Class="pa-3" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Data.Heading</MudPaper>
                                                            }
                                                        </MudStack>

                                                        <SfGrid @ref="ProcedureGrid" TValue="Procedures" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@procedure" AllowPaging="true" PageSettings-PageSize="5" AllowEditing="true" GridLines="GridLine.Both">
                                                            <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                            <GridPageSettings PageSize="10"></GridPageSettings>
                                                            <GridEvents TValue="Procedures"></GridEvents>
                                                            <GridColumns>
                                                                <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                                                                <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="Description" HeaderText="@Localizer["Description"]" TextAlign="TextAlign.Center" Width="120" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                                                                <GridColumn Field="@nameof(Procedures.AssessmentData)"
                                                                HeaderText="@Localizer["Related Assessment"]"
                                                                TextAlign="TextAlign.Center"
                                                                Width="150"
                                                                EditTemplate="@AssessmentEditTemplate">
                                                                </GridColumn>
                                                                <GridColumn Field="@nameof(Procedures.ChiefComplaint)"
                                                                HeaderText="@Localizer["Chief Complaint"]"
                                                                TextAlign="TextAlign.Center"
                                                                Width="150"
                                                                EditTemplate="@ChiefComplaintEditTemplate">
                                                                </GridColumn>
                                                                <GridColumn Field="OrderedBy" HeaderText="@Localizer["Ordered By"]" TextAlign="TextAlign.Center" Width="100" AllowEditing="false"></GridColumn>
                                                                <GridColumn Field="OrderDate" HeaderText="@Localizer["Order Date"]" Width="100" TextAlign="TextAlign.Center" Format="MM/dd/yy"></GridColumn>
                                                                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                                                                    <GridCommandColumns>
                                                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                                                    </GridCommandColumns>
                                                                </GridColumn>
                                                            </GridColumns>
                                                        </SfGrid>
                                                    </MudPaper>
                                                }
                                            }
                                        }
                                    }
                                }
                                else{

                                    <MudGrid> 
                                        <MudItem xs="12">
                                            <MudPaper Class="pa-2" Style="min-height: 400px; background-color: #f9f9f9;">
                                                <MudGrid Class="mb-4">
                                                    <MudItem xs="6">
                                                        <MudGrid>
                                                            <MudItem xs="6" Class="d-flex justify-content-end align-items-center">
                                                                <MudText Class="font-weight-bold" Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Enter Flowsheet Name :</MudText>
                                                            </MudItem>

                                                            <MudItem xs="6">
                                                                <MudTextField @bind-Value="FlowsheetRecord.FlowsheetName"
                                                                              Variant="Variant.Outlined"
                                                                              Immediate="true"
                                                                              Placeholder="Enter flowsheet name"
                                                                              Margin="Margin.Dense"
                                                                              Style="width: 100%; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; background-color:white;" />
                                                            </MudItem>
                                                        </MudGrid>
                                                    </MudItem>

                                                </MudGrid>

                                                <MudDropContainer T="DropItem"
                                                @ref="_container"
                                                Items="AvailableItems"
                                                ItemDropped="ItemUpdated"
                                                ItemsSelector="@((item, dropzone) => dropzone == "Drop Zone 1")"
                                                Class="d-flex flex-wrap flex-grow-1">
                                                    <ChildContent>
                                                        <MudGrid>
                                                            <MudItem xs="3">
                                                                <div style="height: 400px; overflow-y: auto;">
                                                                    <MudDropZone T="DropItem"
                                                                    Identifier="Drop Zone 1"
                                                                    CanDrop="@(args => false)"
                                                                    Class="@($"dropzone dropzone-left {(IsDropDisabled ? "disabled-dropzone" : "")}")"
                                                                    DraggingClass="dropzone-hover"
                                                                    ItemDisabled="@(item => IsDropDisabled)"
                                                                    Style="min-height: 400px;">
                                                                        <MudText Class="mb-4 text-primary font-weight-bold">Components</MudText>
                                                                    </MudDropZone>
                                                                </div>
                                                            </MudItem>
                                                            <MudItem xs="9">
                                                                <div style="height: 400px; overflow-y: auto;">
                                                                    <MudDropZone T="DropItem"
                                                                    Identifier="Drop Zone 2"
                                                                    Class="dropzone dropzone-right"
                                                                    DraggingClass="dropzone-hover"
                                                                    Style="min-height: 400px;">
                                                                        <MudText Class="mb-4 font-weight-bold" Color="Color.Primary">Configured Flowsheet</MudText>
                                                                        @if (!DroppedItems.Any(x => x.Identifier == "Drop Zone 2") && !flowsheetData.Any())
                                                                        {
                                                                            <MudPaper Class="pa-4 ma-2 mud-background-default d-flex flex-column align-items-center justify-center" Style="height: 100px; border: 1px dashed #ccc;">
                                                                                <MudIcon Icon="@Icons.Material.Filled.DragIndicator" Color="Color.Primary" Size="MudBlazor.Size.Large" Class="mb-2" />
                                                                                <MudText Typo="Typo.body1" Color="Color.Secondary">Drag components from the left panel to build your flowsheet</MudText>
                                                                            </MudPaper>
                                                                        }
                                                                        else
                                                                        {
                                                                            @if (flowsheetData.Any())
                                                                            {
                                                                                @foreach (var sections in flowsheetData)
                                                                                {
                                                                                    <MudPaper Class="pa-2 my-2" Elevation="1">
                                                                                @if (!sections.IsEditing)
                                                                                {
                                                                                        @if (!string.IsNullOrWhiteSpace(sections.Heading))
                                                                                        {
                                                                                            <MudText Color="Color.Primary"><b>Heading</b></MudText>
                                                                                            <MudText Typo="Typo.body1">@sections.Heading</MudText>
                                                                                        }
                                                                                        else
                                                                                        {
                                                                                            <MudText Color="Color.Primary"><b>Heading</b></MudText>
                                                                                        }

                                                                                        @if (!string.IsNullOrWhiteSpace(sections.Component?.Name))
                                                                                        {
                                                                                            <MudStack Row="true" AlignItems="AlignItems.Center" JustifyContent="SpaceBetween">
                                                                                                <MudText Typo="Typo.h6">@sections.Component.Name</MudText>
                                                                                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                                                                               Color="Color.Primary"
                                                                                                               OnClick="@(() => StartEdit(sections))" />
                                                                                            </MudStack>

                                                                                            @if (!string.IsNullOrWhiteSpace(sections.Component.Element))
                                                                                            {
                                                                                                @foreach (var el in sections.Component.Element.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries))
                                                                                                {
                                                                                                    <MudText>• @el</MudText>
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                        @if (sections.FreeText != null && sections.FreeText.Any())
                                                                                        {
                                                                                            <MudText Class="mt-2"><b>Comments</b></MudText>
                                                                                            @foreach (var el in sections.FreeText)
                                                                                            {
                                                                                                <MudText>• @el</MudText>
                                                                                            }
                                                                                        }
                                                                                }
                                                                                        else
                                                                                       {
                                                                                            <!-- EDIT MODE -->
                                                                                            @RenderEditSection(sections)

                                                                                            <MudStack Row="true" Class="mt-2"> 
                                                                                                <MudButton Variant="Variant.Outlined" OnClick="@(() =>  { sections.IsEditing = false ; IsDropDisabled=false;})">Cancel</MudButton> 
                                                                                                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(() => SaveSection(sections))">Save</MudButton> 
                                                                                             </MudStack>
                                                                                        }
                                                                                    </MudPaper>
                                                                                }
                                                                            }

                                                                            @foreach (var item in DroppedItems.Where(x => x.Identifier == "Drop Zone 2"))
                                                                            {
                                                                                @RenderDroppedComponent(item)
                                                                            }
                                                                        }
                                                                    </MudDropZone>
                                                                </div>
                                                            </MudItem>
                                                        </MudGrid>
                                                    </ChildContent>
                                                    <ItemRenderer>
                                                        <MudPaper Class="dragging-item pa-3 my-2">
                                                            <MudText Typo="Typo.body1">@context.Name</MudText>
                                                        </MudPaper>
                                                    </ItemRenderer>
                                                </MudDropContainer>
                                            </MudPaper>
                                        </MudItem>
                                    </MudGrid>

                                    <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;width:100%">
                                        <MudButton Color="Color.Secondary"
                                        Variant="Variant.Outlined"
                                        OnClick="CancelChanges"
                                        Dense="true"
                                        Style="min-width: 120px; height: 40px; font-weight: 600;">
                                            @Localizer["Cancel"]
                                        </MudButton>
                                        <MudButton Color="Color.Primary"
                                        Variant="Variant.Filled"
                                        OnClick="SaveChanges"
                                        Dense="true"
                                        Style="min-width: 120px; height: 40px; font-weight: 600;">
                                            @Localizer["Save"]
                                        </MudButton>
                                    </div>
                                }

                            </MudGrid>
                        </MudItem>
                    </MudGrid>


                </MudPaper>
            </MudItem>

        </MudGrid>
    </MudContainer>

</div>


<style>

    .mud-selected-item {
        background-color: #E0E0E0; /* Light grey background */
        font-weight: 600;
        border-left: 4px solid #1976d2; /* Primary color indicator */
    }

    .custom-select .mud-input-slot {
        padding: 10px 8px !important; /* top-bottom, left-right */
    }

    .dropzone {
        background-color: white;
        border: 1px solid #d0e1ec;
        border-radius: 12px;
        padding: 16px;
        transition: background-color 0.3s, border 0.3s;
    }

    .disabled-dropzone .mud-paper,
    .disabled-dropzone .dragging-item {
        opacity: 0.5;
        pointer-events: none;
        filter: grayscale(100%);
        cursor: not-allowed;
    }

    .dropzone-hover {
        background-color: #d0ebff;
    }

    .dragging-item {
        background-color: #ffffff;
        color: var(--mud-palette-primary, #1976d2);
        font-weight: 500;
        border-left: 4px solid var(--mud-palette-primary, #1976d2);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        cursor: grab;
        transition: all 0.2s ease-in-out;
    }

    .text-primary {
        color: var(--mud-palette-primary, #1976d2);
        font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }

    .text-success {
        color: var(--mud-palette-primary, #1976d2);
        font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }

    .font-weight-bold {
        font-weight: 600;
    }

</style>

<script>
    window.downloadFileFromBytes = (fileName, base64) => {
        const link = document.createElement('a');
        link.download = fileName;
        link.href = "data:text/csv;base64," + base64;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

        window.getChartBase64 = async function (elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.warn("Element not found: " + elementId);
            return null;
        }

        try {
            const canvas = await html2canvas(element);
            return canvas.toDataURL("image/png");
        } catch (err) {
            console.error("Error capturing chart:", err);
            return null;
        }
    };

</script>
