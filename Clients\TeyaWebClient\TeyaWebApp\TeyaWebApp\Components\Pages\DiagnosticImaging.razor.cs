﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Http.HttpResults;
using TeyaWebApp.Components.Layout;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using System.Text.RegularExpressions;
using Syncfusion.Blazor;

namespace TeyaWebApp.Components.Pages
{
    public partial class DiagnosticImaging : ComponentBase
    {
        private MudDialog _diagnosticImagingDialog;
        private SfGrid<DiagnosticImage> futureImagingGrid { get; set; }
        private List<DiagnosticImage> DiagnosticImagingList { get; set; }
        private SfGrid<DiagnosticImage> todayImagingGrid { get; set; }
        private List<DiagnosticImage> FutureImagingList { get; set; }
        private DiagnosticImage newDiagnosticImaging = new();
        private bool isDialogOpen = false;
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
      
        private Guid? organizationId { get; set; }
        private static readonly char[] SplitChars = { ' ', ',', '-', '(', ')', '/' };
        private Patient _PatientData = new Patient();

        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IAlertService AlertService { get; set; }
        [Inject] private IDiagnosticImagingAlertService DiagnosticImagingAlertService { get; set; }
        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private ILogger<DiagnosticImaging> _logger { get; set; }
        [Inject] private IMemberService MemberService { get; set; }
        [Inject] private UserContext UserContext { get; set; }

        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] IDialogService DialogService { get; set; }
        public Guid ID { get; set; }
        public Guid patientID { get; set; }
        public Guid orderSetId { get; set; }
        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }

        public string? ManualContent {  get; set; }



        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            ManualContent = Data;
            organizationId = OrgId;
            patientID = PatientID;

            Subscription = UserContext.ActiveUserSubscription;
            DiagnosticImagingList = (await DiagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(patientID, organizationId, Subscription))
            .OrderBy(DiagnosticImagingRecord => DiagnosticImagingRecord.CreatedDate)
            .ToList();
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
            // Start Phase 2 in background without awaiting
            _ = LoadDIDataAsync();
        }

        private async Task LoadDIDataAsync()
        {
            var patient = await MemberService.GetMemberByIdAsync(patientID, organizationId ?? Guid.Empty, false);
            _PatientData.DOB = patient.DateOfBirth;
            _PatientData.Sex = patient.SexualOrientation;
            _PatientData.Name = patient.FirstName;
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Update to RichTextEditor
        /// </summary>
        private void ViewHandler()
        {
            string str = "";

            foreach (var member in DiagnosticImagingList)
            {
                str += $"<li><b>{member.Type}:</b> {member.CreatedDate}</li>";
            }

            str += "</ul>";

            editorContent = str;
        }

        private async Task SaveChanges()
        {

            bool? saveResult = await DialogService.ShowMessageBox(
                Localizer["ConfirmSave"],
                Localizer["SaveMessage"],
                yesText: Localizer["Yes"],
                noText: Localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }

            if (string.IsNullOrWhiteSpace(newDiagnosticImaging.DiCompany) ||
               string.IsNullOrWhiteSpace(newDiagnosticImaging.Type) ||
               string.IsNullOrWhiteSpace(newDiagnosticImaging.Lookup) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.OrderName) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.ccResults) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.StartsWith))
            {
                SnackBar.Add("Please enter all required fields.", Severity.Warning);
                return;
            }

            newDiagnosticImaging.PatientId = patientID;
            newDiagnosticImaging.OrganizationID = organizationId;
            newDiagnosticImaging.CreatedDate = DateTime.Now;
            newDiagnosticImaging.UpdatedDate = DateTime.Now;
            newDiagnosticImaging.CreatedBy = Guid.Parse(User.id);
            newDiagnosticImaging.UpdatedBy = patientID;
            newDiagnosticImaging.RecordID = Guid.NewGuid();
            newDiagnosticImaging.IsActive = true;

            // Create a list for alert checking
            var alertsCheckList = new List<DiagnosticImage> { newDiagnosticImaging };

            await DiagnosticImagingService.CreateDiagnosticImagingAsync(new List<DiagnosticImage> { newDiagnosticImaging }, organizationId, Subscription);
            DiagnosticImagingList = await DiagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(patientID, organizationId, Subscription);



            //ViewHandler();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            // Check for alerts after saving
            // await CheckDiagnosticImagingForAlerts(alertsCheckList);
            CloseDialog();
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);

        }

        private async Task CheckDiagnosticImagingForAlerts(List<DiagnosticImage> imagingToCheck)
        {
            if (imagingToCheck == null || !imagingToCheck.Any())
                return;

            int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string patientGender = _PatientData.Sex ?? "Unknown";
            var alertsToAdd = new List<Alert>();

            // Check custom DI alerts first
            var customDIAlerts = await GetCustomDIAlertsForImaging(imagingToCheck);

            if (customDIAlerts.Count > 0)
            {
                foreach (var customAlert in customDIAlerts)
                {
                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = patientID,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = organizationId ?? Guid.Empty,
                        Severity = "Not Configured",
                        AlertType = "Configured DI Alert",
                        Description = customAlert.Description ?? $"Custom alert for {customAlert.Name}",
                        Solution = $"Follow the guidelines for {customAlert.Name}. See reference: {customAlert.WebReference}",
                        AdditionalInfo = $"Order Set: {customAlert.OrderSet}, Age Range: {customAlert.AgeLowerBound}-{customAlert.AgeUpperBound}, Gender: {customAlert.Gender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    SnackBar.Add($"[CONFIGURED ALERT] {customAlert.Name} - {customAlert.Description}",
                                Severity.Info,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.MedicalServices;
                                });
                }
            }

            // Check AI-suggested alerts for diagnostic imaging appropriateness
            foreach (var imaging in imagingToCheck)
            {
                if (string.IsNullOrEmpty(imaging.Type))
                    continue;

                bool isAppropriate = await CheckImagingAppropriateForPatient(imaging.Type, patientAge, patientGender);

                if (!isAppropriate)
                {
                    string severityResponse = await GetImagingSeverity(imaging.Type, patientAge, patientGender);
                    string severityLevel = ExtractSeverityLevel(severityResponse);

                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = patientID,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = organizationId ?? Guid.Empty,
                        Severity = severityLevel,
                        AlertType = "AI Suggested DI Alert",
                        Description = $"The Diagnostic Imaging '{imaging.Type}' may not be appropriate for a {patientAge}-year-old {patientGender} patient.",
                        Solution = $"Consider reviewing the imaging order or consulting with a specialist. {severityResponse}",
                        AdditionalInfo = $"Imaging Type: {imaging.Type}, Patient Age: {patientAge}, Gender: {patientGender}, Company: {imaging.DiCompany}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    var snackbarSeverity = severityLevel switch
                    {
                        "High" => Severity.Error,
                        "Medium" => Severity.Warning,
                        "Low" => Severity.Info,
                        _ => Severity.Warning
                    };

                    SnackBar.Add($"[AI SUGGESTED] ({patientAge}y, {patientGender}): The Diagnostic Imaging {imaging.Type} may not be appropriate. Severity: {severityLevel}",
                                snackbarSeverity,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.SmartToy;
                                });
                }
            }

            if (alertsToAdd.Count > 0)
            {
                try
                {
                    await AlertService.AddAlertsAsync(alertsToAdd, organizationId, false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error adding diagnostic imaging alerts");
                }
            }
        }

        private async Task<List<DiagnosticImagingAlerts>> GetCustomDIAlertsForImaging(List<DiagnosticImage> imagingToCheck)
        {
            try
            {
                var customDIAlerts = await DiagnosticImagingAlertService.GetAllByIdAndIsActiveAsync(ID, organizationId, false);
                if (customDIAlerts == null || customDIAlerts.Count == 0)
                    return new List<DiagnosticImagingAlerts>();

                int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
                string patientGender = _PatientData.Sex ?? "Unknown";

                var matchingAlerts = new List<DiagnosticImagingAlerts>();
                var imagingTypes = imagingToCheck.Select(i => i.Type).Where(t => !string.IsNullOrEmpty(t)).ToList();

                if (imagingTypes.Count == 0)
                    return matchingAlerts;

                // Use AI to match alerts with imaging types
                StringBuilder alertsDescription = new StringBuilder();
                for (int i = 0; i < customDIAlerts.Count; i++)
                {
                    var alert = customDIAlerts[i];
                    alertsDescription.AppendLine($"Alert {i + 1}:");
                    alertsDescription.AppendLine($"- Name: {alert.Name}");
                    alertsDescription.AppendLine($"- Description: {alert.Description}");
                    alertsDescription.AppendLine($"- Order Set: {alert.OrderSet}");
                    alertsDescription.AppendLine($"- Age Range: {(alert.AgeLowerBound.HasValue ? alert.AgeLowerBound.Value.ToString() : "Any")} to {(alert.AgeUpperBound.HasValue ? alert.AgeUpperBound.Value.ToString() : "Any")}");
                    alertsDescription.AppendLine($"- Gender: {alert.Gender ?? "Any"}");
                    alertsDescription.AppendLine();
                }

                StringBuilder imagingInfo = new StringBuilder();
                foreach (var imagingType in imagingTypes)
                {
                    imagingInfo.AppendLine($"- {imagingType}");
                }

                string prompt = $"Determine which alerts apply to these diagnostic imaging types:\n\n" +
                    $"Patient: {patientAge}y, {patientGender}\n" +
                    $"Imaging Types:\n{imagingInfo}\n\n" +
                    $"Available Alerts:\n{alertsDescription}\n" +
                    $"Return comma-separated alert numbers that apply (e.g., '1,3,5'). If none apply, return 'None'.";

                string response = await AskGptModel(prompt);

                if (!response.Trim().Equals("None", StringComparison.OrdinalIgnoreCase))
                {
                    var matches = Regex.Matches(response, @"\d+");
                    foreach (Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int alertIndex) &&
                            alertIndex >= 1 &&
                            alertIndex <= customDIAlerts.Count)
                        {
                            matchingAlerts.Add(customDIAlerts[alertIndex - 1]);
                        }
                    }
                }

                // Fallback to keyword matching if AI doesn't find matches
                if (matchingAlerts.Count == 0)
                {
                    foreach (var alert in customDIAlerts)
                    {
                        bool imagingMatch = false;

                        // Direct string matching
                        if (!string.IsNullOrEmpty(alert.Name))
                        {
                            foreach (var imagingType in imagingTypes)
                            {
                                if (alert.Name.Contains(imagingType, StringComparison.OrdinalIgnoreCase) ||
                                    imagingType.Contains(alert.Name, StringComparison.OrdinalIgnoreCase))
                                {
                                    imagingMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!imagingMatch && !string.IsNullOrEmpty(alert.Description))
                        {
                            foreach (var imagingType in imagingTypes)
                            {
                                if (alert.Description.Contains(imagingType, StringComparison.OrdinalIgnoreCase) ||
                                    imagingType.Contains(alert.Description, StringComparison.OrdinalIgnoreCase))
                                {
                                    imagingMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!imagingMatch && !string.IsNullOrEmpty(alert.OrderSet))
                        {
                            foreach (var imagingType in imagingTypes)
                            {
                                if (alert.OrderSet.Contains(imagingType, StringComparison.OrdinalIgnoreCase) ||
                                    imagingType.Contains(alert.OrderSet, StringComparison.OrdinalIgnoreCase))
                                {
                                    imagingMatch = true;
                                    break;
                                }
                            }
                        }

                        // Keyword matching
                        if (!imagingMatch)
                        {
                            var imagingKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                            foreach (var imagingType in imagingTypes)
                            {
                                var words = imagingType.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        imagingKeywords.Add(word);
                                }
                            }

                            var alertKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                            if (!string.IsNullOrEmpty(alert.Name))
                            {
                                var words = alert.Name.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        alertKeywords.Add(word);
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.Description))
                            {
                                var words = alert.Description.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        alertKeywords.Add(word);
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.OrderSet))
                            {
                                var words = alert.OrderSet.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        alertKeywords.Add(word);
                                }
                            }

                            foreach (var imagingKeyword in imagingKeywords)
                            {
                                if (alertKeywords.Contains(imagingKeyword))
                                {
                                    imagingMatch = true;
                                    break;
                                }
                            }
                        }

                        if (imagingMatch)
                        {
                            // Check age constraints
                            bool ageMatch = true;
                            if (alert.AgeLowerBound.HasValue && patientAge < alert.AgeLowerBound.Value)
                                ageMatch = false;
                            if (alert.AgeUpperBound.HasValue && patientAge > alert.AgeUpperBound.Value)
                                ageMatch = false;

                            // Check gender constraints
                            bool genderMatch = true;
                            if (!string.IsNullOrEmpty(alert.Gender) && alert.Gender != "Both")
                            {
                                if (_PatientData.Sex != alert.Gender)
                                    genderMatch = false;
                            }

                            if (ageMatch && genderMatch)
                            {
                                matchingAlerts.Add(alert);
                            }
                        }
                    }
                }

                return matchingAlerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting custom DI alerts");
                return new List<DiagnosticImagingAlerts>();
            }
        }

        private async Task<bool> CheckImagingAppropriateForPatient(string imagingType, int patientAge, string patientGender)
        {
            string prompt = $"Is the diagnostic imaging '{imagingType}' appropriate for a {patientAge}-year-old {patientGender} patient? " +
                           $"Consider age-specific and gender-specific medical guidelines, radiation exposure concerns, and clinical appropriateness. " +
                           $"Answer only with 'yes' or 'no'.";

            string response = await AskGptModel(prompt);
            return response.Trim().ToLower().Contains("yes", StringComparison.OrdinalIgnoreCase);
        }

        private async Task<string> GetImagingSeverity(string imagingType, int patientAge, string patientGender)
        {
            string patientName = _PatientData.Name ?? "Unknown";

            string prompt = $"What is the severity level (Low, Medium, High) of ordering diagnostic imaging '{imagingType}' " +
                           $"for a {patientAge}-year-old {patientGender} patient named {patientName}? " +
                           $"Consider radiation exposure, age-appropriate imaging guidelines, gender-specific factors, " +
                           $"and potential contraindications. Explain briefly why.";

            string response = await AskGptModel(prompt);
            return response.Trim();
        }

        private static string ExtractSeverityLevel(string response)
        {
            response = response.ToLower();

            if (response.Contains("critical") || response.Contains("severe") || response.Contains("high"))
                return "High";
            else if (response.Contains("medium") || response.Contains("moderate"))
                return "Medium";
            else if (response.Contains("low") || response.Contains("minor"))
                return "Low";
            else
                return "Medium";
        }

        private async Task<string> AskGptModel(string prompt)
        {
            int age = _PatientData.DOB.HasValue ?
                      (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string systemMessage = $"You are a medical assistant helping to evaluate the appropriateness of diagnostic imaging for patients. " +
                                   $"The patient is {age} years old and {gender}. " +
                                   $"Consider age-appropriate imaging guidelines, radiation exposure concerns, gender-specific medical considerations, " +
                                   $"and clinical appropriateness in your evaluation. " +
                                   $"Provide concise, accurate information that takes into account the patient's demographic factors.";

            return await MeasureService.AskGptAsync(systemMessage, prompt);
        }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add", TooltipText = "Add DI" },
            new ToolbarItemModel() { Name = "close" },
        };

        /// <summary>
        /// Cancel all edit, delete and add operations and close the dialogBox
        /// </summary>
        private async Task CancelChanges()
        {
            await InvokeAsync(StateHasChanged);
            CloseDialog();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        /// <summary>
        /// Open dialog
        /// </summary>
        private void OpenDialog()
        {
            isDialogOpen = true;
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseDialog()
        {
            newDiagnosticImaging = new DiagnosticImage();
            isDialogOpen = false;
        }

        private async void CancelAction()
        {
            _diagnosticImagingDialog.CloseAsync();
            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }

        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = DiagnosticImagingList != null
                ? string.Join(" ", DiagnosticImagingList
                    .Where(m => !string.IsNullOrEmpty(m.Type))
                    .OrderByDescending(m => m.CreatedDate)
                    .Select(m => $"<ul><li style='margin-left: 20px;'><b>{m.Type}:</b> {m.CreatedDate:MM-dd-yyyy}</li></ul>"))
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }



        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}