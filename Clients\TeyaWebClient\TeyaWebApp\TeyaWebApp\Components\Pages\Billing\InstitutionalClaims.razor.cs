using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class InstitutionalClaims
    {
        [Inject] private IInstitutionalClaimsService ClaimsService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }
        [Inject] private ActiveUser _activeUser { get; set; }

        [Parameter]
        public InstitutionalClaim Model { get; set; } = new()
        {
            InsuranceItems = new List<InsuranceItem>(),
            PaymentItems = new List<PaymentItem>(),
            BillToPatient = false
        };

        [Parameter] public Guid? ClaimId { get; set; }

        private int activeTabIndex = 0;
        private Guid activeOrganizationId;
        private List<string> OtherProcedures { get; set; } = new();
        private List<InstitutionalClaim> allClaims = new();
        private bool isLoading = false;

        protected override async Task OnInitializedAsync()
        {
            activeOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(_activeUser.OrganizationName);
            await LoadClaimsData();
        }

        private async Task LoadClaimsData()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                bool subscription = GetCurrentSubscriptionStatus();

                var claims = await ClaimsService.GetAllInstitutionalClaimAsync(activeOrganizationId, subscription);
                allClaims = claims.ToList();

                if (ClaimId.HasValue && ClaimId.Value != Guid.Empty)
                {
                    var existingClaim = await ClaimsService.GetInstitutionalClaimByIdAsync(ClaimId.Value, activeOrganizationId, subscription);
                    if (existingClaim != null)
                    {
                        Model = existingClaim;
                    }
                    else
                    {
                        Snackbar.Add("Claim not found", Severity.Warning);
                        Navigation.NavigateTo("/billing/institutional-claims");
                    }
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error loading claims: {ex.Message}", Severity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task SaveClaim()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                bool subscription = GetCurrentSubscriptionStatus();

                Model.OrganizationID = activeOrganizationId;
                Model.Subscription = subscription;

                if (Model.ID == Guid.Empty)
                {
                    Model.ID = Guid.NewGuid();
                    await ClaimsService.AddInstitutionalClaimsAsync(Model, activeOrganizationId, subscription);
                    Snackbar.Add("Claim saved successfully", Severity.Success);
                }
                else
                {
                    await ClaimsService.UpdateInstitutionalClaimsAsync(Model, Model.ID, activeOrganizationId, subscription);
                    Snackbar.Add("Claim updated successfully", Severity.Success);
                }

                await LoadClaimsData();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error saving claim: {ex.Message}", Severity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task DeleteClaim()
        {
            try
            {
                if (Model.ID != Guid.Empty)
                {
                    isLoading = true;
                    StateHasChanged();

                    bool subscription = GetCurrentSubscriptionStatus();

                    await ClaimsService.DeleteInstitutionalClaimsAsync(Model.ID, activeOrganizationId, subscription);
                    Snackbar.Add("Claim deleted successfully", Severity.Success);
                    Model = new InstitutionalClaim
                    {
                        InsuranceItems = new List<InsuranceItem>(),
                        PaymentItems = new List<PaymentItem>(),
                        BillToPatient = false
                    };

                    await LoadClaimsData();
                }
                else
                {
                    Snackbar.Add("No claim selected for deletion", Severity.Warning);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Error deleting claim: {ex.Message}", Severity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task CreateNewClaim()
        {
            Model = new InstitutionalClaim
            {
                InsuranceItems = new List<InsuranceItem>(),
                PaymentItems = new List<PaymentItem>(),
                BillToPatient = false,
                OrganizationID = activeOrganizationId,
                Subscription = GetCurrentSubscriptionStatus()
            };
            activeTabIndex = 0;
            StateHasChanged();
        }

        private void GoToStep(int step)
        {
            if (step >= 0 && step <= 3)
            {
                activeTabIndex = step;
            }
        }

        private bool GetCurrentSubscriptionStatus()
        {
            return false;
        }

        // Collections
        public List<OccurrenceItem> OccurrenceItems { get; set; } = new();
        public List<ValueCodeItem> ValueCodeItems { get; set; } = new();
        public List<ServiceDetailItem> ServiceDetailItems { get; set; } = new();
        public List<ICD9CodeItem> ICD9CodeItems { get; set; } = new();
        public ICD9CodeItem SelectedICD9Code { get; set; }
        public List<LUFieldItem> LUFieldItems { get; set; } = new();

        // Grid Selection Properties
        private List<InsuranceItem> selectedInsuranceRecords { get; set; } = new List<InsuranceItem>();
        private List<PaymentItem> selectedPaymentRecords { get; set; } = new List<PaymentItem>();
        private List<ICD9CodeItem> selectedICD9Records { get; set; } = new List<ICD9CodeItem>();
        private List<LUFieldItem> selectedLURecords { get; set; } = new List<LUFieldItem>();
        private List<OccurrenceItem> selectedOccurrenceRecords { get; set; } = new List<OccurrenceItem>();
        private List<ValueCodeItem> selectedValueCodeRecords { get; set; } = new List<ValueCodeItem>();
        private List<ServiceDetailItem> selectedServiceDetailRecords { get; set; } = new List<ServiceDetailItem>();

        // Grid Selection Event Handlers
        private void OnInsuranceSelectedHandler(RowSelectEventArgs<InsuranceItem> args)
        {
            if (args.Data != null)
            {
                Model.SelectedInsurance = args.Data;
                StateHasChanged();
            }
        }

        private void OnPaymentSelectedHandler(RowSelectEventArgs<PaymentItem> args)
        {
            if (args.Data != null)
            {
                Model.SelectedPayment = args.Data;
                StateHasChanged();
            }
        }

        private void OnICD9CodeSelectedHandler(RowSelectEventArgs<ICD9CodeItem> args)
        {
            if (args.Data != null)
            {
                SelectedICD9Code = args.Data;
                StateHasChanged();
            }
        }

        private EventCallback<ICD9CodeItem> OnICD9CodeSelected => EventCallback.Factory.Create<ICD9CodeItem>(this, item =>
        {
            SelectedICD9Code = item;
            StateHasChanged();
        });

        // Action methods (implement these based on your business logic)
        private async Task AddPatientInsurance() => await Task.CompletedTask;
        private async Task AddInsurance() => await Task.CompletedTask;
        private async Task UpdateInsurance() => await Task.CompletedTask;
        private async Task RemoveInsurance() => await Task.CompletedTask;
        private async Task ViewCPTPayments() => await Task.CompletedTask;
        private async Task AddPatientPayment() => await Task.CompletedTask;
        private async Task ViewPaymentDetail() => await Task.CompletedTask;
        private async Task AddICD9Code() => await Task.CompletedTask;
        private async Task RemoveICD9Code() => await Task.CompletedTask;
        private async Task AddValueCode() => await Task.CompletedTask;
        private async Task RemoveValueCode() => await Task.CompletedTask;
        private async Task ShowValueCodeDetails() => await Task.CompletedTask;
        private async Task AddServiceDetail() => await Task.CompletedTask;
        private async Task RemoveServiceDetail() => await Task.CompletedTask;
        private async Task ShowServiceDetails() => await Task.CompletedTask;
        private async Task InsertRespParty() => await Task.CompletedTask;
        private async Task InsertPayerInfo() => await Task.CompletedTask;
    }
}