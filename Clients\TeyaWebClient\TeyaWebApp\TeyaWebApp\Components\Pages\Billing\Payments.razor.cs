using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static TeyaWebApp.Components.Pages.Encounters;

namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class Payments : ComponentBase
    {

        private List<PaymentsData> Payment;
        private List<Member> AllMembers;
        private Guid OrgID { get; set; }
        private bool Subscription { get; set; }
        private Guid? PatientID { get; set; }
        private bool IsReadOnly { get; set; } = true;
        private List<Member> ProviderMembers { get; set; }
        private string patientFilter = string.Empty;
        private string providerFilter = string.Empty;
        [Inject] private ActiveUser User { get; set; }
        private DateTime? dateFromFilter;
        private DateTime? dateToFilter;
        private string? paymentFromFilter;
        private string? postedByFilter;
        private string? checkNoFilter;
        private DateTime? checkDateFilter;
        private string? paymentNoFilter;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrgID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgID);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";
                AllMembers = await MemberService.GetAllMembersAsync(OrgID, Subscription);
                ProviderMembers = AllMembers.Where(m => m.RoleName == "Provider").ToList();
                Payment = await PaymentService.GetAllByOrgIdAsync(OrgID, Subscription);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing Encounters: {ex.Message}");
            }
        }
        private List<PaymentsData> filteredPayments => Payment?
             
             .Where(r =>
                 (string.IsNullOrEmpty(paymentFromFilter) ||
                 (r.PaymentFrom != null && r.PaymentFrom.Contains(paymentFromFilter, StringComparison.OrdinalIgnoreCase))))
             .Where(r =>
                 (string.IsNullOrEmpty(postedByFilter) ||
                 (r.PostedBy != null && r.PostedBy.Contains(postedByFilter, StringComparison.OrdinalIgnoreCase))))
             .Where(r =>
                 (string.IsNullOrEmpty(checkNoFilter) ||
                 (r.CheckNo != null && r.CheckNo.Contains(checkNoFilter, StringComparison.OrdinalIgnoreCase))))
             .Where(r =>
                 (string.IsNullOrEmpty(paymentNoFilter) ||
                 (r.PaymentNo != null && r.PaymentNo.Contains(paymentNoFilter, StringComparison.OrdinalIgnoreCase))))
             .Where(r =>
                 (!dateFromFilter.HasValue ||
                 (r.PostedDate >= dateFromFilter.Value)))
             .Where(r =>
                 (!dateToFilter.HasValue ||
                 (r.PostedDate <= dateToFilter.Value)))
             .Where(r =>
                 (!checkDateFilter.HasValue ||
                 (r.CheckDate.HasValue && r.CheckDate.Value.Date == checkDateFilter.Value.Date)))
             .ToList() ?? new List<PaymentsData>();

        private void ResetFilters()
        {
            paymentFromFilter = string.Empty;
            postedByFilter = string.Empty;
            checkNoFilter = string.Empty;
            checkDateFilter = null;
            paymentNoFilter = string.Empty;
            patientFilter = string.Empty;
            providerFilter = string.Empty;
            dateFromFilter = null;
            dateToFilter = null;
        }
    }
}

