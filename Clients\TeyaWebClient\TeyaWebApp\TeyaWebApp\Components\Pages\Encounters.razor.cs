﻿using System.Reflection;
using System.Text;
using System.Text.Json;
using Markdig;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;


namespace TeyaWebApp.Components.Pages
{
    public partial class Encounters : ComponentBase
    {

        private SfGrid<GridRecord> Grid;
        private List<Record> records;
        private Guid? OrgID { get; set; }
        private Guid? PatientID { get; set; }
        private bool IsReadOnly { get; set; } = true;
        private List<GridRecord> gridData = new List<GridRecord>();
        private Record selectedRecord;
        private bool Subscription { get; set; } = false;
        [Inject] UserContext UserContext { get; set; }

        [Inject] private ActiveUser User { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject]private IAppointmentService appointmentService { get; set; }
       
        protected override async Task OnInitializedAsync()
        {
            try
            {
                Subscription = UserContext.ActiveUserSubscription;

                if (_PatientService.PatientData != null)
                {
                    PatientID = _PatientService.PatientData.Id;
                    OrgID = _PatientService.PatientData.OrganizationID;
                    var patientId = _PatientService.PatientData.Id;
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(patientId,OrgID, Subscription);
                }
                else
                {
                    OrgID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id),OrgID, Subscription);
                }

                if (records == null || !records.Any())
                {
                    records = new List<Record>();
                    gridData = new List<GridRecord>();
                }
                else
                {
                    var pcpIds = records.Select(r => r.PCPId).Distinct().ToList();
                    var patIds = records.Select(r => r.PatientId).Distinct().ToList();

                    var allProviders = await MemberService.GetProviderlistAsObjectAsync((Guid)OrgID, Subscription);

                    var allPatients = await MemberService.GetAllActivePatientList((Guid)OrgID, Subscription);

                    var allAppointments = await appointmentService.GetAllAppointmentsAsync((Guid)OrgID, Subscription)
                        ?? new List<Appointment>();


                    var relevantProviders = allProviders
                        .Where(p => pcpIds.Contains(p.Id))
                        .ToList();

                    var relevantPatients = allPatients
                        .Where(p => patIds.Contains(p.Id))
                        .ToList();

                    var pcpMap = relevantProviders.ToDictionary(
                        p => p.Id,
                        p => $"{p.UserName}".Trim()
                    );

                    var appointmentMap = allAppointments
                        .Where(a => a != null && a.Id != Guid.Empty)
                        .ToDictionary(a => a.Id, a => a);

                    // Build a map from PatientId - MRN
                    var mrnMap = relevantPatients.ToDictionary(
                        p => p.Id,
                        p => string.IsNullOrWhiteSpace(p.MRN) ? Localizer["N/A"]: p.MRN
                    );


                    gridData = records.Select(r =>
                    {
                        Appointment? appointment = null;
                        var hasAppointment = r.AppointmentId != null && appointmentMap.TryGetValue(r.AppointmentId.Value, out appointment);

                        return new GridRecord
                        {
                            Id = r.Id,
                            AppointmentId = r.AppointmentId,
                            PatientName = r.PatientName,
                            DateTime = r.DateTime,
                            Physician = pcpMap.TryGetValue(r.PCPId, out var name) ? name : Localizer["N/A"],
                            ChiefComplaint = ExtractChiefComplaint(r.Notes),
                            Status = r.isEditable == false ? Localizer["Signed"] : Localizer["Draft"],
                            MRN = mrnMap.TryGetValue(r.PatientId, out var mrn) ? mrn : Localizer["N/A"],
                            VisitType = hasAppointment && !string.IsNullOrWhiteSpace(appointment.VisitType)
                                            ? appointment.VisitType
                                            : Localizer["N/A"],
                            Facility = hasAppointment && !string.IsNullOrWhiteSpace(appointment.Facility)
                                            ? appointment.Facility
                                            : Localizer["N/A"],
                            OriginalRecord = r
                        };
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading records: {ex.Message}");
            }
        }




        private string patientFilter = string.Empty;
        private string providerFilter = string.Empty;
        private string facilityFilter = string.Empty;
        private string visitTypeFilter = string.Empty;
        private string mrnFilter = string.Empty;
        private string statusFilter=string.Empty;

        private DateTime? selectedDate = null;

        private List<GridRecord> filteredGridData => gridData
             .Where(r => string.IsNullOrEmpty(patientFilter) ||
                         (r.PatientName?.Contains(patientFilter, StringComparison.OrdinalIgnoreCase) ?? false))
             .Where(r => string.IsNullOrEmpty(providerFilter) ||
                         (r.Physician?.Contains(providerFilter, StringComparison.OrdinalIgnoreCase) ?? false))
             .Where(r => string.IsNullOrEmpty(mrnFilter) ||
                         (r.MRN?.Contains(mrnFilter, StringComparison.OrdinalIgnoreCase) ?? false))
             .Where(r => string.IsNullOrEmpty(facilityFilter) ||
                         (r.Facility?.Contains(facilityFilter, StringComparison.OrdinalIgnoreCase) ?? false))
             .Where(r => string.IsNullOrEmpty(visitTypeFilter) ||
                         (r.VisitType?.Contains(visitTypeFilter, StringComparison.OrdinalIgnoreCase) ?? false))
             .Where(r => !selectedDate.HasValue || r.DateTime.Date == selectedDate.Value.Date)
             .Where(r => string.IsNullOrEmpty(statusFilter) ||
                         string.Equals(r.Status, statusFilter, StringComparison.OrdinalIgnoreCase))
             .ToList();




        private void ResetFilters()
        {
            patientFilter = string.Empty;
            providerFilter = string.Empty;
            facilityFilter = string.Empty;
            visitTypeFilter = string.Empty;
            mrnFilter = string.Empty;
            statusFilter = string.Empty;
            selectedDate = null;
        }
       

        /// <summary>
        /// Get the Cheif complain from the records
        /// </summary>
        /// <param name="notesJson"></param>
        /// <returns></returns>
      
        private string ExtractChiefComplaint(string notesJson)
        {
            string result = Localizer["NotAvailable"];

            try
            {
                if (string.IsNullOrEmpty(notesJson))
                    return result;

                var notes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(notesJson);

                if (notes != null &&
                    notes.TryGetValue("Subjective", out var subjectiveSection) &&
                    subjectiveSection.TryGetValue("Chief Complaint", out var htmlContent))
                {
                    result = System.Text.RegularExpressions.Regex.Replace(htmlContent, "<.*?>", string.Empty);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting chief complaint: {ex.Message}");
            }

            return result;
        }

        public class GridRecord
        {
            public Guid Id { get; set; }
            public Guid? AppointmentId { get; set; }
            public string PatientName { get; set; }
            public DateTime DateTime { get; set; }
            public string Physician { get; set; }
            public string ChiefComplaint { get; set; }
            public string Billing { get; set; }
            public string Status { get; set; }
            public string? MRN { get; set; }
            public string? VisitType { get; set; }
            public string? Facility { get; set; }
            public Record OriginalRecord { get; set; }
        }
        

        [Parameter] public EventCallback<Guid> OnRowClick { get; set; }

        private async Task OnRowSelected(RowSelectEventArgs<GridRecord> args)
        {
            Guid selectedId = args.Data.Id; // Assuming 'Id' is a Guid
            await OnRowClick.InvokeAsync(selectedId); // Send Guid to parent
        }


        private Appointment selectedAppointment=new Appointment();
        private string selectedPatientMRN=string.Empty;
        private bool showAppointmentSection = false;

        private async Task ViewAppointment(Guid? appointmentId, string? patientMRN)
        {
            try
            {
                showAppointmentSection = true; // always show the section when user clicks

                if (appointmentId != null && appointmentId != Guid.Empty)
                {
                    selectedAppointment = await appointmentService.GetAppointmentByIdAsync((Guid)appointmentId, OrgID, false);
                }
                else
                {
                    selectedAppointment = null; 
                }

                selectedPatientMRN = string.IsNullOrWhiteSpace(patientMRN)
                    ? Localizer["Not Available"]
                    : patientMRN;
            }
            catch (Exception ex)
            {
               
                selectedAppointment = null;
                showAppointmentSection = true; // still show the section with the "No info" message
            }
        }



    }
}

