﻿/* Existing Styles - Keep as is for desktop */
.search-container {
    margin-bottom: 0.75rem;
}

.close-button {
    padding: 15px 80px;
    margin: 15px;
}

.search-group {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.search-criteria, .search-input {
    flex: 1;
}

.search-criteria {
    min-width: 120px;
}

.criteria-dropdown {
    width: 100%;
}

.input-button-group {
    display: flex;
    gap: 0.5rem;
}

    .input-button-group input {
        flex: 1;
        padding: 4px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 0.85rem;
    }

.search-button {
    white-space: nowrap;
    font-size: 0.85rem;
    padding: 2px 8px;
}

/* Form scroll container */
.form-scroll-container {
    margin-top: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 12px;
}

/* Form row for side-by-side layout */
.form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

/* Compact form fields */
.form-field-compact {
    flex: 1;
    margin-bottom: 0.5rem;
}

.form-field-full {
    width: 100%;
    margin-bottom: 0.5rem;
}

    .form-field-compact label,
    .form-field-full label {
        font-weight: bold;
        margin-bottom: 0.25rem;
        color: #333;
        font-size: 0.85rem;
        display: block;
    }

/* Compact form controls */
.form-control-dropdown-compact,
.form-control-date-compact,
.form-control-time-compact {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    height: 32px;
}

.form-control-textarea-compact {
    width: 100%;
    min-height: 60px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    resize: vertical;
    font-family: inherit;
    font-size: 0.85rem;
}

    .form-control-dropdown-compact:focus,
    .form-control-date-compact:focus,
    .form-control-time-compact:focus,
    .form-control-textarea-compact:focus {
        border-color: #80bdff;
        font-size: 14px !important;
        font-weight: 600 !important;
        box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
        outline: 0;
    }

/* Compact submit button */
.submit-button-compact {
    min-width: 100px;
    font-size: 0.9rem;
    padding: 5px 15px;
}

/* Alert styles */
.alert {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 0.85rem;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Margin utilities */
.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

/* Card styling */
.fixed-card {
    width: 700px;
    height: 500px;
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 0;
    display: none;
}

    .overlay.visible {
        display: block;
    }

.appointment-form {
    padding: 15px;
}

.btn-close {
    height: 1.75em;
    width: 1.75em;
}

.appointment-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #112442;
    margin: 5px;
    text-align: center;
    width: 100%;
}

.mt-3.d-flex.justify-content-center {
    display: flex;
    justify-content: center;
}

/* Custom scrollbar styling */
.form-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.form-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.form-scroll-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

    .form-scroll-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

/* Calendar editor styling */
.custom-event-editor {
    margin: 15px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 94%;
}

.e-textlabel {
    font-weight: bold;
    margin-bottom: 5px;
}

.custom-event-editor td {
    padding: 10px;
}

.custom-event-editor input,
.custom-event-editor .e-textbox,
.custom-event-editor .e-datepicker,
.custom-event-editor .e-timepicker {
    width: 100%;
}

.e-grid .e-altrow {
    background-color: #fafafa;
}

/* Desktop Base Styles - Keep original layout */
.appointments-container {
    padding: 0.5rem;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.appointments-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    font-weight: 600;
}

/* Keep original flex layout for desktop */
.appointments-layout {
    display: flex;
    gap: 1rem;
    min-height: calc(100vh - 120px);
}

/* Desktop sidebar layout - Keep original */
.controls-section {
    width: 300px;
    padding: 0.5rem;
    flex-shrink: 0;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.schedule-container {
    flex: 1;
    min-height: 500px;
}

/* Schedule Hide Header */
.e-schedule .e-header-cells {
    display: none;
}

.schedule-cell-dimension.e-schedule .e-vertical-view .e-date-header-wrap table col,
.schedule-cell-dimension.e-schedule .e-vertical-view .e-content-wrap table col {
    width: 200px;
}

.schedule-cell-dimension.e-schedule .e-vertical-view .e-time-cells-wrap table td,
.schedule-cell-dimension.e-schedule .e-vertical-view .e-work-cells {
    height: 55px;
}

/* Enhanced Dialog and Form Styles */
.responsive-dialog {
    width: 85vw !important;
    max-width: 800px !important;
    max-height: 90vh !important;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.5rem 0;
}

.dialog-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    margin: 0 !important;
}

.dialog-close-btn {
    margin: -4px !important;
    color: #666 !important;
}

.dialog-content {
    padding: 1rem;
    max-height: 70vh;
    overflow-y: auto;
}

/* Search Section */
.search-section {
    margin-bottom: 1rem;
}

.search-row {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.search-criteria {
    min-width: 150px;
    flex-shrink: 0;
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.search-btn {
    background-color: #007bff !important;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    white-space: nowrap;
    flex-shrink: 0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

    .search-btn:hover {
        background-color: #0056b3 !important;
    }

.no-results {
    color: #666;
    font-style: italic;
    margin: 0.5rem 0;
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.grid-container {
    margin: 1rem 0;
}

/* Grid Styles */
.responsive-grid {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
}

    .responsive-grid .e-grid {
        border: 1px solid #e0e0e0;
    }

    .responsive-grid .e-headercell {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
    }

    .responsive-grid .e-row:hover {
        background-color: #e3f2fd;
    }

/* Form Styles */
.form-section {
    margin-top: 1rem;
}

.form-group {
    flex: 1;
    min-width: 0;
}

    .form-group.full-width {
        flex: none;
        width: 100%;
    }

/* Form Control Styles */
.form-control-responsive {
    width: 100% !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    padding: 0.5rem !important;
    font-size: 0.9rem !important;
    min-height: 38px !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

    .form-control-responsive:focus {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        outline: 0 !important;
    }

.form-control-dropdown-responsive {
    width: 100% !important;
}

    .form-control-dropdown-responsive .e-input-group {
        border: 1px solid #ced4da !important;
        border-radius: 4px !important;
    }

        .form-control-dropdown-responsive .e-input-group:focus-within {
            border-color: #80bdff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

.form-textarea {
    width: 100%;
    min-height: 80px;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

    .form-textarea:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: 0;
    }

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.actions-left,
.actions-right {
    display: flex;
    gap: 0.5rem;
}

.btn-delete {
    background-color: #dc3545 !important;
    color: white !important;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

    .btn-delete:hover {
        background-color: #c82333 !important;
    }

.btn-submit {
    background-color: #28a745 !important;
    color: white !important;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

    .btn-submit:hover {
        background-color: #218838 !important;
    }

.btn-new-appointment {
    background-color: #007bff !important;
    color: white !important;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

    .btn-new-appointment:hover {
        background-color: #0056b3 !important;
    }

    /* Accessibility */
    .btn-new-appointment:focus,
    .search-btn:focus,
    .btn-delete:focus,
    .btn-submit:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5) !important;
        outline: 0 !important;
    }

/* RESPONSIVE STYLES - Only apply on smaller screens */

/* Tablet and below - Start making changes */
@media (max-width: 992px) {
    .appointments-layout {
        flex-direction: column;
        gap: 1rem;
    }

    .controls-section {
        width: 100%;
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* Row 1: Calendar */
    .calendar-row {
        display: flex;
        justify-content: center;
        align-items: center;
        background: white;
        padding: 1rem;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .calendar-wrapper {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    /* Row 2: Filters */
    .filters-row {
        display: flex;
        flex-direction: row;
        gap: 1rem;
        background: white;
        padding: 1rem;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        flex: 1;
    }

    .filter-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    /* Row 3: Actions */
    .actions-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        background: white;
        padding: 1rem;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        gap: 1rem;
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .schedule-container {
        flex: 1;
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
        min-height: 500px;
    }
}

/* Mobile Styles - More aggressive changes */
@media (max-width: 768px) {
    .appointments-container {
        padding: 0.25rem;
    }

    .appointments-title {
        font-size: 1.1rem;
        padding: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .controls-section {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .calendar-row,
    .filters-row,
    .actions-row {
        padding: 0.75rem;
    }

    .filters-row {
        flex-direction: column;
        gap: 0.75rem;
    }

    .search-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .search-criteria,
    .search-input,
    .search-btn {
        width: 100%;
        min-width: auto;
    }

    .form-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .actions-left,
    .actions-right {
        width: 100%;
        justify-content: center;
    }

    .btn-delete,
    .btn-submit {
        width: 100%;
        text-align: center;
    }

    .schedule-container {
        padding: 0.5rem;
        min-height: 400px;
    }

    .responsive-dialog {
        width: 95vw !important;
        max-width: none !important;
        margin: 0.5rem !important;
    }

    .dialog-content {
        padding: 0.75rem;
    }

    .grid-container {
        margin: 0.5rem 0;
    }

    .responsive-grid .e-grid {
        font-size: 0.85rem;
    }
}

/* Extra Small Mobile */
@media (max-width: 576px) {
    .appointments-container {
        padding: 0.125rem;
    }

    .controls-section {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .calendar-row,
    .filters-row,
    .actions-row {
        padding: 0.5rem;
    }

    .btn-new-appointment {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .responsive-dialog {
        width: 98vw !important;
        height: 95vh !important;
        margin: 1vh !important;
    }

    .dialog-content {
        padding: 0.5rem;
        max-height: calc(95vh - 80px);
    }

    .form-textarea {
        min-height: 60px;
    }
}

/* Print Styles */
@media print {
    .controls-section,
    .actions-row,
    .form-actions {
        display: none;
    }

    .appointments-layout {
        flex-direction: column;
    }

    .main-content {
        width: 100%;
    }

    .schedule-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}