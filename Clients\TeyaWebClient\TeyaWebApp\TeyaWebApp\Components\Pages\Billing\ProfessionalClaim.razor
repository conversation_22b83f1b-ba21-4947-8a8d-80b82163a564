﻿@page "/ProfessionalClaims"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "ProfessionalClaimsAccessPolicy")]
@using TeyaWebApp.Components.Layout
@layout Admin
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.DropDowns
@using System.Net.Http
@using System.Net.Http.Json
@using TeyaWebApp.Services
@using TeyaWebApp.TeyaAIScribeResources
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using static TeyaUIModels.Model.ProfessionalClaims
@inject IUserLicenseService UserLicenseService
@inject IInsuranceService InsuranceService
@inject IPlanTypeService PlanTypeService
@inject IMemberService MemberService
@* @inject IProfessionalClaimsService ProfessionalClaimsService *@
@inject HttpClient Http
@inject IDialogService DialogService

<div class="dental-claim-form">
    <div class="form-header">
        <h1 class="form-title">Professional Claim Form</h1>
        <div class="header-accent"></div>
    </div>

    <div class="claim-header-card">
        <div class="form-group">
            <label class="form-label">Claim Number</label>
            <SfTextBox @bind-Value="@ProfessionalClaims.ClaimNumber" Enabled="false" CssClass="form-control-compact" Width="180px"></SfTextBox>
        </div>
        <div class="form-group">
            <label class="form-label">Claim Date</label>
            <SfDatePicker @bind-Value="@ProfessionalClaims.ClaimDate" Format="MM-dd-yyyy" Width="180px"></SfDatePicker>
        </div>
        <div class="form-group">
            <label class="form-label">Service Date</label>
            <SfDatePicker @bind-Value="@ProfessionalClaims.ServiceDate" Format="MM-dd-yyyy" Width="180px"></SfDatePicker>
        </div>
        <div class="form-group">
            <label class="form-label">Appointment Facility</label>
            <SfTextBox @bind-Value="@ProfessionalClaims.AppointmentFacility" CssClass="form-control-compact" Width="180px"></SfTextBox>
        </div>
        <div class="form-group">
            <label class="form-label">POS</label>
            <SfTextBox @bind-Value="@ProfessionalClaims.POS" CssClass="form-control-compact" Width="180px"></SfTextBox>
        </div>
    </div>

    <div class="patient-info-section">
        <div class="patient-info-grid">
            <!-- Patient Info -->
            <div class="form-group patient-info-group">
                <label class="form-label">Patient Info</label>
                <SfTextBox @bind-Value="@ProfessionalClaims.PatientInfo"
                           Multiline="true"
                           CssClass="form-control patient-textarea-large"
                           Width="400px"
                           Height="150px">
                </SfTextBox>
            </div>

            <!-- Copay & Patient Uncovered Amount -->
            <div class="financial-info-group">
                <div class="financial-labels-row">
                    <span class="financial-label">Copay</span>
                    <span class="financial-label">Pt. Uncovered Amt</span>
                </div>
                <div class="financial-inputs-row">
                    <SfNumericTextBox @bind-Value="@ProfessionalClaims.CoPay"
                                      Format="$#,##0.00"
                                      CssClass="financial-input-box"
                                      ShowSpinButton="false"
                                      Width="120px">
                    </SfNumericTextBox>
                    <SfNumericTextBox @bind-Value="@ProfessionalClaims.PatientUncoveredAmount"
                                      Format="$#,##0.00"
                                      CssClass="financial-input-box"
                                      Width="120px"
                                      Enabled="false"
                                      ShowSpinButton="false">
                    </SfNumericTextBox>
                </div>
            </div>
            <!-- Provider Information Group -->
            <div class="dropdown-info-group">
                <div class="provider-heading">Provider</div>
                <div class="form-group horizontal-group">
                    <label class="form-label">Billing</label>
                    <SfTextBox @bind-Value="@ProfessionalClaims.Billing"
                               CssClass="form-control-compact"
                               Width="180px" />
                </div>
                <div class="form-group horizontal-group">
                    <label class="form-label">Rendering</label>
                    <SfTextBox @bind-Value="@ProfessionalClaims.Rendering"
                               CssClass="form-control-compact"
                               Width="180px" />
                </div>
                <div class="form-group horizontal-group">
                    <label class="form-label">Supervisor</label>
                    <SfTextBox @bind-Value="@ProfessionalClaims.Supervisor"
                               CssClass="form-control-compact"
                               Width="180px" />
                </div>
                <div class="form-group horizontal-group">
                    <label class="form-label">Claim Status</label>
                    <SfTextBox @bind-Value="@ProfessionalClaims.ClaimStatus"
                               CssClass="form-control-compact"
                               Width="180px" />
                </div>
            </div>
        </div>
    </div>

    <!-- Main Tab Control Section -->
    <SfTab CssClass="custom-tabs">
        <TabItems>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="ICD 10 & CPT"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div style="display: flex; gap: 20px;">
                        <!-- ICD Section -->
                        <div class="icd-section" style="flex: 1;">
                            <h3 class="subsection-title">ICD-10 Codes</h3>
                            <div class="icd-container">
                                <div class="icd-search-controls">
                                    <div class="search-add-row">
                                        <MudAutocomplete T="string"
                                                         Placeholder="@Localizer["Search ICD By Codes or Description"]"
                                                         Value="currentICDSelection"
                                                         ValueChanged="OnICDNameChanged"
                                                         SearchFunc="SearchICDCodes"
                                                         ToStringFunc="@(s => s)"
                                                         CoerceText="true"
                                                         Clearable="true"
                                                         Dense="true"
                                                         ResetValueOnEmptyText="true"
                                                         Variant="Variant.Outlined"
                                                         Margin="Margin.Dense"
                                                         MinCharacters="2"
                                                         Style="flex: 1;" />
                                        <MudButton Color="Color.Primary"
                                                   OnClick="AddICDCode"
                                                   Variant="Variant.Filled"
                                                   Dense="true"
                                                   Style="min-width: 50px; height: 30px; margin-left: 0;">
                                            @Localizer["Add"]
                                        </MudButton>
                                    </div>
                                </div>

                                <!-- ICD Grid -->
                                @if (AddedICDCodes != null)
                                {
                                    <SfGrid @ref="ICDGrid"
                                            TValue="ProfessionalClaimsICD"
                                            Style="font-size: 0.85rem; margin-top: 1rem;"
                                            DataSource="@AddedICDCodes"
                                            AllowPaging="false"
                                            GridLines="GridLine.Both"
                                            AllowEditing="true"
                                            Height="120"
                                            CssClass="icd-compact-grid">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridEvents OnActionComplete="ICDActionCompletedHandler" OnActionBegin="ICDActionBeginHandler" TValue="ProfessionalClaimsICD"></GridEvents>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(ProfessionalClaimsICD.ICDId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsICD.Code)"
                                                        HeaderText="ICD Code"
                                                        Width="100"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsICD.Description)"
                                                        HeaderText="Description"
                                                        Width="300"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        TextAlign="TextAlign.Left"></GridColumn>
                                            <GridColumn HeaderText="Actions"
                                                        Width="100"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                                <GridCommandColumns>
                                                    <GridCommandColumn Type="CommandButtonType.Edit"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Delete"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Save"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                                </GridCommandColumns>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                }
                            </div>
                        </div>

                        <!-- Immunization Section -->
                        <div class="immunization-section" style="flex: 1;">
                            <h3 class="subsection-title">Immunization</h3>
                            <div class="immunization-container">
                                <div class="immunization-search-controls">
                                    <div class="search-add-row">
                                        <MudAutocomplete T="string"
                                                         Placeholder="@Localizer["Search Vaccines"]"
                                                         Value="currentVaccineSelection"
                                                         ValueChanged="OnVaccineNameChanged"
                                                         SearchFunc="SearchVaccines"
                                                         ToStringFunc="@(s => s)"
                                                         CoerceText="true"
                                                         Clearable="true"
                                                         Dense="true"
                                                         ResetValueOnEmptyText="true"
                                                         Variant="Variant.Outlined"
                                                         Margin="Margin.Dense"
                                                         MinCharacters="2"
                                                         Style="flex: 1;" />
                                        <MudButton Color="Color.Primary"
                                                   OnClick="AddVaccineCode"
                                                   Variant="Variant.Filled"
                                                   Dense="true"
                                                   Style="min-width: 50px; height: 30px; margin-left: 0;">
                                            @Localizer["Add"]
                                        </MudButton>
                                    </div>
                                </div>

                                <!-- Immunization Grid -->
                                @if (AddedVaccines != null)
                                {
                                    <SfGrid @ref="VaccineGrid"
                                            TValue="ProfessionalClaimsImmunization"
                                            Style="font-size: 0.85rem; margin-top: 1rem;"
                                            DataSource="@AddedVaccines"
                                            AllowPaging="false"
                                            GridLines="GridLine.Both"
                                            AllowEditing="true"
                                            Height="120"
                                            CssClass="immunization-compact-grid">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridEvents OnActionComplete="VaccineActionCompletedHandler" OnActionBegin="VaccineActionBeginHandler" TValue="ProfessionalClaimsImmunization"></GridEvents>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(ProfessionalClaimsImmunization.VaccineId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsImmunization.VaccineName)"
                                                        HeaderText="Vaccine Name"
                                                        Width="200"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        TextAlign="TextAlign.Left"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsImmunization.VaccineDate)"
                                                        HeaderText="Date"
                                                        Width="120"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        Format="MM/dd/yyyy"
                                                        Type="ColumnType.Date"></GridColumn>
                                            <GridColumn HeaderText="Actions"
                                                        Width="100"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                                <GridCommandColumns>
                                                    <GridCommandColumn Type="CommandButtonType.Edit"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Delete"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Save"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                                </GridCommandColumns>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- CPT Section -->
                    <div class="cpt-section">
                        <h3 class="subsection-title">CPT/HCPCS</h3>
                        <div class="cpt-container">
                            <div class="cpt-search-controls">
                                <div class="search-add-row">
                                    <MudAutocomplete T="string"
                                                     Placeholder="@Localizer["Search CPT By Codes or Description"]"
                                                     Value="currentCPTSelection"
                                                     ValueChanged="OnCPTNameChanged"
                                                     SearchFunc="SearchCPTCodes"
                                                     ToStringFunc="@(s => s)"
                                                     CoerceText="true"
                                                     Clearable="true"
                                                     Dense="true"
                                                     ResetValueOnEmptyText="true"
                                                     Variant="Variant.Outlined"
                                                     Margin="Margin.Dense"
                                                     MinCharacters="2"
                                                     Style="flex: 1;" />
                                    <MudButton Color="Color.Primary"
                                               OnClick="AddCPTCode"
                                               Variant="Variant.Filled"
                                               Dense="true"
                                               Style="min-width: 50px; height: 30px; margin-left: 0;">
                                        @Localizer["Add"]
                                    </MudButton>
                                </div>
                            </div>

                            <!-- CPT Grid -->
                            @if (AddedCPTCodes != null)
                            {
                                <SfGrid @ref="CPTGrid"
                                        TValue="ProfessionalClaimsCPT"
                                        Style="font-size: 0.85rem; margin-top: 1rem;"
                                        DataSource="@AddedCPTCodes"
                                        AllowPaging="false"
                                        GridLines="GridLine.Both"
                                        AllowEditing="true"
                                        Height="200"
                                        Width="100%"
                                        CssClass="cpt-full-width-grid">
                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                    <GridEvents OnActionComplete="CPTActionCompletedHandler" OnActionBegin="CPTActionBeginHandler" TValue="ProfessionalClaimsCPT"></GridEvents>
                                    <GridColumns>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.CPTId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.SerialNumber)"
                                                    HeaderText="S.No"
                                                    Width="50"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    AllowEditing="false"
                                                    Type="ColumnType.Number"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.IsSelected)"
                                                    HeaderText="Select"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Type="ColumnType.CheckBox"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.Code)"
                                                    HeaderText="Code"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.Description)"
                                                    HeaderText="Description"
                                                    Width="150"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    TextAlign="TextAlign.Left" AllowEditing="false"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.POS)"
                                                    HeaderText="POS"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.TOS)"
                                                    HeaderText="TOS"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.SDOS)"
                                                    HeaderText="SDOS"
                                                    Width="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="MM/dd/yyyy"
                                                    Type="ColumnType.Date"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.EDOS)"
                                                    HeaderText="EDOS"
                                                    Width="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="MM/dd/yyyy"
                                                    Type="ColumnType.Date"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.M1)"
                                                    HeaderText="M1"
                                                    Width="50"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.M2)"
                                                    HeaderText="M2"
                                                    Width="50"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.M3)"
                                                    HeaderText="M3"
                                                    Width="50"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.ICD1)"
                                                    HeaderText="ICD1"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.ICD2)"
                                                    HeaderText="ICD2"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.ICD3)"
                                                    HeaderText="ICD3"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.ICD4)"
                                                    HeaderText="ICD4"
                                                    Width="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.Units)"
                                                    HeaderText="Units"
                                                    Width="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Type="ColumnType.Number"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.BilledFee)"
                                                    HeaderText="Billed Fee"
                                                    Width="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="$#,##0.00"
                                                    Type="ColumnType.Number"></GridColumn>
                                        <GridColumn Field="@nameof(ProfessionalClaimsCPT.PcpId)"
                                                    HeaderText="PcpId"
                                                    Width="80"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn HeaderText="Actions"
                                                    Width="110"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                            <GridCommandColumns>
                                                <GridCommandColumn Type="CommandButtonType.Edit"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Delete"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Save"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                            </GridCommandColumns>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            }
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>

            <TabItem>
                <ChildContent>
                    <TabHeader Text="Insurances & Payments"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="form-section">
                        <!-- Checkbox for Bill To Patient -->
                        <div class="bill-to-patient-section" style="margin-bottom: 20px;">
                            <SfCheckBox @bind-Checked="@ProfessionalClaims.BillToPatient"
                                        Label="Bill To Patient"
                                        CssClass="bill-to-patient-checkbox">
                            </SfCheckBox>
                        </div>
                        @if (isLoading)
                        {
                            <div class="loading-container">
                                <div class="loading-spinner"></div>
                                <div class="loading-text">Loading insurance data...</div>
                            </div>
                        }
                        else
                        {
                            <div class="insurance-grid-container">
                                <SfGrid DataSource="@InsuranceData" AllowPaging="true" CssClass="custom-grid">
                                    <GridColumns>
                                        <GridColumn Field=@nameof(Insurance.IsSelected) HeaderText="Selected" DisplayAsCheckBox="true" TextAlign="TextAlign.Center" Width="80"></GridColumn>
                                        <GridColumn Field=@nameof(Insurance.PrimaryInsuranceProvider) HeaderText="Provider" Width="120"></GridColumn>
                                        <GridColumn Field=@nameof(Insurance.Subscriber) HeaderText="Subscriber" Width="150"></GridColumn>
                                        <GridColumn Field=@nameof(Insurance.SubscriberPhone) HeaderText="Subscriber N" Width="120"></GridColumn>
                                        <GridColumn Field=@nameof(Insurance.GroupNumber) HeaderText="Group No" Width="100"></GridColumn>
                                        <GridColumn Field=@nameof(Insurance.SubscriberEmployer) HeaderText="Employer Name" Width="150"></GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>

                            <!-- Payments/Adjustments/Refunds Section -->
                            <div class="payments-section" style="margin-top: 50px;">
                                <h3 class="subsection-title">Payments/Adjustments/Refunds</h3>
                                <div class="grid-container">
                                    <SfGrid @ref="PaymentsGrid"
                                            TValue="ProfessionalClaimsPayments"
                                            Style="font-size: 0.85rem; margin-top: 24px;"
                                            DataSource="@PaymentsData"
                                            AllowPaging="true"
                                            PageSettings-PageSize="5"
                                            GridLines="GridLine.Both"
                                            AllowEditing="true"
                                            Toolbar="@(new List<string>() { "Add" })">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridPageSettings PageSize="10"></GridPageSettings>
                                        <GridEvents OnActionComplete="PaymentsActionCompletedHandler" OnActionBegin="PaymentsActionBeginHandler" TValue="ProfessionalClaimsPayments"></GridEvents>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.PaymentsId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Id)"  HeaderText="Id" Width="150" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Date)" HeaderText="Date" Width="150" Format="d" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.From)" HeaderText="From" Width="150" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Allowed)" HeaderText="Allowed" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center" ></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Deduct)" HeaderText="Deduct" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.CoIns)" HeaderText="CoIns" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Copay)" HeaderText="Copay" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Paid)" HeaderText="Paid" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Adjust)" HeaderText="Adjust" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(ProfessionalClaimsPayments.Withheld)" HeaderText="Withheld" Width="150" Format="$#,##0.00" Type="ColumnType.Number" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn HeaderText="Actions"
                                                        Width="120"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                                <GridCommandColumns>
                                                    <GridCommandColumn Type="CommandButtonType.Edit"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Delete"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Save"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                                </GridCommandColumns>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            </div>
                        }
                    </div>
                </ContentTemplate>
            </TabItem>

            <TabItem>
                <ChildContent>
                    <TabHeader Text="Additional Information"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="form-section">
                        <div class="additional-info-container" style="display: flex; gap: 20px;">
                            <!-- Left Side - 60% -->
                            <div class="additional-providers-section" style="flex: 0 0 60%; padding-right: 10px;">
                                <!-- Additional Providers Section -->
                                <div class="providers-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 20px; position: relative;">
                                    <div class="providers-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                        Additional Providers(optional)
                                    </div>

                                    <div class="providers-subtitle" style="font-size: 12px; color: #888; margin-bottom: 15px; font-style: italic;">
                                        (for ecw reports only and will not be submitted to insurances)
                                    </div>

                                    <div class="providers-inputs" style="display: flex; gap: 20px;">
                                        <div style="display: flex; flex-direction: column; gap: 5px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px;">Provider 1</label>
                                            <SfTextBox @bind-Value="@ProfessionalClaims.Provider1"
                                                       CssClass="compact-textbox"
                                                       Width="200px">
                                            </SfTextBox>
                                        </div>

                                        <div style="display: flex; flex-direction: column; gap: 5px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px;">Provider 2</label>
                                            <SfTextBox @bind-Value="@ProfessionalClaims.Provider2"
                                                       CssClass="compact-textbox"
                                                       Width="200px">
                                            </SfTextBox>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Rejection Status Section -->
                                <div class="rejection-status-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative;">
                                    <div style="display: flex; align-items: center; gap: 15px;">
                                        <label class="form-label" style="min-width: auto; margin: 0; font-size: 14px;">Payment Rejection Status:</label>
                                        <SfTextBox @bind-Value="@ProfessionalClaims.PaymentRejectionStatus"
                                                   CssClass="compact-textbox"
                                                   Width="250px">
                                        </SfTextBox>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Side - 40% -->
                            <div class="department-section" style="flex: 0 0 35%; padding-left: 10px;">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <SfTextBox @bind-Value="@ProfessionalClaims.Department"
                                               CssClass="compact-textbox"
                                               Width="200px">
                                    </SfTextBox>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>
        </TabItems>
    </SfTab>

    <!-- Independent Summary Tabs Section - Now Outside Main Tab Control -->
    <div class="summary-tabs-section">
        <SfTab CssClass="summary-tabs">
            <TabItems>
                <!-- Updated Summary Tab Content -->
                <TabItem>
                    <ChildContent>
                        <TabHeader Text="Summary"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <div class="summary-content">
                            <div class="summary-split-container">
                                <!-- Left Half - Billing Notes -->
                                <div class="billing-notes-section">
                                    <div class="form-group">
                                        <label class="form-label">Billing Notes</label>
                                        <SfTextBox @bind-Value="@ProfessionalClaims.BillingNotes"
                                                   Multiline="true"
                                                   CssClass="form-control billing-notes-textarea"
                                                   Width="90%"
                                                   Height="150px">
                                        </SfTextBox>
                                    </div>
                                </div>

                                <!-- Right Half - Amounts Grid -->
                                <div class="amounts-section">
                                    <div class="amounts-grid">
                                        <div class="amounts-column">
                                            <div class="amount-row">
                                                <span class="amount-header">Patient Portion</span>
                                            </div>
                                            <div class="amount-row">
                                                <span class="amount-label">Charges:</span>
                                                <SfNumericTextBox @bind-Value="@ProfessionalClaims.PatientCharges"
                                                                  Format="$#,##0.00"
                                                                  Enabled="false"
                                                                  CssClass="amount-input"
                                                                  ShowSpinButton="false"></SfNumericTextBox>
                                            </div>
                                            <div class="amount-row">
                                                <span class="amount-label">Payments:</span>
                                                <SfNumericTextBox @bind-Value="@ProfessionalClaims.PatientPayments"
                                                                  Format="$#,##0.00"
                                                                  CssClass="amount-input"
                                                                  Enabled="false"
                                                                  ShowSpinButton="false"></SfNumericTextBox>
                                            </div>
                                            <div class="amount-row">
                                                <span class="amount-label">Balance:</span>
                                                <SfNumericTextBox @bind-Value="@ProfessionalClaims.PatientBalance"
                                                                  Format="$#,##0.00"
                                                                  CssClass="amount-input"
                                                                  Enabled="false"
                                                                  ShowSpinButton="false"></SfNumericTextBox>
                                            </div>
                                        </div>
                                        <div class="amounts-column">
                                            <div class="amount-row">
                                                <span class="amount-header">Total</span>
                                            </div>
                                            <div class="amount-row">
                                                <span class="amount-label">Charges:</span>
                                                <SfNumericTextBox @bind-Value="@ProfessionalClaims.TotalCharges"
                                                                  Format="$#,##0.00"
                                                                  CssClass="amount-input"
                                                                  Enabled="false"
                                                                  ShowSpinButton="false"></SfNumericTextBox>
                                            </div>
                                            <div class="amount-row">
                                                <span class="amount-label">Payments:</span>
                                                <SfNumericTextBox @bind-Value="@ProfessionalClaims.TotalPayments"
                                                                  Format="$#,##0.00"
                                                                  CssClass="amount-input"
                                                                  Enabled="false"
                                                                  ShowSpinButton="false"></SfNumericTextBox>
                                            </div>
                                            <div class="amount-row">
                                                <span class="amount-label">Balance:</span>
                                                <SfNumericTextBox @bind-Value="@ProfessionalClaims.TotalBalance"
                                                                  Format="$#,##0.00"
                                                                  Enabled="false"
                                                                  CssClass="amount-input"
                                                                  ShowSpinButton="false"></SfNumericTextBox>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons Section -->
                            <div class="action-buttons" style="margin-top: 50px;">
                                <MudButton Variant="Variant.Outlined"
                                           OnClick="OnClaimHeaderClicked"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 120px;">
                                    Claim Header
                                </MudButton>
                                <MudDialog Visible="isClaimHeaderDialogVisible" Options="dialogOptions">
                                    <TitleContent>
                                        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                            <MudText Typo="Typo.h6">Claim Header</MudText>
                                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                           OnClick="CloseClaimHeaderDialog"
                                                           Size="Size.Small"
                                                           Style="color: #666;">
                                            </MudIconButton>
                                        </div>
                                    </TitleContent>
                                    <DialogContent>
                                        <div class="form-section">
                                            <div class="claim-data-container" style="display: flex; flex-direction: column; gap: 15px;">

                                                <!-- Patient Data and Miscellaneous Services Section -->
                                                <div style="display: flex; gap: 20px;">
                                                    <!-- Patient Data Section (Reduced height) -->
                                                    <div class="patient-data-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative; flex: 2; height: fit-content;">
                                                        <div class="patient-data-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                            Patient Data
                                                        </div>

                                                        <div class="patient-data-inputs" style="display: flex; flex-direction: column; gap: 12px; margin-top: 10px;">
                                                            <div style="display: flex; align-items: center; gap: 15px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; min-width: 130px;">Resident Type</label>
                                                                <SfTextBox @bind-Value="@ProfessionalClaims.ResidentType"
                                                                           CssClass="compact-textbox"
                                                                           Width="250px">
                                                                </SfTextBox>
                                                            </div>

                                                            <div style="display: flex; align-items: center; gap: 15px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; min-width: 130px;">Student Status</label>
                                                                <SfTextBox @bind-Value="@ProfessionalClaims.StudentStatus"
                                                                           CssClass="compact-textbox"
                                                                           Width="250px">
                                                                </SfTextBox>
                                                            </div>

                                                            <div style="display: flex; align-items: center; gap: 15px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; min-width: 130px;">Employment Status</label>
                                                                <SfTextBox @bind-Value="@ProfessionalClaims.EmploymentStatus"
                                                                           CssClass="compact-textbox"
                                                                           Width="250px">
                                                                </SfTextBox>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Miscellaneous Services Section -->
                                                    <div class="miscellaneous-services-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative; flex: 1;">
                                                        <div class="miscellaneous-services-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                            Miscellaneous Services
                                                        </div>

                                                        <div class="miscellaneous-services-inputs" style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                            <!-- Healthy Kid Service -->
                                                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; text-align: center;">Healthy Kid Service</label>
                                                                <div style="display: flex; justify-content: center; gap: 15px;">
                                                                    <SfCheckBox @bind-Checked="@HealthyKidServiceYes"
                                                                                Label="Yes"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                    <SfCheckBox @bind-Checked="@HealthyKidServiceNo"
                                                                                Label="No"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                </div>
                                                            </div>

                                                            <!-- Family Planning -->
                                                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; text-align: center;">Family Planning</label>
                                                                <div style="display: flex; justify-content: center; gap: 15px;">
                                                                    <SfCheckBox @bind-Checked="@FamilyPlanningYes"
                                                                                Label="Yes"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                    <SfCheckBox @bind-Checked="FamilyPlanningNo"
                                                                                Label="No"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                </div>
                                                            </div>

                                                            <!-- Sterilization/Abortion -->
                                                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; text-align: center;">Sterilization/Abortion</label>
                                                                <div style="display: flex; justify-content: center; gap: 15px;">
                                                                    <SfCheckBox @bind-Checked="SterilizationAbortionYes"
                                                                                Label="Yes"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                    <SfCheckBox @bind-Checked="@SterilizationAbortionNo"
                                                                                Label="No"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Claim Editing Indicator/Plan Type -->
                                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                                    <label class="form-label" style="margin: 0; font-size: 14px;"><b>Claim Editing Indicator/Plan Type</b></label>
                                                    <SfTextBox @bind-Value="@ProfessionalClaims.ClaimEditingIndicator"
                                                               CssClass="compact-textbox"
                                                               Width="50%">
                                                    </SfTextBox>
                                                </div>

                                                <!-- Claim Type -->
                                                <div style="display: flex; flex-direction: column; gap: 5px; margin-top: 10px;">
                                                    <label class="form-label" style="margin: 0; font-size: 14px;">Claim Type</label>
                                                    <SfTextBox @bind-Value="@ProfessionalClaims.ClaimType"
                                                               CssClass="compact-textbox"
                                                               Width="50%">
                                                    </SfTextBox>
                                                </div>

                                                <!-- Facility/Lab Id and Facility Type -->
                                                <div style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                    <div style="display: flex; align-items: center; gap: 15px;">
                                                        <label class="form-label" style="margin: 0; font-size: 14px; min-width: 215px;">Facility/Lab Id Number</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.FacilityLabId"
                                                                   CssClass="compact-textbox"
                                                                   Width="300px">
                                                        </SfTextBox>
                                                    </div>

                                                    <div style="display: flex; align-items: center; gap: 15px;">
                                                        <label class="form-label" style="margin: 0; font-size: 14px; min-width: 215px;">Facility Type</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.FacilityType"
                                                                   CssClass="compact-textbox"
                                                                   Width="300px">
                                                        </SfTextBox>
                                                    </div>
                                                </div>

                                                <!-- Resubmittal Section -->
                                                <div style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                    <SfCheckBox @bind-Checked="@ProfessionalClaims.IsResubmittal"
                                                                Label="Resubmittal"
                                                                CssClass="resubmittal-checkbox">
                                                    </SfCheckBox>

                                                    <!-- Resubmission fields (enabled only when checkbox is checked) -->
                                                    <div style="display: flex; flex-direction: column; gap: 15px; margin-bottom: 20px;">
                                                        <div style="display: flex; align-items: center; gap: 15px;">
                                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 215px;">Resubmission Code</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ResubmissionCode"
                                                                       CssClass="compact-textbox"
                                                                       Width="300px"
                                                                       Enabled="@ProfessionalClaims.IsResubmittal">
                                                            </SfTextBox>
                                                        </div>

                                                        <div style="display: flex; align-items: center; gap: 15px;">
                                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 215px;">Resubmission Reference Number</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ResubmissionRefNumber"
                                                                       CssClass="compact-textbox"
                                                                       Width="300px"
                                                                       Enabled="@ProfessionalClaims.IsResubmittal">
                                                            </SfTextBox>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Reserved for local use (HCFA Paper Claims only) Section -->
                                                <div class="hcfa-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative; margin-top: 10px;">
                                                    <div class="hcfa-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                        Reserved for local use (HCFA Paper Claims only)
                                                    </div>

                                                    <div class="hcfa-inputs" style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                        <div style="display: flex; align-items: center; gap: 15px;">
                                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 85px;">HCFA 10d</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.Hcfa10d"
                                                                       CssClass="compact-textbox"
                                                                       Width="300px">
                                                            </SfTextBox>
                                                        </div>

                                                        <div style="display: flex; align-items: center; gap: 15px;">
                                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 85px;">HCFA 19</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.Hcfa19"
                                                                       CssClass="compact-textbox"
                                                                       Width="300px">
                                                            </SfTextBox>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Claim Data Field -->
                                                <div style="display: flex; align-items: center; gap: 15px; margin-top: 10px;">
                                                    <label class="form-label" style="margin: 0; font-size: 14px; min-width: 100px;">Claim Note</label>
                                                    <SfTextBox @bind-Value="@ProfessionalClaims.ClaimData"
                                                               CssClass="compact-textbox"
                                                               Width="300px">
                                                    </SfTextBox>
                                                </div>

                                                <!-- Provider Assignment Indicator box (HCFA Box 27) -->
                                                <div class="provider-assignment-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative; margin-top: 10px;">
                                                    <div class="provider-assignment-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                        Provider Assignment Indicator box (HCFA Box 27)
                                                    </div>

                                                    <div class="provider-assignment-inputs" style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                        <div style="display: flex; align-items: center; gap: 15px;">
                                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 85px;">Assigned</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ProviderAssignmentIndicator"
                                                                       CssClass="compact-textbox"
                                                                       Width="75px">
                                                            </SfTextBox>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Delay Reason -->
                                                <div style="display: flex; align-items: center; gap: 15px; margin-top: 10px;">
                                                    <label class="form-label" style="margin: 0; font-size: 14px; min-width: 100px;">Delay Reason</label>
                                                    <SfTextBox @bind-Value="@ProfessionalClaims.DelayReason"
                                                               CssClass="compact-textbox"
                                                               Width="75px">
                                                    </SfTextBox>
                                                </div>

                                            </div>
                                        </div>
                                    </DialogContent>
                                    <DialogActions>
                                        <div style="display: flex; justify-content: flex-end; gap: 10px; width: 100%; padding-right: 25px;">
                                            <MudButton OnClick="CloseClaimHeaderDialog"
                                                       Color="Color.Secondary"
                                                       Variant="Variant.Outlined"
                                                       Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;">
                                                Cancel
                                            </MudButton>

                                            <MudButton OnClick="SaveClaimHeaderData"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;">
                                                Save
                                            </MudButton>
                                        </div>

                                    </DialogActions>
                                </MudDialog>

                                <MudButton Variant="Variant.Outlined"
                                           OnClick="OnClaimDataClicked"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 105px;"
                                           Class="uniform-button">
                                    Claim Data
                                </MudButton>

                                <MudDialog Visible="isClaimDataDialogVisible" Options="dialogOptions">
                                    <TitleContent>
                                        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                            <MudText Typo="Typo.h6">Claim Data</MudText>
                                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                           OnClick="CloseClaimDataDialog"
                                                           Size="Size.Small"
                                                           Style="color: #666;">
                                            </MudIconButton>
                                        </div>
                                    </TitleContent>
                                    <DialogContent>
                                        <div class="form-section">
                                            <div class="claim-data-container" style="display: flex; gap: 20px;">

                                                <!-- Left side: Is patient condition related to Section -->
                                                <div class="left-section" style="flex: 1; min-width: 400px;">
                                                    <div class="patient-condition-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative;">
                                                        <div class="patient-condition-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                            Is patient condition related to
                                                        </div>

                                                        <div class="patient-condition-inputs" style="display: flex; flex-direction: column; gap: 20px; margin-top: 10px;">

                                                            <!-- Employment Section -->
                                                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500;">a. Employment? (Current or Previous)</label>
                                                                <div style="display: flex; align-items: center; gap: 15px; margin-left: 20px;">
                                                                    <SfCheckBox @bind-Checked="EmploymentRelatedYes"
                                                                                Label="Yes"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                    <SfCheckBox @bind-Checked="EmploymentRelatedNo"
                                                                                Label="No"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                </div>
                                                            </div>

                                                            <!-- Accident Section -->
                                                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                                                <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500;">b. Accident?</label>
                                                                <div style="display: flex; align-items: center; gap: 15px; margin-left: 20px;">
                                                                    <SfCheckBox @bind-Checked="AccidentAuto"
                                                                                Label="Auto"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                    <SfCheckBox @bind-Checked="AccidentNonAuto"
                                                                                Label="Non Auto"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                    <SfCheckBox @bind-Checked="AccidentNo"
                                                                                Label="No"
                                                                                CssClass="service-checkbox">
                                                                    </SfCheckBox>
                                                                </div>
                                                            </div>

                                                            <!-- Accident Details Section -->
                                                            <div style="display: flex; flex-direction: column; gap: 15px; margin-left: 20px;">
                                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                                    <label class="form-label" style="margin: 0; font-size: 14px; min-width: 170px;">Place</label>
                                                                    <SfTextBox @bind-Value="@ProfessionalClaims.AccidentPlace"
                                                                               CssClass="compact-textbox"
                                                                               Width="125px">
                                                                    </SfTextBox>
                                                                </div>

                                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                                    <label class="form-label" style="margin: 0; font-size: 14px; min-width: 170px;">Accident Hour</label>
                                                                    <SfTextBox @bind-Value="@ProfessionalClaims.AccidentHour"
                                                                               CssClass="compact-textbox"
                                                                               Width="125px">
                                                                    </SfTextBox>
                                                                </div>

                                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                                    <label class="form-label" style="margin: 0; font-size: 14px; min-width: 170px;">External Cause of Accident</label>
                                                                    <SfTextBox @bind-Value="@ProfessionalClaims.ExternalCauseOfAccident"
                                                                               CssClass="compact-textbox"
                                                                               Width="125px">
                                                                    </SfTextBox>
                                                                </div>

                                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                                    <label class="form-label" style="margin: 0; font-size: 14px; min-width: 170px;">Responsibility Indicator</label>
                                                                    <SfTextBox @bind-Value="@ProfessionalClaims.ResponsibilityIndicator"
                                                                               CssClass="compact-textbox"
                                                                               Width="125px">
                                                                    </SfTextBox>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Right side: All other sections -->
                                                <div class="right-section" style="flex: 1; min-width: 400px; display: flex; flex-direction: column; gap: 15px;">

                                                    <!-- Documentation Section -->
                                                    <div style="display: flex; flex-direction: column; gap: 10px;">
                                                        <div style="display: flex; align-items: center; gap: 10px;">
                                                            <!-- Label with subtext stacked vertically -->
                                                            <div style="display: flex; flex-direction: column; min-width: 160px;">
                                                                <label class="form-label" style="margin: 0; font-size: 12px;">Documentation Indicator</label>
                                                                <span style="font-size: 10px; color: #888;">(Report Transmission Code)</span>
                                                            </div>

                                                            <!-- Textbox aligned to the right of label block -->
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.DocumentationIndicator"
                                                                       CssClass="compact-textbox"
                                                                       Width="200px">
                                                            </SfTextBox>
                                                        </div>


                                                        <div style="display: flex; align-items: center; gap: 10px;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 160px;">Documentation Type</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.DocumentationType"
                                                                       CssClass="compact-textbox"
                                                                       Width="200px">
                                                            </SfTextBox>
                                                        </div>

                                                        <div style="display: flex; align-items: center; gap: 10px;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 160px;">Attachment Control Number</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.AttachmentControlNumber"
                                                                       CssClass="compact-textbox"
                                                                       Width="200px">
                                                            </SfTextBox>
                                                        </div>

                                                        <div style="display: flex; align-items: center; gap: 10px;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 160px;">Date Documentation Sent</label>
                                                            <SfDatePicker @bind-Value="@ProfessionalClaims.DateDocumentationSent"
                                                                          CssClass="compact-datepicker"
                                                                          Width="130px">
                                                            </SfDatePicker>
                                                        </div>

                                                        <div style="display: flex; align-items: center; gap: 10px;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 130px;">Release of Information</label>
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ReleaseOfInformation"
                                                                       CssClass="compact-textbox"
                                                                       Width="70px">
                                                            </SfTextBox>
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 50px;">Sig. Date</label>
                                                            <SfDatePicker @bind-Value="@ProfessionalClaims.SignatureDate"
                                                                          CssClass="compact-datepicker"
                                                                          Width="130px">
                                                            </SfDatePicker>
                                                        </div>
                                                    </div>

                                                    <!-- Patient Work Dates Section -->
                                                    <div class="patient-unable-work-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 12px; position: relative;">
                                                        <div class="patient-unable-work-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 12px;">
                                                            Dates Patient Unable To Work In Current Occupation
                                                        </div>

                                                        <div class="patient-unable-work-inputs" style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 115px;">HCFA Form Box 16:</label>
                                                            <SfDatePicker @bind-Value="@ProfessionalClaims.UnableToWorkFromDate"
                                                                          CssClass="compact-datepicker"
                                                                          Style="flex: 1;">
                                                            </SfDatePicker>
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 20px; margin-left: 6px;">to</label>
                                                            <SfDatePicker @bind-Value="@ProfessionalClaims.UnableToWorkToDate"
                                                                          CssClass="compact-datepicker"
                                                                          Style="flex: 1;">
                                                            </SfDatePicker>
                                                        </div>
                                                    </div>


                                                    <!-- Hospitalization Dates Section -->
                                                    <div class="hospitalization-dates-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 12px; position: relative;">
                                                        <div class="hospitalization-dates-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 12px;">
                                                            Hospitalization Dates Related To Current Service
                                                        </div>

                                                        <div class="hospitalization-dates-inputs" style="display: flex; align-items: center; gap: 8px; margin-top: 8px;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 115px;">HCFA Form Box 18:</label>
                                                            <SfDatePicker @bind-Value="@ProfessionalClaims.HospitalizationFromDate"
                                                                          CssClass="compact-datepicker"
                                                                          Style="flex: 1;">
                                                            </SfDatePicker>
                                                            <label class="form-label" style="margin: 0; font-size: 12px; min-width: 20px; margin-left: 6px;">to</label>
                                                            <SfDatePicker @bind-Value="@ProfessionalClaims.HospitalizationToDate"
                                                                          CssClass="compact-datepicker"
                                                                          Style="flex: 1;">
                                                            </SfDatePicker>
                                                        </div>
                                                    </div>

                                                    <!-- Lab Charges Section -->
                                                    <div class="lab-charges-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 12px; position: relative;">
                                                        <div class="lab-charges-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 12px;">
                                                            Lab Charges
                                                        </div>
                                                        <div class="lab-charges-inputs" style="display: flex; align-items: center; gap: 10px; margin-top: 8px; flex-wrap: wrap;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px;">Outside Lab?</label>
                                                            <SfCheckBox @bind-Checked="@ProfessionalClaims.OutsideLabYes"
                                                                        Label="Yes"
                                                                        CssClass="service-checkbox">
                                                            </SfCheckBox>
                                                            <SfCheckBox @bind-Checked="@ProfessionalClaims.OutsideLabNo"
                                                                        Label="No"
                                                                        CssClass="service-checkbox">
                                                            </SfCheckBox>

                                                            <!-- Lab Charges TextBox inline -->
                                                            <SfNumericTextBox @bind-Value="@ProfessionalClaims.LabCharges"
                                                                              CssClass="compact-textbox"
                                                                              Width="160px"
                                                                              Format="$#,##0.00">
                                                            </SfNumericTextBox>
                                                        </div>
                                                    </div>


                                                </div>

                                            </div>

                                            <!-- New section below the boxes -->
                                            <div class="symptoms-section" style="margin-top: 20px;">
                                                <!-- First row: Input controls with labels above -->
                                                <div style="display: flex; align-items: flex-start; gap: 15px; margin-bottom: 15px;">
                                                    <div style="flex: 1; display: flex; flex-direction: column;">
                                                        <label class="form-label" style="margin: 0 0 5px 0; font-size: 12px; font-weight: 500;">Symptom</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.Symptom"
                                                                   CssClass="compact-textbox">
                                                        </SfTextBox>
                                                    </div>

                                                    <div style="flex: 1; display: flex; flex-direction: column;">
                                                        <label class="form-label" style="margin: 0 0 5px 0; font-size: 12px; font-weight: 500;">Accident/Symptom Date</label>
                                                        <SfDatePicker @bind-Value="@ProfessionalClaims.AccidentSymptomDate"
                                                                      CssClass="compact-datepicker">
                                                        </SfDatePicker>
                                                    </div>

                                                    <div style="flex: 1; display: flex; flex-direction: column;">
                                                        <label class="form-label" style="margin: 0 0 5px 0; font-size: 12px; font-weight: 500;">Date Last Seen <span style="font-size: 10px; color: #888;">(For Podiatric Claims)</span></label>
                                                        <SfDatePicker @bind-Value="@ProfessionalClaims.DateLastSeen"
                                                                      CssClass="compact-datepicker">
                                                        </SfDatePicker>
                                                    </div>

                                                    <div style="flex: 1; display: flex; flex-direction: column;">
                                                        <label class="form-label" style="margin: 0 0 5px 0; font-size: 12px; font-weight: 500;">Initial Treatment Date</label>
                                                        <SfDatePicker @bind-Value="@ProfessionalClaims.InitialTreatmentDate"
                                                                      CssClass="compact-datepicker">
                                                        </SfDatePicker>
                                                    </div>
                                                </div>

                                                <!-- Second row: Input controls with labels above -->
                                                <div style="display: flex; align-items: flex-start; gap: 15px;">
                                                    <div style="flex: 1; display: flex; flex-direction: column;">
                                                        <label class="form-label" style="margin: 0 0 5px 0; font-size: 12px; font-weight: 500;">Similar Symptom</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.SimilarSymptom"
                                                                   CssClass="compact-textbox">
                                                        </SfTextBox>
                                                    </div>

                                                    <div style="flex: 1; display: flex; flex-direction: column;">
                                                        <label class="form-label" style="margin: 0 0 5px 0; font-size: 12px; font-weight: 500;">Similar Symptom Date</label>
                                                        <SfDatePicker @bind-Value="@ProfessionalClaims.SimilarSymptomDate"
                                                                      CssClass="compact-datepicker">
                                                        </SfDatePicker>
                                                    </div>

                                                    <!-- Empty divs to maintain spacing -->
                                                    <div style="flex: 1;"></div>
                                                    <div style="flex: 1;"></div>
                                                </div>
                                            </div>

                                            <!-- Special Program Section -->
                                            <div class="special-program-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative; margin-top: 20px;">
                                                <div class="special-program-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                    Special Program
                                                </div>

                                                <div class="special-program-inputs" style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                    <div style="display: flex; align-items: center; gap: 15px;">
                                                        <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500; min-width: 30px;">Code</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.SpecialProgramCode"
                                                                   CssClass="compact-textbox"
                                                                   Width="150px">
                                                        </SfTextBox>
                                                    </div>

                                                    <div style="display: flex; align-items: center; gap: 15px;">
                                                        <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500; min-width: 100px;">Was an EPSDT Referral given to the patient?</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.EPSDTReferralGiven"
                                                                   CssClass="compact-textbox"
                                                                   Width="150px">
                                                        </SfTextBox>
                                                    </div>

                                                    <div style="display: flex; align-items: center; gap: 15px;">
                                                        <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500; min-width: 100px;">EPSDT Referral Code</label>
                                                        <SfTextBox @bind-Value="@ProfessionalClaims.EPSDTReferralCode"
                                                                   CssClass="compact-textbox"
                                                                   Width="150px">
                                                        </SfTextBox>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Referring Provider Section -->
                                            <div class="referring-provider-container" style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; position: relative; margin-top: 20px;">
                                                <div class="referring-provider-header" style="position: absolute; top: -12px; left: 10px; background: white; padding: 0 8px; font-weight: 500; color: #666; font-size: 14px;">
                                                    Referring Provider
                                                </div>

                                                <div class="referring-provider-inputs" style="display: flex; flex-direction: column; gap: 15px; margin-top: 10px;">
                                                    <div style="display: flex; align-items: center; gap: 15px;">
                                                        <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500; min-width: 120px;">Provider ID Type</label>
                                                        <div style="display: flex; gap: 15px; margin-left: auto;">
                                                            <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500; width: 150px;">Name</label>
                                                            <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500; width: 100px;">ID</label>
                                                        </div>
                                                    </div>

                                                    <div style="display: flex; align-items: flex-start; gap: 15px;">
                                                        <div style="display: flex; align-items: center; gap: 10px; min-width: 120px;">
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ReferringProviderANSI"
                                                                       CssClass="compact-textbox"
                                                                       Width="100px">
                                                            </SfTextBox>
                                                            <label class="form-label" style="margin: 0; font-size: 12px; font-weight: 500;">ANSI-Provider UPIN Number</label>
                                                        </div>

                                                        <div style="display: flex; gap: 15px; margin-left: auto;">
                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ReferringProviderName"
                                                                       Readonly="true"
                                                                       CssClass="compact-textbox"
                                                                       Width="150px">
                                                            </SfTextBox>

                                                            <SfTextBox @bind-Value="@ProfessionalClaims.ReferringProviderID"
                                                                       CssClass="compact-textbox"
                                                                       Width="100px">
                                                            </SfTextBox>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </DialogContent>
                                    <DialogActions>
                                        <div style="display: flex; justify-content: flex-end; gap: 10px; width: 100%; padding-right: 25px;">
                                            <MudButton OnClick="CloseClaimDataDialog"
                                                       Color="Color.Secondary"
                                                       Variant="Variant.Outlined"
                                                       Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;">
                                                Cancel
                                            </MudButton>
                                            <MudButton OnClick="SaveClaimData"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;">
                                                Save
                                            </MudButton>
                                        </div>
                                    </DialogActions>
                                </MudDialog>
                                <MudButton Variant="Variant.Outlined"
                                           OnClick="@(EventCallback.Factory.Create(this, OnOptionsClicked))"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 80px;"
                                           Class="uniform-button">
                                    Options
                                </MudButton>
                                <MudButton Variant="Variant.Outlined"
                                           OnClick="@(EventCallback.Factory.Create(this, OnPrintHCFAClicked))"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 140px;"
                                           Class="uniform-button">
                                    Print HCFA(New)
                                </MudButton>
                                <MudButton Variant="Variant.Outlined"
                                           OnClick="@(EventCallback.Factory.Create(this, OnAdjustmentsClicked))"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 110px;"
                                           Class="uniform-button">
                                    Adjustments
                                </MudButton>
                                <MudButton Variant="Variant.Filled"
                                           Disabled="true"
                                           OnClick="@(EventCallback.Factory.Create(this, OnProgNotesClicked))"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 100px;"
                                           Class="uniform-button">
                                    Prog.Notes
                                </MudButton>
                                <MudButton Variant="Variant.Outlined"
                                           OnClick="@(EventCallback.Factory.Create(this, OnCPTPayersClicked))"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 105px;"
                                           Class="uniform-button">
                                    CPT Payers
                                </MudButton>
                                <MudButton Variant="Variant.Filled"
                                           OnClick="@(EventCallback.Factory.Create(this, OnOkClicked))"
                                           Color="Color.Primary"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 45px;"
                                           Class="uniform-button">
                                    OK
                                </MudButton>
                                <MudButton Variant="Variant.Outlined"
                                           OnClick="@(EventCallback.Factory.Create(this, OnCancelClicked))"
                                           Color="Color.Error"
                                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 80px;"
                                           Class="uniform-button">
                                    Cancel
                                </MudButton>
                            </div>
                        </div>
                    </ContentTemplate>
                </TabItem>

                <TabItem>
                    <ChildContent>
                        <TabHeader Text="*Error"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <div class="error-content">
                            <div class="error-grid-container">
                                <!-- Error content will go here -->
                                <div class="error-placeholder">
                                    <p>Error information will be displayed here.</p>
                                </div>
                            </div>
                        </div>
                    </ContentTemplate>
                </TabItem>

                <TabItem>
                    <ChildContent>
                        <TabHeader Text="Claims Logs"></TabHeader>
                    </ChildContent>
                    <ContentTemplate>
                        <div class="claims-logs-content">
                            <div class="claims-logs-grid-container">
                                <!-- Claims Logs content will go here -->
                                <div class="claims-logs-placeholder">
                                    <p>Claims logs information will be displayed here.</p>
                                </div>
                            </div>
                        </div>
                    </ContentTemplate>
                </TabItem>
            </TabItems>
        </SfTab>
    </div>
    <div class="form-actions">
        <SfButton CssClass="btn-secondary" @onclick="OnCancel">
            <span class="btn-icon">✕</span>
            Cancel
        </SfButton>
        <SfButton CssClass="btn-primary" @onclick="OnSubmit">
            <span class="btn-icon">✓</span>
            Submit Claim
        </SfButton>
    </div>
</div>

 <style>
    :root {
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --primary-color: #007bff;
        --light-gray: #f8f9fa;
        --medium-gray: #dee2e6;
        --text-color: #212529;
        --text-muted: #6c757d;
        --focus-color: #80bdff;
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
    }

    /* OK Button - Green */
    .ok-btn {
        background: var(--success-color);
        color: white;
        border: 1px solid var(--success-color);
    }

    /* Cancel Button - Red */
    .cancel-btn {
        background: var(--danger-color);
        color: white;
        border: 1px solid var(--danger-color);
    }

    /* Adjustments Button - Yellow */
    .adjustments-btn {
        background: var(--warning-color);
        color: var(--text-color);
        border: 1px solid var(--warning-color);
    }

    /* All Other Buttons - Blue */
    .claim-header-btn,
    .claim-data-btn,
    .options-btn,
    .print-hcfa-btn,
    .cpt-payers-btn {
        background: var(--primary-color);
        color: white;
        border: 1px solid var(--primary-color);
    }

    /* Prog.Notes button - Always disabled styling */
    .prog-notes-btn {
        background: var(--light-gray);
        color: var(--text-muted);
        border: 1px solid var(--medium-gray);
        cursor: not-allowed;
        opacity: 0.6;
    }

    /* Hover effects */
    .action-btn:hover:not(:disabled) {
        opacity: 0.9;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .action-btn:active:not(:disabled) {
        transform: translateY(0);
    }

    /* Focus styles for accessibility */
    .action-btn:focus {
        outline: 2px solid var(--focus-color);
        outline-offset: 2px;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        margin-top: 30px;
        padding: 20px 0;
        border-top: 1px solid #e0e0e0;
    }

    .btn-primary {
        background-color: #007bff;
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.3s ease;
    }

        .btn-primary:hover {
            background-color: #0056b3;
        }

    .btn-secondary {
        background-color: #6c757d;
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.3s ease;
    }

        .btn-secondary:hover {
            background-color: #545b62;
        }

    .btn-icon {
        font-size: 12px;
        font-weight: bold;
    }

    /* Tab styles */
    .custom-tabs {
        margin-top: 2rem;
    }

        .custom-tabs .e-tab-header {
            background: var(--light-gray);
            border-bottom: 2px solid var(--medium-gray);
        }

            .custom-tabs .e-tab-header .e-tab-text {
                font-weight: 500;
                color: var(--dark-gray);
                font-size: 0.95rem;
            }

            .custom-tabs .e-tab-header .e-active .e-tab-text {
                color: var(--primary-color);
                font-weight: 600;
            }

        .custom-tabs .e-content .e-item {
            padding: 0;
            background: white;
        }

    .tab-placeholder {
        padding: 2rem;
        text-align: center;
        color: var(--dark-gray);
        background: var(--light-gray);
        border-radius: 8px;
        margin: 1rem 0;
    }

        .tab-placeholder p {
            margin: 0;
            font-style: italic;
        }

    /* Patient Info Section Layout - Updated with reduced spacing */
    .patient-info-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--light-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .patient-info-grid {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
        justify-content: space-between;
    }

    /* Container for dropdown/text fields */
    .dropdown-info-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    /* Each row: label + textbox */
    .horizontal-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Form labels */
    .form-label {
        min-width: 80px;
        font-size: 0.9rem;
    }

    /* Textbox style (adjusted for centered cursor) */
    .dropdown-info-group .e-textbox,
    .dropdown-info-group .e-control-wrapper {
        width: 180px !important;
        height: 28px !important;
    }

        .dropdown-info-group .e-input,
        .dropdown-info-group .e-control-wrapper input.e-input {
            height: 100% !important;
            line-height: 28px !important;
            padding: 0 0.75rem !important;
            font-size: 0.9rem !important;
        }

    /* Centered and bold heading between label and input */
    .provider-heading {
        align-self: center;
        font-weight: bold;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .patient-info-group {
        flex: 0 0 400px; /* Fixed width instead of flex: 1 */
        margin-bottom: 0;
    }

    .financial-info-group {
        flex: 0 0 auto;
        display: flex;
        flex-direction: column;
        gap: 0.5rem; /* Reduced from 1rem to 0.5rem */
        min-width: 250px; /* Reduced from 280px */
        margin-left: 0; /* Removed extra margin */
    }

    .financial-labels-row {
        display: flex;
        gap: 1rem; /* Reduced from 1.5rem to 1rem */
        margin-bottom: 0.5rem;
    }

    .financial-label {
        font-weight: 500;
        color: var(--dark-gray);
        font-size: 0.9rem;
        white-space: nowrap;
        min-width: 110px; /* Reduced from 120px */
    }

    .financial-inputs-row {
        display: flex;
        gap: 1rem; /* Reduced from 1.5rem to 1rem */
        align-items: center;
    }

    .financial-input-box {
        width: 120px !important; /* Reduced from 130px */
        height: 32px !important;
    }

        /* Syncfusion NumericTextBox specific overrides for financial inputs */
        .financial-input-box .e-input-group {
            width: 120px !important; /* Reduced from 130px */
            height: 32px !important;
            border: 1px solid var(--medium-gray);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

            .financial-input-box .e-input-group .e-input {
                height: 30px !important;
                padding: 0 0.75rem !important;
                font-size: 0.9rem !important;
                line-height: 30px !important;
                text-align: right; /* Right-align currency values */
            }

            /* Hover and focus states for financial inputs */
            .financial-input-box .e-input-group:hover {
                border-color: var(--primary-color);
            }

            .financial-input-box .e-input-group.e-input-focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
            }

    /* Prior Authorization input - REDUCED HEIGHT */
    .prior-auth-input {
        width: 140px !important;
        height: 26px !important; /* Reduced from 35px to 28px */
        flex-shrink: 0;
        padding: 4px 10px; /* Reduced padding */
        line-height: 20px !important; /* Reduced line-height */
        vertical-align: middle;
        box-sizing: border-box;
        display: flex !important;
        align-items: center !important;
    }

    /* Updated status row to accommodate all items in one row */
    .status-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 2rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }

    .status-dropdown-container,
    .status-checkbox-container,
    .copay-container,
    .uncovered-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        width: 100%; /* Full width of container */
    }

    .copay-input {
        width: 160px !important; /* Reduced from 80px to 100px for better visibility */
    }

    .status-label {
        font-weight: 500;
        color: var(--dark-gray);
        font-size: 0.9rem;
        white-space: nowrap;
        min-width: 120px; /* Set minimum width for labels to align textboxes */
    }

    /* Dedicated CSS class for patient info textarea with increased height */
    .patient-textarea-large {
        resize: vertical;
        min-height: 150px !important;
        height: 150px !important;
    }

        /* Syncfusion specific overrides for the large textarea */
        .patient-textarea-large .e-input-group {
            height: 150px !important;
            min-height: 150px !important;
            border: 1px solid var(--medium-gray);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

            .patient-textarea-large .e-input-group .e-input {
                height: 150px !important;
                min-height: 150px !important;
                padding: 10px !important;
                font-size: 0.95rem;
                line-height: 1.4;
            }

            .patient-textarea-large .e-input-group textarea {
                height: 140px !important;
                min-height: 140px !important;
                resize: vertical;
            }

            /* Hover and focus states for the large textarea */
            .patient-textarea-large .e-input-group:hover {
                border-color: var(--primary-color);
            }

            .patient-textarea-large .e-input-group.e-input-focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
            }

    .financial-input {
        text-align: right;
    }

    /* Syncfusion TextBox styles for patient info */
    .patient-info-textarea .e-input-group {
        border: 1px solid var(--medium-gray);
        border-radius: 6px;
        transition: all 0.2s ease;
    }

        .patient-info-textarea .e-input-group:hover {
            border-color: var(--primary-color);
        }

        .patient-info-textarea .e-input-group.e-input-focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
        }

    .financial-input .e-input-group {
        border: 1px solid var(--medium-gray);
        border-radius: 6px;
        transition: all 0.2s ease;
    }

        .financial-input .e-input-group:hover {
            border-color: var(--primary-color);
        }

        .financial-input .e-input-group.e-input-focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
        }

    /* Base styles */
    .dental-claim-form {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        color: var(--text-color);
    }

    /* Header styles */
    .form-header {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-title {
        color: var(--primary-color);
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        letter-spacing: -0.5px;
    }

    .header-accent {
        height: 4px;
        width: 80px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
    }

    /* Professional Claims Header Card - Updated for 5 columns */
    .claim-header-card {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--light-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Checkbox vertical alignment */
    .checkboxes-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .checkbox-vertical-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    /* Subsection styles */
    .subsection-title {
        color: var(--primary-dark);
        font-size: 1rem;
        margin: 1.5rem 0 0.75rem 0;
        border-bottom: 1px solid var(--medium-gray);
        padding-bottom: 0.5rem;
    }

    .service-rendered-section,
    .dos-section,
    .billing-dentist-section,
    .icd-section,
    .remarks-section {
        margin-bottom: 1.5rem;
    }

    .total-charges-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 1rem;
        gap: 1rem;
    }

    .total-label {
        font-weight: 600;
    }

    .total-input {
        width: 120px;
    }

    .dentist-controls,
    .icd-controls {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .grid-actions {
        display: flex;
        justify-content: center;
    }

    .remarks-textarea {
        width: 100%;
        min-height: 80px;
    }

    .e-grid .e-rowcell {
        padding: 8px !important;
    }

    /* Section styles */
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        border: 1px solid var(--medium-gray);
    }

    .section-title {
        color: var(--primary-dark);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        font-size: 1.5rem;
    }

    /* Form control styles */
    .form-group {
        margin-bottom: 1.25rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--dark-gray);
        font-size: 0.9rem;
        white-space: nowrap;
    }

    .form-control {
        width: 100%;
        min-height: 24px !important; /* Reduced from 35px to 28px */
        padding: 0.5rem; /* Reduced padding */
        border: 1px solid var(--medium-gray);
        border-radius: 6px;
        font-size: 0.95rem;
        transition: all 0.2s ease;
    }

    /* Compact form control for header card */
    .form-control-compact {
        width: 180px !important;
        min-height: 24px !important; /* Reduced from 35px to 28px */
        padding: 0.25rem 0.75rem !important; /* Reduced padding */
        font-size: 0.9rem !important;
        border: 1px solid var(--medium-gray);
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    /* DatePicker specific styles for header card - REDUCED HEIGHT */
    .claim-header-card .e-datepicker {
        width: 180px !important;
    }

        .claim-header-card .e-datepicker .e-input-group {
            width: 180px !important;
            height: 28px !important; /* Reduced from 35px to 28px */
        }

            .claim-header-card .e-datepicker .e-input-group .e-input {
                height: 26px !important; /* Reduced from 33px to 26px */
                padding: 0 0.75rem !important;
                font-size: 0.9rem !important;
                line-height: 26px !important; /* Adjusted line-height */
            }

    /* TextBox specific styles for header card - REDUCED HEIGHT */
    .claim-header-card .e-textbox {
        width: 180px !important;
        height: 24px !important; /* Reduced from 35px to 28px */
    }

        .claim-header-card .e-textbox .e-input-group {
            width: 180px !important;
            height: 24px !important; /* Reduced from 35px to 28px */
        }

            .claim-header-card .e-textbox .e-input-group .e-input {
                height: 24px !important; /* Reduced from 33px to 26px */
                padding: 0 0.75rem !important;
                font-size: 0.9rem !important;
                line-height: 24px !important; /* Reduced from 33px to 26px */
            }

    /* NumericTextBox specific styles for financial inputs */
    .financial-info-group .e-numerictextbox {
        width: 180px !important;
        height: 28px !important;
    }

        .financial-info-group .e-numerictextbox .e-input-group {
            width: 180px !important;
            height: 28px !important;
        }

            .financial-info-group .e-numerictextbox .e-input-group .e-input {
                height: 26px !important;
                padding: 0 0.75rem !important;
                font-size: 0.9rem !important;
                line-height: 26px !important;
            }

    /* Hover and focus states */
    .form-control:hover,
    .form-control-compact:hover {
        border-color: var(--primary-color);
    }

    .form-control:focus,
    .form-control-compact:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
    }

    .textarea-control {
        min-height: 80px;
        resize: vertical;
    }

    /* Professional Claims specific styles */
    .professional-claim-form {
        max-width: 1400px; /* Slightly wider to accommodate 5 columns */
        margin: 2rem auto;
        padding: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        color: var(--text-color);
    }

    /* Ensure proper spacing for new fields */
    .claim-header-card .form-group:nth-child(4),
    .claim-header-card .form-group:nth-child(5) {
        margin-bottom: 0;
    }
    /* Summary Tabs Section Styles */
    .summary-tabs-section {
        margin-top: 2rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #ddd;
    }

    .summary-tabs {
        width: 100%;
    }

        /* Tab Header */
        .summary-tabs .e-tab-header {
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            border-radius: 6px 6px 0 0;
            padding: 0;
            margin: 0;
        }

            /* Tab Text - Default State */
            .summary-tabs .e-tab-header .e-tab-text {
                font-weight: 500;
                color: #666;
                font-size: 0.9rem;
                padding: 0.75rem 1.5rem;
                background: transparent;
                border: none;
                transition: all 0.2s ease;
            }

            /* Tab Text - Active State */
            .summary-tabs .e-tab-header .e-active .e-tab-text {
                color: #0066cc;
                font-weight: 600;
                background: transparent;
                border-bottom: 2px solid #0066cc;
            }

            /* Tab Text - Hover State */
            .summary-tabs .e-tab-header .e-tab-wrap:hover .e-tab-text {
                color: #0066cc;
                background: rgba(0, 102, 204, 0.05);
            }

            /* Error Tab Special Color */
            .summary-tabs .e-tab-header .e-tab-text:contains("*Error") {
                color: #dc3545;
            }

            .summary-tabs .e-tab-header .e-active .e-tab-text:contains("*Error") {
                color: #dc3545;
                border-bottom-color: #dc3545;
            }

        /* Tab Content Area */
        .summary-tabs .e-content {
            background: white;
            border-radius: 0 0 6px 6px;
            min-height: 200px;
            border: none;
            margin: 0;
            padding: 0;
        }

            .summary-tabs .e-content .e-item {
                padding: 1.5rem;
                background: transparent;
                margin: 0;
            }

    /* Individual Tab Content */
    .summary-content,
    .error-content,
    .claims-logs-content {
        width: 100%;
        min-height: 150px;
    }

    .summary-grid-container,
    .error-grid-container,
    .claims-logs-grid-container {
        width: 100%;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    /* Placeholder Content */
    .summary-placeholder,
    .error-placeholder,
    .claims-logs-placeholder {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 2rem;
    }

        .summary-placeholder p,
        .error-placeholder p,
        .claims-logs-placeholder p {
            margin: 0;
            font-size: 0.95rem;
        }

    /* Color-coded Placeholders */
    .error-placeholder {
        border-left: 4px solid #dc3545;
        background: rgba(220, 53, 69, 0.05);
    }

    .summary-placeholder {
        border-left: 4px solid #0066cc;
        background: rgba(0, 102, 204, 0.05);
    }

    .claims-logs-placeholder {
        border-left: 4px solid #28a745;
        background: rgba(40, 167, 69, 0.05);
    }

    /* Summary Content Layout */
    .summary-content {
        padding: 1.5rem;
        background: white;
    }

    .summary-split-container {
        display: flex;
        gap: 2rem;
        height: auto;
        min-height: auto;
    }

    /* Left Half - Billing Notes */
    .billing-notes-section {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .billing-notes-textarea {
        resize: vertical;
        min-height: 120px !important;
        height: 120px !important;
    }

        /* Syncfusion specific overrides for billing notes textarea */
        .billing-notes-textarea .e-input-group {
            height: 120px !important;
            min-height: 120px !important;
            border: 1px solid var(--medium-gray);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

            .billing-notes-textarea .e-input-group .e-input {
                height: 120px !important;
                min-height: 120px !important;
                padding: 10px !important;
                font-size: 0.95rem;
                line-height: 1.4;
            }

            .billing-notes-textarea .e-input-group textarea {
                height: 110px !important;
                min-height: 110px !important;
                resize: vertical;
            }

            /* Hover and focus states for billing notes textarea */
            .billing-notes-textarea .e-input-group:hover {
                border-color: var(--primary-color);
            }

            .billing-notes-textarea .e-input-group.e-input-focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
            }

    /* Right Half - Amounts Section */
    .amounts-section {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 0;
    }

    .amounts-grid {
        display: flex;
        gap: 3rem;
        justify-content: center;
        align-items: flex-start;
    }

    .amounts-column {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        min-width: 200px;
    }

    .amount-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding: 0.5rem 0;
    }

    .amount-header {
        font-weight: 600;
        font-size: 1.1rem;
        color: var(--primary-dark);
        text-align: center;
        width: 100%;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .amount-label {
        font-weight: 500;
        color: var(--dark-gray);
        font-size: 0.95rem;
        min-width: 80px;
    }

    .amount-input {
        width: 110px !important;
        height: 32px !important;
    }

        /* Syncfusion NumericTextBox specific overrides for amount inputs */
        .amount-input .e-input-group {
            width: 110px !important;
            height: 32px !important;
            border: 1px solid var(--medium-gray);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

            .amount-input .e-input-group .e-input {
                height: 30px !important;
                padding: 0 0.75rem !important;
                font-size: 0.9rem !important;
                line-height: 30px !important;
                text-align: right;
                font-weight: 500;
            }

            /* Hover and focus states for amount inputs */
            .amount-input .e-input-group:hover {
                border-color: var(--primary-color);
            }

            .amount-input .e-input-group.e-input-focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
            }

    /* Responsive design for smaller screens */
        (max-width: 1024px) {
        .summary-split-container

    {
        flex-direction: column;
        gap: 1.5rem;
    }

    .amounts-grid {
        gap: 2rem;
    }

    }

        (max-width: 768px) {
        .amounts-grid

    {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }

    .amounts-column {
        width: 100%;
        max-width: 300px;
    }

    }
    /* Responsive Design */
    (max-width: 768px) {
        .summary-tabs-section

    {
        margin: 1rem 0;
        padding: 0.5rem;
    }

    .summary-tabs .e-tab-header .e-tab-text {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .summary-tabs .e-content .e-item {
        padding: 1rem;
    }

    .summary-grid-container,
    .error-grid-container,
    .claims-logs-grid-container {
        padding: 1rem;
    }

    }
    .compact-textbox .e-input {
        height: 25px !important;
        padding: 2px 8px !important;
        font-size: 13px !important;
    }

    .compact-textbox .e-input-group {
        height: 25px !important;
    }
    /* CPT Section Styles - Add this to your existing CSS */
    .cpt-section {
        margin-bottom: 1.5rem;
    }

    .cpt-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        background: var(--light-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .cpt-search-controls {
        width: 50%;
        max-width: 600px;
        min-width: 500px;
    }

    .cpt-full-width-grid {
        width: 100%;
        border: 1px solid var(--medium-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        /* Grid header styling for CPT */
        .cpt-full-width-grid .e-grid .e-headercell {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.85rem;
            padding: 8px 4px;
        }

        /* Grid row styling for CPT */
        .cpt-full-width-grid .e-grid .e-row {
            border-bottom: 1px solid var(--medium-gray);
        }

            .cpt-full-width-grid .e-grid .e-row:hover {
                background-color: var(--light-gray);
            }

        /* Grid cell styling for CPT */
        .cpt-full-width-grid .e-grid .e-rowcell {
            padding: 6px 8px;
            font-size: 0.85rem;
            vertical-align: middle;
        }

        /* Action buttons styling for CPT */
        .cpt-full-width-grid .e-flat {
            border: none;
            background: transparent;
            padding: 4px;
            margin: 0 2px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

            .cpt-full-width-grid .e-flat:hover {
                background-color: var(--medium-gray);
            }

            .cpt-full-width-grid .e-flat.e-edit:hover {
                background-color: var(--primary-color);
                color: white;
            }

            .cpt-full-width-grid .e-flat.e-delete:hover {
                background-color: var(--danger-color);
                color: white;
            }

            .cpt-full-width-grid .e-flat.e-save:hover {
                background-color: var(--success-color);
                color: white;
            }

            .cpt-full-width-grid .e-flat.e-cancel:hover {
                background-color: var(--warning-color);
                color: white;
            }
    /* ICD Section Styles - Updated for perfect height alignment */
    .icd-section {
        margin-bottom: 1.5rem;
    }

    .icd-container {
        width: 60%;
        max-width: 700px;
        min-width: 600px;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        background: var(--light-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .icd-search-controls {
        width: 100%;
    }

    .search-add-row {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 31px; /* Fixed container height */
    }

        /* MudAutocomplete styling with aggressive height override */
        .search-add-row .mud-autocomplete {
            flex: 0 0 70%;
            max-width: 350px;
            height: 31px !important;
        }

            /* Force all MudBlazor autocomplete elements to 30px height */
            .search-add-row .mud-autocomplete,
            .search-add-row .mud-autocomplete *,
            .search-add-row .mud-autocomplete .mud-input-control,
            .search-add-row .mud-autocomplete .mud-input-control *,
            .search-add-row .mud-autocomplete .mud-input-root,
            .search-add-row .mud-autocomplete .mud-input-root *,
            .search-add-row .mud-autocomplete .mud-input,
            .search-add-row .mud-autocomplete .mud-input-slot,
            .search-add-row .mud-autocomplete .mud-input-adornment,
            .search-add-row .mud-autocomplete .mud-input-outlined,
            .search-add-row .mud-autocomplete .mud-input-outlined-border {
                height: 31px !important;
                min-height: 31px !important;
                max-height: 31px !important;
                line-height: 31px !important;
                box-sizing: border-box !important;
            }

                /* Specific input field styling */
                .search-add-row .mud-autocomplete input {
                    height: 29px !important;
                    min-height: 29px !important;
                    max-height: 29px !important;
                    padding: 0 12px !important;
                    font-size: 0.85rem !important;
                    line-height: 29px !important;
                    border: none !important;
                    outline: none !important;
                }

                /* Remove any margins or padding that might add height */
                .search-add-row .mud-autocomplete .mud-input-control {
                    margin: 0 !important;
                    padding: 0 !important;
                }

                /* Hide the floating label completely */
                .search-add-row .mud-autocomplete .mud-input-label,
                .search-add-row .mud-autocomplete label {
                    display: none !important;
                    visibility: hidden !important;
                    height: 0 !important;
                    opacity: 0 !important;
                }

                /* Override any fieldset or border elements */
                .search-add-row .mud-autocomplete fieldset {
                    height: 31px !important;
                    min-height: 31px !important;
                    top: 0 !important;
                }

                .search-add-row .mud-autocomplete legend {
                    display: none !important;
                    height: 0 !important;
                    font-size: 0 !important;
                    line-height: 0 !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    width: 0 !important;
                }

        /* MudButton styling for perfect alignment */
        .search-add-row .mud-button {
            flex: 0 0 auto;
            min-width: 60px;
            height: 31px !important; /* Force exact height */
            margin: 0;
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1; /* Prevent line-height from affecting button height */
        }

            /* Additional overrides for MudButton internal elements */
            .search-add-row .mud-button .mud-button-label {
                height: auto;
                line-height: 1;
            }

    .icd-compact-grid {
        width: 100%;
        border: 1px solid var(--medium-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        /* Grid header styling */
        .icd-compact-grid .e-grid .e-headercell {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.85rem;
            padding: 8px 4px;
        }

        /* Grid row styling */
        .icd-compact-grid .e-grid .e-row {
            border-bottom: 1px solid var(--medium-gray);
        }

            .icd-compact-grid .e-grid .e-row:hover {
                background-color: var(--light-gray);
            }

        /* Grid cell styling */
        .icd-compact-grid .e-grid .e-rowcell {
            padding: 6px 8px;
            font-size: 0.85rem;
            vertical-align: middle;
        }

        /* Action buttons styling */
        .icd-compact-grid .e-flat {
            border: none;
            background: transparent;
            padding: 4px;
            margin: 0 2px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

            .icd-compact-grid .e-flat:hover {
                background-color: var(--medium-gray);
            }

            .icd-compact-grid .e-flat.e-edit:hover {
                background-color: var(--primary-color);
                color: white;
            }

            .icd-compact-grid .e-flat.e-delete:hover {
                background-color: var(--danger-color);
                color: white;
            }

            .icd-compact-grid .e-flat.e-save:hover {
                background-color: var(--success-color);
                color: white;
            }

            .icd-compact-grid .e-flat.e-cancel:hover {
                background-color: var(--warning-color);
                color: white;
            }


    .immunization-section {
        margin-bottom: 1.5rem;
    }

    .immunization-container {
        width: 50%;
        max-width: 600px;
        min-width: 500px;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
        background: var(--light-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .immunization-search-controls {
        width: 100%;
    }

        .immunization-search-controls .search-add-row {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 31px; /* Fixed container height */
        }

            /* MudAutocomplete styling with aggressive height override for immunization */
            .immunization-search-controls .search-add-row .mud-autocomplete {
                flex: 0 0 70%;
                max-width: 350px;
                height: 31px !important;
            }

                /* Force all MudBlazor autocomplete elements to 30px height */
                .immunization-search-controls .search-add-row .mud-autocomplete,
                .immunization-search-controls .search-add-row .mud-autocomplete *,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-control,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-control *,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-root,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-root *,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-slot,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-adornment,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-outlined,
                .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-outlined-border {
                    height: 31px !important;
                    min-height: 31px !important;
                    max-height: 31px !important;
                    line-height: 31px !important;
                    box-sizing: border-box !important;
                }

                    /* Specific input field styling */
                    .immunization-search-controls .search-add-row .mud-autocomplete input {
                        height: 29px !important;
                        min-height: 29px !important;
                        max-height: 29px !important;
                        padding: 0 12px !important;
                        font-size: 0.85rem !important;
                        line-height: 29px !important;
                        border: none !important;
                        outline: none !important;
                    }

                    /* Remove any margins or padding that might add height */
                    .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-control {
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    /* Hide the floating label completely */
                    .immunization-search-controls .search-add-row .mud-autocomplete .mud-input-label,
                    .immunization-search-controls .search-add-row .mud-autocomplete label {
                        display: none !important;
                        visibility: hidden !important;
                        height: 0 !important;
                        opacity: 0 !important;
                    }

                    /* Override any fieldset or border elements */
                    .immunization-search-controls .search-add-row .mud-autocomplete fieldset {
                        height: 31px !important;
                        min-height: 31px !important;
                        top: 0 !important;
                    }

                    .immunization-search-controls .search-add-row .mud-autocomplete legend {
                        display: none !important;
                        height: 0 !important;
                        font-size: 0 !important;
                        line-height: 0 !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        width: 0 !important;
                    }

            /* MudButton styling for perfect alignment */
            .immunization-search-controls .search-add-row .mud-button {
                flex: 0 0 auto;
                min-width: 60px;
                height: 31px !important; /* Force exact height */
                margin: 0;
                padding: 0 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 1; /* Prevent line-height from affecting button height */
            }

                /* Additional overrides for MudButton internal elements */
                .immunization-search-controls .search-add-row .mud-button .mud-button-label {
                    height: auto;
                    line-height: 1;
                }

    .immunization-compact-grid {
        width: 100%;
        border: 1px solid var(--medium-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

        /* Grid header styling */
        .immunization-compact-grid .e-grid .e-headercell {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.85rem;
            padding: 8px 4px;
        }

        /* Grid row styling */
        .immunization-compact-grid .e-grid .e-row {
            border-bottom: 1px solid var(--medium-gray);
        }

            .immunization-compact-grid .e-grid .e-row:hover {
                background-color: var(--light-gray);
            }

        /* Grid cell styling */
        .immunization-compact-grid .e-grid .e-rowcell {
            padding: 6px 8px;
            font-size: 0.85rem;
            vertical-align: middle;
        }

        /* Action buttons styling */
        .immunization-compact-grid .e-flat {
            border: none;
            background: transparent;
            padding: 4px;
            margin: 0 2px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

            .immunization-compact-grid .e-flat:hover {
                background-color: var(--medium-gray);
            }

            .immunization-compact-grid .e-flat.e-edit:hover {
                background-color: var(--primary-color);
                color: white;
            }

            .immunization-compact-grid .e-flat.e-delete:hover {
                background-color: var(--danger-color);
                color: white;
            }

            .immunization-compact-grid .e-flat.e-save:hover {
                background-color: var(--success-color);
                color: white;
            }

            .immunization-compact-grid .e-flat.e-cancel:hover {
                background-color: var(--warning-color);
                color: white;
            }

    /* Remove the optional additional padding section */
</style>