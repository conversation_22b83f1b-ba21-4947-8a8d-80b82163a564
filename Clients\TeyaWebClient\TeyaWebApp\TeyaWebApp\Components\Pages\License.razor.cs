using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public class UserDisplayInfo
    {
        public string Name { get; set; } = "N/A";
        public string Email { get; set; } = "N/A";
        public string DisplayText { get; set; } = "N/A";
    }

    public partial class License
    {
        private string? errorMessage = null;
        private string? username;
        private const int ten = 10;
        private int organizationCounter = 1;
        private string organizationSearchText = string.Empty;

        private Organization Organization { get; set; } = new Organization();
        private UserLicense UserLicense { get; set; } = new UserLicense();
        private List<Organization> Organizations = new();
        private List<PlanType> PlanTypes = new();
        private List<Product> Products = new();
        private List<Organization> OrganizationList = new();
        private List<PlanType> PlanList = new();
        private List<Product> ProductList = new();
        private List<UserLicense> UserLicenses = new();
        private List<Organization> ActiveOrganizations = new();
        private List<UserLicense> ActiveUserLicenses = new();
        private SfGrid<UserLicense>? licenseGrid;
        public string[] ToolBarItems = new string[] { "Edit", "Delete", "Cancel" };

        [Inject] private ActiveUser User { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        [Inject] private ILogger<License> Logger { get; set; }

        private List<Organization> FilteredOrganizations
        {
            get
            {
                organizationCounter = 1;
                if (string.IsNullOrEmpty(organizationSearchText))
                {
                    return ActiveOrganizations;
                }
                return ActiveOrganizations.Where(org =>
                    org.OrganizationName.Contains(organizationSearchText, StringComparison.OrdinalIgnoreCase))
                    .ToList();
            }
        }

        /// <summary>
        /// Gets the organization name by organization ID.
        /// </summary>
        /// <param name="organizationId">The organization ID.</param>
        /// <returns>The organization name.</returns>
        private string GetOrganizationName(Guid? orgId)
        {
            if (orgId == null || orgId == Guid.Empty)
                return "N/A";

            var org = Organizations.FirstOrDefault(o => o.OrganizationId == orgId);
            return org?.OrganizationName ?? "N/A";
        }

        /// <summary>
        /// Gets the organization email by organization ID.
        /// </summary>
        /// <param name="organizationId">The organization ID.</param>
        /// <returns>The organization email.</returns>
        private string GetOrganizationEmail(Guid? orgId)
        {
            if (orgId == null || orgId == Guid.Empty)
                return "N/A";

            var org = Organizations.FirstOrDefault(o => o.OrganizationId == orgId);
            return org?.Email ?? "N/A";
        }

        /// <summary>
        /// Gets user display information including name and email.
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>User display information.</returns>
        private UserDisplayInfo GetUserDisplayInfo(Guid? userId)
        {
            if (userId == null || userId == Guid.Empty)
            {
                return new UserDisplayInfo
                {
                    Name = "N/A",
                    Email = "N/A",
                    DisplayText = "N/A"
                };
            }

            if (userId.ToString() == User.id)
            {
                var displayName = !string.IsNullOrEmpty(User.displayName) ? User.displayName : "Current User";
                var email = !string.IsNullOrEmpty(User.mail) ? User.mail : "N/A";

                return new UserDisplayInfo
                {
                    Name = displayName,
                    Email = email,
                    DisplayText = $"{displayName} ({email})"
                };
            }

            // For other users, you might want to fetch their details from a user service
            // For now, returning a generic display
            return new UserDisplayInfo
            {
                Name = "Unknown User",
                Email = "N/A",
                DisplayText = "Unknown User"
            };
        }

        /// <summary>
        /// Gets the display name of the user (for grid display).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>The display name of the user.</returns>
        private string GetUserDisplayName(Guid? userId)
        {
            var userInfo = GetUserDisplayInfo(userId);
            return userInfo.DisplayText;
        }

        /// <summary>
        /// Initializes the component and loads the necessary data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadMembersAsync();
                await GetActiveOrganizationsList();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error : {ex.Message}");
                Snackbar.Add($"Error initializing component: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// Loads members data asynchronously.
        /// </summary>
        private async Task LoadMembersAsync()
        {
            try
            {
                username = User.id;

                // Load all data in parallel for better performance
                var organizationsTask = OrganizationService.GetAllOrganizationsAsync();
                var planTypesTask = PlanTypeService.GetAllPlanTypesAsync();
                var productsTask = ProductService.GetProductsAsync();
                var userLicensesTask = UserLicenseService.GetAllUserLicensesAsync();

                await Task.WhenAll(organizationsTask, planTypesTask, productsTask, userLicensesTask);

                Organizations = await organizationsTask;
                PlanTypes = (await planTypesTask).ToList();
                Products = (await productsTask).ToList();
                UserLicenses = (await userLicensesTask).ToList();

                // Create lookup lists for foreign key relationships
                OrganizationList = Organizations.Select(o => new Organization
                {
                    OrganizationId = o.OrganizationId,
                    OrganizationName = o.OrganizationName
                }).ToList();

                PlanList = PlanTypes.Select(p => new PlanType
                {
                    Id = p.Id,
                    PlanName = p.PlanName
                }).ToList();

                ProductList = Products.Select(pr => new Product
                {
                    Id = pr.Id,
                    Name = pr.Name
                }).ToList();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading data: {ex.Message}");
                Snackbar.Add($"Error loading data: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// Gets the list of active organizations asynchronously.
        /// </summary>
        private async Task GetActiveOrganizationsList()
        {
            try
            {
                errorMessage = null;
                ActiveOrganizations = Organizations.Where(org => org.IsActive).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingOrganizations"], ex.Message);
                Snackbar.Add(errorMessage, Severity.Error);
            }
        }

        private Guid _selectedOrganization;
        public Guid selectedOrganization
        {
            get => _selectedOrganization;
            set
            {
                _selectedOrganization = value;
                _ = OnOrganizationChanged(value);
            }
        }

        /// <summary>
        /// Handles the event when the selected organization is changed.
        /// </summary>
        /// <param name="selectedOrganization">The selected organization ID.</param>
        private async Task OnOrganizationChanged(Guid selectedOrganization)
        {
            if (selectedOrganization == Guid.Empty) return;

            try
            {
                Logger.LogInformation("Fetching licenses for organization: {OrganizationId}", selectedOrganization);
                var license = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(selectedOrganization);

                ActiveUserLicenses.Clear();

                if (license != null)
                {
                    ActiveUserLicenses.Add(license);
                    Logger.LogInformation("Fetched license for organization: {OrganizationId}", selectedOrganization);
                }
                else
                {
                    Logger.LogWarning("No license found for organization: {OrganizationId}", selectedOrganization);
                    var freeLicense = await AddFreeUserLicense(selectedOrganization);
                    if (freeLicense != null)
                    {
                        ActiveUserLicenses.Add(freeLicense);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading license for organization: {OrganizationId}", selectedOrganization);
                Snackbar.Add($"Error loading license: {ex.Message}", Severity.Error);
                ActiveUserLicenses.Clear();
            }
            finally
            {
                if (licenseGrid != null)
                {
                    await licenseGrid.Refresh();
                }
                StateHasChanged();
            }
        }

        /// <summary>
        /// Adds a free user license for the specified organization.
        /// </summary>
        /// <param name="selectedOrganization">The selected organization ID.</param>
        private async Task<UserLicense?> AddFreeUserLicense(Guid selectedOrganization)
        {
            UserLicense? result = null;

            try
            {
                if (selectedOrganization != Guid.Empty)
                {
                    var freePlan = PlanTypes.FirstOrDefault(p => p.PlanName.Equals("Free", StringComparison.OrdinalIgnoreCase));
                    if (freePlan == null)
                    {
                        Logger.LogWarning("No 'Free' plan found in the system");
                        Snackbar.Add("No 'Free' plan available in the system", Severity.Warning);
                        return null;
                    }

                    var freeUserLicense = new UserLicense
                    {
                        Id = Guid.NewGuid(),
                        PlanId = freePlan.Id,
                        OrganizationId = selectedOrganization,
                        ProductId = Products.FirstOrDefault()?.Id,
                        Seats = ten,
                        ActiveUsers = 0,
                        CreatedDate = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(username),
                        Status = true,
                        ExpiryDate = DateTime.UtcNow.AddDays(14)
                    };

                    await UserLicenseService.AddUserLicenseAsync(freeUserLicense);
                    Logger.LogInformation("Free license added for organization: {OrganizationId}", selectedOrganization);
                    Snackbar.Add("Free license added successfully", Severity.Success);
                    await RefreshActiveUserLicenses();
                    result = freeUserLicense;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error adding free license for organization: {OrganizationId}", selectedOrganization);
                Snackbar.Add($"Error adding free license: {ex.Message}", Severity.Error);
            }

            return result;
        }

        /// <summary>
        /// Refreshes the list of active user licenses.
        /// </summary>
        private async Task RefreshActiveUserLicenses()
        {
            try
            {
                if (selectedOrganization != Guid.Empty)
                {
                    ActiveUserLicenses.Clear();

                    var license = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(selectedOrganization);

                    if (license != null)
                    {
                        ActiveUserLicenses.Add(license);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error refreshing active user licenses: {ex.Message}");
                Snackbar.Add($"Error refreshing licenses: {ex.Message}", Severity.Error);
            }
            finally
            {
                if (licenseGrid != null)
                {
                    await licenseGrid.Refresh();
                }
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles the action begin event for the grid.
        /// </summary>
        /// <param name="args">The action event arguments.</param>
        private async Task OnActionBegin(ActionEventArgs<UserLicense> args)
        {
            try
            {
                Logger.LogInformation("Action triggered: {ActionType}", args.RequestType);

                if (args.RequestType == Syncfusion.Blazor.Grids.Action.BeginEdit)
                {
                    Logger.LogInformation("Editing user license with {Seats} seats", args.RowData?.Seats);

                    if (licenseGrid != null)
                    {
                        await licenseGrid.HideColumnsAsync(new string[] { "CreatedBy", "UpdatedBy" });
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    args.Cancel = true;

                    // Validate the data before saving
                    if (!ValidateUserLicense(args.Data))
                    {
                        await RefreshActiveUserLicenses();
                        return;
                    }

                    var result = await ShowSaveConfirmationDialog(args.Data);
                    if (result)
                    {
                        Logger.LogInformation("Updating user license with {Seats} seats", args.Data?.Seats);

                        args.Data.UpdatedBy = Guid.Parse(username);
                        args.Data.UpdatedDate = DateTime.UtcNow;

                        await UpdateUserLicense(args.Data);
                    }
                    else
                    {
                        await RefreshActiveUserLicenses();
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    args.Cancel = true;

                    var result = await ShowDeleteConfirmationDialog(args.Data);
                    if (result)
                    {
                        args.Data.Status = false;
                        args.Data.UpdatedBy = Guid.Parse(username);
                        args.Data.UpdatedDate = DateTime.UtcNow;

                        Logger.LogInformation("Deactivating user license with {Seats} seats", args.Data?.Seats);
                        await UpdateUserLicense(args.Data);
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Cancel)
                {
                    if (licenseGrid != null)
                    {
                        await licenseGrid.ShowColumnsAsync(new string[] { "CreatedBy", "UpdatedBy" });
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in OnActionBegin");
                Snackbar.Add($"Error in action: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// Validates the user license data.
        /// </summary>
        /// <param name="license">The user license to validate.</param>
        /// <returns>True if valid, false otherwise.</returns>
        private bool ValidateUserLicense(UserLicense license)
        {
            if (license == null)
            {
                Snackbar.Add("License data is null", Severity.Error);
                return false;
            }

            if (license.Seats <= 0)
            {
                Snackbar.Add("Seats must be greater than 0", Severity.Error);
                return false;
            }

            if (license.PlanId == Guid.Empty)
            {
                Snackbar.Add("Plan must be selected", Severity.Error);
                return false;
            }

            if (license.OrganizationId == Guid.Empty)
            {
                Snackbar.Add("Organization must be selected", Severity.Error);
                return false;
            }

            if (license.ExpiryDate <= DateTime.UtcNow)
            {
                Snackbar.Add("Expiry date must be in the future", Severity.Error);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Shows a save confirmation dialog.
        /// </summary>
        /// <param name="license">The user license being saved.</param>
        /// <returns>True if user confirms, false otherwise.</returns>
        private async Task<bool> ShowSaveConfirmationDialog(UserLicense license)
        {
            bool result = false;

            try
            {
                var orgName = GetOrganizationName(license.OrganizationId);
                var planName = PlanTypes.FirstOrDefault(p => p.Id == license.PlanId)?.PlanName ?? "Unknown";

                var message = $"Are you sure you want to save changes to the license for {orgName}?\n" +
                             $"Plan: {planName}\n" +
                             $"Seats: {license.Seats}";

                bool? confirmed = await DialogService.ShowMessageBox(
                    "Confirm Save",
                    message,
                    yesText: "Save",
                    cancelText: "Cancel");

                result = confirmed == true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing save dialog");
                Snackbar.Add($"Error showing save dialog: {ex.Message}", Severity.Error);
            }

            return result;
        }

        /// <summary>
        /// Shows a delete confirmation dialog.
        /// </summary>
        /// <param name="license">The user license being deleted.</param>
        /// <returns>True if user confirms, false otherwise.</returns>
        private async Task<bool> ShowDeleteConfirmationDialog(UserLicense license)
        {
            bool result = false;

            try
            {
                var orgName = GetOrganizationName(license.OrganizationId);
                var planName = PlanTypes.FirstOrDefault(p => p.Id == license.PlanId)?.PlanName ?? "Unknown";

                var message = $"Are you sure you want to deactivate the license for {orgName}?\n" +
                             $"Plan: {planName}\n" +
                             $"This will set the license status to Inactive.";

                bool? confirmed = await DialogService.ShowMessageBox(
                    "Confirm Delete",
                    message,
                    yesText: "Delete",
                    cancelText: "Cancel");

                result = confirmed == true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing delete dialog");
                Snackbar.Add($"Error showing delete dialog: {ex.Message}", Severity.Error);
            }

            return result;
        }

        /// <summary>
        /// Handles the action complete event for the grid.
        /// </summary>
        /// <param name="args">The action event arguments.</param>
        private void OnActionComplete(ActionEventArgs<UserLicense> args)
        {
            Logger.LogInformation("Action completed: {ActionType}", args.RequestType);
        }

        /// <summary>
        /// Updates the user license.
        /// </summary>
        /// <param name="license">The user license to update.</param>
        private async Task UpdateUserLicense(UserLicense license)
        {
            try
            {
                await UserLicenseService.UpdateUserLicenseAsync(license);
                await RefreshActiveUserLicenses();

                Logger.LogInformation("User license updated successfully");
                Snackbar.Add("User license updated successfully", Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error updating user license: {ex.Message}");
                Snackbar.Add($"Error updating license: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// Refreshes the grid.
        /// </summary>
        private async Task RefreshGrid()
        {
            if (licenseGrid != null)
            {
                await licenseGrid.Refresh();
            }
            await InvokeAsync(StateHasChanged);
        }

        private Organization? SelectedOrgObject
        {
            get => ActiveOrganizations.FirstOrDefault(o => o.OrganizationId == selectedOrganization);
            set
            {
                if (value != null)
                {
                    selectedOrganization = value.OrganizationId;
                }
                else
                {
                    selectedOrganization = Guid.Empty;
                    ActiveUserLicenses.Clear();
                    StateHasChanged();
                }
            }
        }

        private Task<IEnumerable<Organization>> SearchOrganizations(string value, CancellationToken token)
        {
            if (string.IsNullOrWhiteSpace(value))
                return Task.FromResult(ActiveOrganizations.AsEnumerable());

            var result = ActiveOrganizations
                .Where(x => x.OrganizationName.Contains(value, StringComparison.InvariantCultureIgnoreCase))
                .AsEnumerable();

            return Task.FromResult(result);
        }
    }
}