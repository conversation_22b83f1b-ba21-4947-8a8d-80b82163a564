﻿@page "/officevisit"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "VisitAccessPolicy")]
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using MudBlazor
@using TeyaWebApp.Components.Layout
@using TeyaUIModels.Model
@inject TeyaUIViewModels.ViewModel.IOfficeVisitService VisitService
@inject IMemberService MemberService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@layout Admin

<PageTitle>@Localizer["Office Visits"]</PageTitle>

<div class="office-visit-wrapper">
    <div class="d-flex align-items-center mb-4">
        <MudIcon Icon="@Icons.Material.Filled.MedicalInformation" Size="Size.Medium" Class="me-2 text-primary" />
        <h3 class="m-0">Office Visit</h3>
    </div>
    <div class="visit-card sf-box">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="filter-controls">
                <div class="filter-date-card">
                    <div class="form-group">
                        <SfDatePicker @bind-Value="@SelectedFilterDate"
                                      Format="MM-dd-yyyy"
                                      Width="180px"
                                      Placeholder="Filter By Date"
                                      AllowEdit="false"
                                      ShowClearButton="true"></SfDatePicker>
                    </div>
                </div>
                <div class="filter-name-card">
                    <div class="form-group">
                        <SfTextBox @bind-Value="@SelectedFilterName"
                                   Width="180px"
                                   Placeholder="Filter By Patient Name"
                                   ShowClearButton="true"></SfTextBox>
                    </div>
                </div>
            </div>
        </div>

        @if (FilteredOfficeVisits.Any())
        {
            <SfGrid DataSource="@FilteredOfficeVisits" AllowPaging="true" GridLines="GridLine.Both"  AllowTextWrap="false"
                    CssClass="custom-grid fixed-columns">

                <GridPageSettings PageSize="10" PageSizes="true" />

                <GridColumns>
                    <GridColumn Field=@nameof(OfficeVisitModel.MRN) HeaderText="@Localizer["MRN"]" Width="100" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left" />
                    <GridColumn Field=@nameof(OfficeVisitModel.VisitType) HeaderText="@Localizer["Visit Type"]" Width="100" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var visitType = visit.VisitType ?? "";
                            }
                            <div class="truncated-cell" title="@visitType">
                                @visitType
                            </div>
                        </Template>
                    </GridColumn>
                    <GridColumn Field=@nameof(OfficeVisitModel.AppointmentTime) HeaderText="@Localizer["Appointment Time"]" Format="H:mm" Width="140" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center" />

                    <GridColumn Field=@nameof(OfficeVisitModel.AppointmentDate) HeaderText="@Localizer["Appointment Date"]" Format="MM-dd-yyyy" Width="140" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center" />

                    <GridColumn HeaderText="@Localizer["Patient Name"]" Width="150" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var patientName = visit.PatientName ?? "";
                            }
                            @if (!string.IsNullOrEmpty(visit.PatientName))
                            {
                                <div class="truncated-cell clickable-name" title="@patientName" @onclick="() => RedirectToChart(visit.Id, visit.VisitStatus, visit.VisitType,visit.AppointmentsId,visit.PCPID)">
                                    @patientName
                                </div>
                            }
                            else
                            {
                                <span class="placeholder-text">N/A</span>
                            }
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.PR) HeaderText="@Localizer["P/R"]" Width="100" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var pr = visit.PR ?? "";
                            }
                            <div class="truncated-cell" title="@pr">
                                @pr
                            </div>
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.Reason) HeaderText="@Localizer["Reason"]" Width="120" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var reason = visit.Reason ?? "";
                            }
                            <div class="truncated-cell" title="@reason">
                                @reason
                            </div>
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.Notes) HeaderText="@Localizer["Notes"]" Width="130" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var notes = visit.Notes ?? "";
                            }
                            <div class="truncated-cell" title="@notes">
                                @notes
                            </div>
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.Sex) HeaderText="@Localizer["Sex"]" Width="90" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var sex = visit.Sex ?? "";
                            }
                            <div class="truncated-cell" title="@sex">
                                @sex
                            </div>
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.Dob) HeaderText="@Localizer["Date of Birth"]" Format="MMM dd, yyyy" Width="130" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center" />

                    <GridColumn Field=@nameof(OfficeVisitModel.VisitStatus) HeaderText="@Localizer["Visit Status"]" Width="110" TextAlign="TextAlign.Left" HeaderTextAlign="TextAlign.Left">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var visitStatus = visit.VisitStatus ?? "";
                            }
                            <div class="truncated-cell" title="@visitStatus">
                                @visitStatus
                            </div>
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.ArrivalTime) HeaderText="@Localizer["Arrival Time"]" Format="H:mm" Width="120" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center" />

                    <GridColumn Field=@nameof(OfficeVisitModel.Duration) HeaderText="@Localizer["Duration"]" Width="100" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var duration = visit.Duration ?? "";
                            }
                            <div class="truncated-cell" title="@duration">
                                @duration
                            </div>
                        </Template>
                    </GridColumn>

                    <GridColumn Field=@nameof(OfficeVisitModel.RoomNumber) HeaderText="@Localizer["Room Number"]" Width="120" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Center">
                        <Template>
                            @{
                                var visit = (OfficeVisitModel)context;
                                var roomNumber = visit.RoomNumber ?? "";
                            }
                            <div class="truncated-cell" title="@roomNumber">
                                @roomNumber
                            </div>
                        </Template>
                    </GridColumn>
                </GridColumns>

            </SfGrid>
        }
        else
        {
            <div class="no-records-container">
                <div class="no-records-content">
                    <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Large" Class="text-muted mb-3" />
                    <h5 class="text-muted mb-2">@Localizer["No Records Found"]</h5>
                    <p class="text-muted small">
                        @if (SelectedFilterDate.HasValue || !string.IsNullOrWhiteSpace(SelectedFilterName))
                        {
                            @Localizer["No appointments found for the selected filters."]
                        }
                        else
                        {
                            @Localizer["No office visits available."]
                        }
                    </p>
                </div>
            </div>
        }
    </div>
</div>


<style>
    .office-visit-wrapper {
        padding: 1rem 1.5rem;
        background-color: #f9f9f9;
        min-height: 100vh;
    }

    .visit-card {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .filter-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .filter-date-card,
    .filter-name-card {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

        .filter-date-card .form-group,
        .filter-name-card .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-date-card .form-label,
        .filter-name-card .form-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 0;
        }

        .filter-date-card .e-datepicker,
        .filter-name-card .e-textbox {
            width: 180px !important;
        }

            .filter-date-card .e-datepicker .e-input-group,
            .filter-name-card .e-textbox .e-input-group {
                width: 180px !important;
                height: 28px !important;
                border: 1px solid #d1d5db !important;
                border-radius: 4px !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                background-color: white !important;
            }

                .filter-date-card .e-datepicker .e-input-group:hover,
                .filter-name-card .e-textbox .e-input-group:hover {
                    border-color: #9ca3af !important;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15) !important;
                }

                .filter-date-card .e-datepicker .e-input-group:focus-within,
                .filter-name-card .e-textbox .e-input-group:focus-within {
                    border-color: var(--mud-palette-primary) !important;
                    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
                }

                .filter-date-card .e-datepicker .e-input-group .e-input,
                .filter-name-card .e-textbox .e-input-group .e-input {
                    height: 26px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    line-height: 26px !important;
                    border: none !important;
                    background: transparent !important;
                    box-shadow: none !important;
                }

                    .filter-date-card .e-datepicker .e-input-group .e-input::placeholder,
                    .filter-name-card .e-textbox .e-input-group .e-input::placeholder {
                        color: #999 !important;
                        font-size: 0.85rem !important;
                    }

            /* Clear button styling */
            .filter-date-card .e-datepicker .e-clear-icon,
            .filter-name-card .e-textbox .e-clear-icon {
                font-size: 12px !important;
                color: #666 !important;
            }

                .filter-date-card .e-datepicker .e-clear-icon:hover,
                .filter-name-card .e-textbox .e-clear-icon:hover {
                    color: var(--danger-color) !important;
                }

    .visit-summary {
        font-size: 0.9rem;
        font-weight: 500;
    }

    .no-records-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        padding: 2rem;
    }

    .no-records-content {
        text-align: center;
        max-width: 400px;
    }

    .custom-grid .e-gridheader {
        background-color: #f0f4f8;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .custom-grid .e-gridcontent {
        font-size: 0.9rem;
    }

    .fixed-columns .e-gridcontent .e-rowcell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 12px;
    }

    .fixed-columns .e-gridheader .e-headercell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 12px;
    }

    .truncated-cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        display: block;
        cursor: default;
    }

    .clickable-name {
        color: var(--mud-palette-primary);
        font-weight: 500;
        cursor: pointer;
    }

        .clickable-name:hover {
            text-decoration: underline;
        }

    .placeholder-text {
        color: #999;
        font-style: italic;
    }

    .truncated-cell[title]:hover {
        position: relative;
    }

        .truncated-cell[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.85rem;
            white-space: normal;
            word-wrap: break-word;
            max-width: 300px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            margin-bottom: 5px;
            line-height: 1.3;
        }

        .truncated-cell[title]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #333;
            z-index: 1000;
        }

    .d-flex {
        display: flex;
    }

    .align-items-center {
        align-items: center;
    }

    .me-2 {
        margin-right: 0.5rem;
    }

    .mb-4 {
        margin-bottom: 1.5rem;
    }

    .mb-3 {
        margin-bottom: 1rem;
    }

    .text-primary {
        color: var(--mud-palette-primary);
    }

    .text-muted {
        color: #6c757d;
    }

    .small {
        font-size: 0.875rem;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    @@media (max-width: 768px) {
        .visit-card {
            padding: 1rem;
        }

        .filter-controls {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .filter-date-card,
        .filter-name-card {
            width: 100%;
        }

            .filter-date-card .e-datepicker,
            .filter-name-card .e-textbox {
                width: 100% !important;
            }

                .filter-date-card .e-datepicker .e-input-group,
                .filter-name-card .e-textbox .e-input-group {
                    width: 100% !important;
                }

        .custom-grid .e-gridcontent,
        .custom-grid .e-gridheader {
            font-size: 0.85rem;
        }

        .truncated-cell[title]:hover::after {
            max-width: 250px;
            font-size: 0.8rem;
        }

        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }

        .visit-summary {
            order: 2;
        }
    }
</style>