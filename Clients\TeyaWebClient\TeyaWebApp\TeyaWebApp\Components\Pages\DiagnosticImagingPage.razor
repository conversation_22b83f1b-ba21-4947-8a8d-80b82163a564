﻿@page "/diagnosticimagingpage"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@using System.Text.Json
@using Syncfusion.Blazor.Grids
@using TeyaWebApp.Components.Layout
@using Syncfusion.Blazor.Inputs
@using TeyaWebApp.TeyaAIScribeResources
@inject ISnackbar Snackbar
@using MudBlazor
@inject HttpClient Http
@inject IAssessmentsService AssessmentsService
@inject IDiagnosticImagingPageService DiagnosticImagingPageService

<div class="py-4">
          <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
                <MudGrid>
                    <MudItem xs="12">
                <MudText Typo="Typo.h6" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Diagnostic Imaging</MudText>
                    </MudItem>
                    <MudItem xs="12">
                         <MudPaper Class="pa-2">
                             <MudGrid Spacing="2">
                                <MudItem xs="12">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Status</MudText>
                                </MudItem>
                                <MudItem xs="12" Class="d-flex align-center">
                            <MudRadioGroup T="string" @bind-Value="DiagnosticImageData.Status" >
                                <MudRadio T="string" Value="Open" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Open</MudRadio>
                                <MudRadio T="string" Value="Reviewed" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Reviewed</MudRadio>
                                    </MudRadioGroup>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Provider</MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Facility</MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Assigned To</MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <SfTextBox @bind-Value="DiagnosticImageData.Provider" Dense />
                                </MudItem>
                                <MudItem xs="4">
                            <SfTextBox @bind-Value="DiagnosticImageData.Facility" Dense />
                                </MudItem>
                                <MudItem xs="4">
                                    <SfTextBox @bind-Value="DiagnosticImageData.AssignedTo" Dense />
                                </MudItem>
                                <MudItem xs="4">
                            <MudCheckBox T="bool" Label="Future Order" @bind-Value="DiagnosticImageData.FutureOrder" />
                                </MudItem>
                                <MudItem xs="4">
                            <MudCheckBox T="bool" Label="High Priority" @bind-Value="DiagnosticImageData.HigherPriority" />
                                </MudItem>
                            
                                <MudItem xs="4">
                            <MudCheckBox T="bool" Label="In House" @bind-Value="DiagnosticImageData.InHouse" />
                                </MudItem>
                            </MudGrid>
                         </MudPaper>
                    </MudItem>
                    @* procedgure info *@
                    <MudItem xs="6">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="2">
                                <MudItem xs="12">
                            <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Procedure Information</MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Procedure</MudText>
                                </MudItem>
                                <MudItem xs="12">
                            <SfTextBox @bind-Value="DiagnosticImageData.Procedgure" Dense />
                                </MudItem>
                                <MudItem xs="6">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Order Date</MudText>
                                </MudItem>
                                <MudItem xs="6">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Reason</MudText>
                                </MudItem>
                                <MudItem xs="6">
                             <SfDatePicker @bind-Value="DiagnosticImageData.OrderDate" Dense />
                                </MudItem>
                                <MudItem xs="6">
                                    <SfTextBox @bind-Value="DiagnosticImageData.Reason" Dense />
                                </MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    @* Results info *@
                    <MudItem xs="6">
                        <MudPaper Class="pa-2" Height="170px">
                            <MudGrid Spacing="2">
                                <MudItem xs="12">
                            <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Results</MudText>
                                </MudItem>
                        <MudItem xs="12">
                            <MudCheckBox T="bool" Label="Recieved" @bind-Value="DiagnosticImageData.Recieved" />
                        </MudItem>
                        <MudItem xs="6">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Date</MudText>
                        </MudItem>
                        <MudItem xs="6">
                            <MudText Typo="Typo.body1" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Result</MudText>
                        </MudItem>
                        <MudItem xs="6">
                            <SfDatePicker @bind-Value="DiagnosticImageData.Date" Dense />
                        </MudItem>
                        <MudItem xs="6">
                            <SfTextBox @bind-Value="DiagnosticImageData.Result" Dense />
                        </MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    @* Assessment info *@
                    <MudItem xs="6">
                <MudPaper Class="pa-2" Style="height: 340px;">
                            <MudGrid Spacing="2">
                                <MudItem xs="12">
                            <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Assessment</MudText>
                                </MudItem>
                                <MudItem xs="12">
                                <SfGrid @ref="AssessmentGrid" DataSource="@assessmentData" AllowPaging="true">
                                    <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                                    <GridPageSettings PageSize="5"></GridPageSettings>
                                    <GridColumns>
                                        <GridColumn Field="AssessmentsID" IsPrimaryKey="true" Visible="false"></GridColumn>
                                        <GridColumn Type="ColumnType.CheckBox" Width="50"></GridColumn>
                                        <GridColumn Field="Diagnosis" HeaderText="Diagnosis" Width="100"></GridColumn>
                                        <GridColumn Field="Specify" HeaderText="Specify" Width="200"></GridColumn>
                                    </GridColumns>
                                </SfGrid>
                                </MudItem>
                                
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    @* Clinical info *@
                    <MudItem xs="6">
                <MudPaper Class="pa-4" Style="height: 340px;">
                            <MudGrid Spacing="2">
                        <MudItem xs="12" Class="mb-2">
                            <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Notes</MudText>
                                    <MudTextField Lines="1" Variant="Variant.Outlined" T="string" @bind-Value="DiagnosticImageData.Notes" />
                                </MudItem>
                        <MudItem xs="12" Class="mb-2">
                            <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Classical Information</MudText>
                                    <MudTextField Lines="1" Variant="Variant.Outlined" T="string" @bind-Value="DiagnosticImageData.ClassicalInfo" />
                                </MudItem>
                        <MudItem xs="12" Class="mb-2">
                            <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">Internal Notes</MudText>
                                    <MudTextField Lines="1" Variant="Variant.Outlined" T="string" @bind-Value="DiagnosticImageData.InternalNotes" />
                                </MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="12">
                <div class="action-buttons" style="display: flex; align-items: center; padding: 1rem;width: 100%;">
                    <div style="display: flex; gap: 0.5rem;">
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Reports"]</MudButton>
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Products"]</MudButton>
                        <MudButton Variant="Variant.Outlined" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Emark ECG"]</MudButton>
                        <MudButton Variant="Variant.Outlined" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Send Mail"]</MudButton>
                        <MudButton Variant="Variant.Outlined" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">@Localizer["Options"]</MudButton>
                    </div>

                    <div style="display: flex; gap: 0.5rem; margin-left: auto;">
                        <MudButton style="width: 80px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Outlined" Color="Color.Primary" OnClick="CancelChanges">@Localizer["Cancel"]</MudButton>
                        <MudButton style="width: 80px;font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;" Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveChanges">@Localizer["OK"]</MudButton>
                    </div>
                </div>

                </MudItem>

                </MudGrid>
          </MudContainer>
</div>

