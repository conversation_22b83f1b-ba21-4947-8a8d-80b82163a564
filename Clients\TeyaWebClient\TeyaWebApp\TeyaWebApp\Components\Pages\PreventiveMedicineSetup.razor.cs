﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Unity;
using TeyaWebApp.TeyaAIScribeResources;
using System.Threading.Tasks;

namespace TeyaWebApp.Components.Pages
{
    public partial class PreventiveMedicineSetup : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject] private IPreventiveMedicineService PreventiveMedicineService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        private List<PMCategory> categories = new();

        private Guid _selectedCategoryId = Guid.Empty;
        private Guid _selectedSubCategoryId = Guid.Empty;
        private Guid _selectedCategoryIdForSymptom = Guid.Empty;
        private Guid _selectedSubCategoryIdForSymptom = Guid.Empty;

        private PMCategory _newCategory = new();
        private PMSubCategory _newSubCategory = new();
        private PMSymptoms _newSymptom = new();

        private List<PMSubCategory> _filteredSubCategoriesForSymptom = new();

        private List<PMSubCategory> _filteredSubCategories = new();
        private List<PMSymptoms> _filteredSymptoms = new();

        private enum FormType { None, Category, Subcategory, Symptom }
        private FormType activeForm = FormType.None;

        protected override async Task OnInitializedAsync()
        {
            categories = await PreventiveMedicineService.GetAllPMCategoriesAsync();
        }

        private async Task LoadSubCategories(Guid categoryId)
        {
            _filteredSubCategories = await PreventiveMedicineService.GetPMSubCategoriesByPMCategoryIdAsync(categoryId);
        }

        private async Task LoadSymptoms(Guid subCategoryId)
        {
            _filteredSymptoms = await PreventiveMedicineService.GetPMSymptomsBySubCategoryIdAsync(subCategoryId);
        }



        private void ShowForm(FormType formType)
        {
            activeForm = formType;

            if (formType == FormType.Subcategory)
            {
                _newSubCategory = new();
                _selectedCategoryIdForSymptom = Guid.Empty;
            }

            if (formType == FormType.Symptom)
            {
                _newSymptom = new();
                _selectedCategoryIdForSymptom = _selectedCategoryId;
                _selectedSubCategoryIdForSymptom = _selectedSubCategoryId;

                if (_selectedCategoryIdForSymptom != Guid.Empty)
                {
                    _filteredSubCategoriesForSymptom = _filteredSubCategories
                        .Where(s => s.PMCategoryId == _selectedCategoryIdForSymptom)
                        .ToList();
                }
            }
        }

        private async Task SelectCategory(Guid categoryId)
        {
            _selectedCategoryId = categoryId;
            _selectedSubCategoryId = Guid.Empty;
            activeForm = FormType.None;
            await LoadSubCategories(categoryId);
        }

        private async Task SelectSubCategory(Guid subCategoryId)
        {
            _selectedSubCategoryId = subCategoryId;
            activeForm = FormType.None;
            await LoadSymptoms(subCategoryId);
        }

        private void CancelForm()
        {
            activeForm = FormType.None;
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private async Task AddCategory()
        {
            if (!await ConfirmSave()) return;

            if (string.IsNullOrWhiteSpace(_newCategory.PMCategoryName))
            {
                Snackbar.Add(Localizer["CategoryNameRequired"], Severity.Error);
                return;
            }

            if (categories.Any(c => c.PMCategoryName.Equals(_newCategory.PMCategoryName, StringComparison.OrdinalIgnoreCase)))
            {
                Snackbar.Add(Localizer["CategoryExists"], Severity.Error);
                return;
            }

            var categoryToAdd = new PMCategory
            {
                PMCategoryId = Guid.NewGuid(),
                PMCategoryName = _newCategory.PMCategoryName,
                PMCategoryDescription = _newCategory.PMCategoryDescription,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            try
            {
                await PreventiveMedicineService.AddPMCategoryAsync(new List<PMCategory> { categoryToAdd });
                Snackbar.Add(Localizer["CategoryAdded"], Severity.Success);
                categories = await PreventiveMedicineService.GetAllPMCategoriesAsync();
                ShowForm(FormType.Category);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["ErrorAddingCategory", ex.Message], Severity.Error);
            }
        }

        private async Task AddSubCategory()
        {
            if (!await ConfirmSave()) return;

            if (string.IsNullOrWhiteSpace(_newSubCategory.PMSubcategoryName))
            {
                Snackbar.Add(Localizer["SubcategoryNameRequired"], Severity.Error);
                return;
            }

            if (_selectedCategoryId == Guid.Empty)
            {
                Snackbar.Add(Localizer["SelectACategory"], Severity.Error);
                return;
            }

            if (_filteredSubCategories.Any(sc =>
                sc.PMCategoryId == _selectedCategoryId &&
                sc.PMSubcategoryName.Equals(_newSubCategory.PMSubcategoryName, StringComparison.OrdinalIgnoreCase)))
            {
                Snackbar.Add(Localizer["SubcategoryExists"], Severity.Error);
                return;
            }

            var subCategoryToAdd = new PMSubCategory
            {
                PMSubcategoryId = Guid.NewGuid(),
                PMCategoryId = _selectedCategoryId,
                PMSubcategoryName = _newSubCategory.PMSubcategoryName,
                PMSubcategoryDescription = _newSubCategory.PMSubcategoryDescription,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            try
            {
                await PreventiveMedicineService.AddPMSubCategoryAsync(new List<PMSubCategory> { subCategoryToAdd });
                Snackbar.Add(Localizer["SubcategoryAdded"], Severity.Success);
                await LoadSubCategories(_selectedCategoryId);
                ShowForm(FormType.Subcategory);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["ErrorAddingSubcategory", ex.Message], Severity.Error);
            }
        }

        private async Task AddSymptom()
        {
            if (!await ConfirmSave()) return;

            if (string.IsNullOrWhiteSpace(_newSymptom.PMSymptomName))
            {
                Snackbar.Add(Localizer["SymptomNameRequired"], Severity.Error);
                return;
            }

            if (_selectedSubCategoryId == Guid.Empty)
            {
                Snackbar.Add(Localizer["SelectSubcategory"], Severity.Error);
                return;
            }

            if (_filteredSymptoms.Any(s =>
                s.PMSubCategoryId == _selectedSubCategoryId &&
                s.PMSymptomName.Equals(_newSymptom.PMSymptomName, StringComparison.OrdinalIgnoreCase)))
            {
                Snackbar.Add(Localizer["SymptomExists"], Severity.Error);
                return;
            }

            var symptomToAdd = new PMSymptoms
            {
                PMSymptomId = Guid.NewGuid(),
                PMSubCategoryId = _selectedSubCategoryId,
                PMSymptomName = _newSymptom.PMSymptomName,
                PMSymptomDescription = _newSymptom.PMSymptomDescription,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            try
            {
                await PreventiveMedicineService.AddPMSymptomsAsync(new List<PMSymptoms> { symptomToAdd });
                Snackbar.Add(Localizer["SymptomAdded"], Severity.Success);
                await LoadSymptoms(_selectedSubCategoryId);
                ShowForm(FormType.Symptom);
            }
            catch (Exception ex)
            {
                Snackbar.Add(Localizer["ErrorAddingSymptom", ex.Message], Severity.Error);
            }
        }

        private async Task<bool> ConfirmSave()
        {
            var result = await DialogService.ShowMessageBox(
                Localizer["ConfirmSave"],
                Localizer["SaveMessage"],
                yesText: Localizer["Yes"],
                noText: Localizer["No"]);
            return result == true;
        }
    }
}
