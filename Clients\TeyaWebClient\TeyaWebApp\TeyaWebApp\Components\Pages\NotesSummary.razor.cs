﻿using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models;
using MudBlazor;
using Sprache;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared;
using Syncfusion.Windows.Shared.Resources;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class NotesSummary : ComponentBase
    {
        private List<FamilyMember> familyMembers { get; set; }
        private List<PatientVitals> patientVitals { get; set; }
        private PatientVitals MostRecentVitals { get; set; }
        private List<BPVitals> formattedVitals { get; set; }
        private List<Allergy> allergies { get; set; }
        private List<MedicalHistoryDTO> medicalHistory { get; set; }
        private List<ActiveMedication> activeMedications { get; set; }

        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        public string fontFamily { get; set; } = "font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif";
        private Guid PatientID { get; set; }
        private Guid? OrganizationID { get; set; }



        protected override async Task OnInitializedAsync()
        {
           

            PatientID = _PatientService.PatientData.Id;
            OrganizationID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);

            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            patientVitals = (await VitalService.GetVitalsByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                 .OrderBy(v => v.CreatedDate)
            .Select(v => {
                     v.Temperature = VitalUtils.ExtractNumeric(v.Temperature);
                     v.Weight = VitalUtils.ExtractNumeric(v.Weight);
                     v.Height = VitalUtils.ExtractNumeric(v.Height);
                     v.Pulse = VitalUtils.ExtractNumeric(v.Pulse);
                     return v;
                 })
                 .ToList();


         

            MostRecentVitals = patientVitals
                              ?.OrderByDescending(v => v.CreatedDate).FirstOrDefault();

            familyMembers = (await FamilyMemberService.GetFamilyMemberByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                            .OrderByDescending(fm => fm.CreatedDate) 
                            .Take(5) 
                            .ToList();
            allergies = (await AllergyService.GetAllergyByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                        .OrderByDescending(fm => fm.CreatedOn)
                            .Take(5)
                            .ToList();
            medicalHistory = (await MedicalHistoryService.GetAllByIdAndIsActiveAsync(PatientID, OrganizationID, Subscription))
                              .OrderByDescending(fm => fm.CreatedDate)
                               .Take(5)
                               .ToList();
            activeMedications = (await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                                .OrderByDescending(fm => fm.CreatedDate)
                                .Take(5)
                                .ToList();

            formattedVitals = patientVitals
                .Where(v => !string.IsNullOrWhiteSpace(v.BP))
                .Select(v =>
                {
                    var bpParts = v.BP.Split('/');
                    int systolic = 0, diastolic = 0;

                    if (bpParts.Length == 2)
                    {
                        int.TryParse(bpParts[0], out systolic);
                        int.TryParse(bpParts[1], out diastolic);
                    }
                    else if (bpParts.Length == 1)
                    {
                        int.TryParse(bpParts[0], out systolic);
                        // Diastolic remains 0
                    }

                    return new BPVitals
                    {
                        CreatedDate = v.CreatedDate,
                        Systolic = systolic,
                        Diastolic = diastolic
                    };
                })
                .ToList();

        }

        public static class VitalUtils
        {
            public static string? ExtractNumeric(string? input)
            {
                if (string.IsNullOrWhiteSpace(input))
                    return null;

                var match = Regex.Match(input, @"-?\d+(\.\d+)?");
                return match.Success ? match.Value : null;
            }
        }

    }
}
