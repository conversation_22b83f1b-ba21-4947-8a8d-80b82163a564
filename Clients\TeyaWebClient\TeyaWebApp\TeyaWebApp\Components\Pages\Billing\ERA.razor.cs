﻿using Microsoft.AspNetCore.Components;
using Microsoft.CognitiveServices.Speech.Transcription;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.Data;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Inputs;
using Syncfusion.Blazor.Popups;
using TeyaUIModels.Model.Billing;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static TeyaUIModels.Model.Billing.ERARecord;
using TeyaUIViewModels.ViewModel.Billing.Interfaces;
using TeyaUIViewModels.ViewModel.Billing.Classes;
using TeyaUIModels.Model;

namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class ERA : ComponentBase
    {
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject]
        private ActiveUser User { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }

        // Main ERA data structure - following DentalClaims pattern
        private CompleteERA CompleteERA = new();
        private ERARecord ERARecord = new();

        // Grid data sources
        private List<ERAPayments> eraPayments = new List<ERAPayments>();
        private List<ERAClaimPosted> claimsPostedData = new List<ERAClaimPosted>();
        private List<ERACPTPayment> cptPaymentsData = new List<ERACPTPayment>();

        // Grid references
        private SfGrid<ERAPayments> PaymentsGrid;
        private SfGrid<ERAClaimPosted> ClaimPostedGrid;
        private SfGrid<ERACPTPayment> CPTPaymentsGrid;

        // Modal dialogs
        private SfDialog? PaymentAdvisoryModal;
        private bool isPaymentAdvisoryModalVisible = false;
        private SfDialog PaymentPostingModal;
        private bool isPaymentPostingModalVisible = false;

        // Loading and submission states
        private bool isLoading = false;
        private bool isSubmitting = false;

        // Organization and subscription info
        private Guid OrganizationID { get; set; }
        private bool Subscription = false;

        // ERA Process variables
        private string selectedERAType = "";
        private string selectedImportOption = "";
        private string selectedFacility = "";
        private bool makeDefaultFacility = false;

        // Filter variables
        private string selectedPostingStatus = "";
        private string selectedPayer = "";
        private string selectedPostedBy = "";
        private DateTime? selectedPostedDate;

        // Data sources for dropdowns
        private List<string> eraTypeOptions = new() { "835", "999" };
        private List<string> importOptions = new() { "File Upload", "EDI Direct", "Manual Entry" };
        private List<string> facilityOptions = new() { "Main Facility", "Branch Office" };
        private List<string> postingStatusOptions = new() { "Pending", "Posted", "Rejected" };
        private List<string> payerOptions = new() { "Aetna", "Blue Cross", "Medicare", "Medicaid" };
        private List<string> postedByOptions = new() { "System", "User1", "User2" };
        private List<string> feeScheduleOptions = new List<string>();

        protected override async Task OnInitializedAsync()
        {
            try
            {
                isLoading = true;

                // Get organization info - following DentalClaims pattern
                OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);

                // Get subscription info - following DentalClaims pattern
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrganizationID);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise"; // Assuming Enterprise is the subscription level

                // Initialize CompleteERA structure
                InitializeCompleteERA();

                // Load initial data
                await LoadERAData();

                isLoading = false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during initialization: {ex.Message}");
                Snackbar?.Add("Error loading page data", Severity.Error);
                isLoading = false;
            }
        }

        private void InitializeCompleteERA()
        {
            CompleteERA = new CompleteERA
            {
                Id = Guid.NewGuid(),
                eraRecord = new ERARecord(),
                eraClaimPosted = new List<ERAClaimPosted>(),
                eraCPTPayment = new List<ERACPTPayment>(),
                eraPayments = new List<ERAPayments>(),
                OrganizationID = OrganizationID,
                Subscription = Subscription
            };

            ERARecord = CompleteERA.eraRecord;
            claimsPostedData = CompleteERA.eraClaimPosted;
            cptPaymentsData = CompleteERA.eraCPTPayment;
            eraPayments = CompleteERA.eraPayments;

        }

        private async Task LoadERAData()
        {
            try
            {
                if (CompleteERA.Id != Guid.Empty)
                {
                    var existingERA = await ERAService.GetERAByIdAsync(
                        CompleteERA.Id,
                        OrganizationID,
                        Subscription);
                    if (existingERA != null && existingERA.Any())
                    {
                        var existingRecord = existingERA.First();
                        CompleteERA = existingRecord;
                        ERARecord = CompleteERA.eraRecord ?? new ERARecord();
                        claimsPostedData = CompleteERA.eraClaimPosted ?? new List<ERAClaimPosted>();
                        cptPaymentsData = CompleteERA.eraCPTPayment ?? new List<ERACPTPayment>();
                        eraPayments = CompleteERA.eraPayments ?? new List<ERAPayments>();
                        StateHasChanged();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading ERA data: {ex.Message}");
            }
        }

        // Event handlers
        private async Task OnImportERA()
        {
            try
            {
                isSubmitting = true;
                // Import ERA logic here
                Snackbar?.Add("ERA import functionality to be implemented", Severity.Info);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error importing ERA: {ex.Message}");
                Snackbar?.Add("Error importing ERA", Severity.Error);
            }
            finally
            {
                isSubmitting = false;
            }
        }

        private async Task OnEPost()
        {
            try
            {
                isSubmitting = true;
                // ePost logic here
                Snackbar?.Add("ePost functionality to be implemented", Severity.Info);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing ePost: {ex.Message}");
                Snackbar?.Add("Error processing ePost", Severity.Error);
            }
            finally
            {
                isSubmitting = false;
            }
        }

        private async Task OnMarkAsPosted()
        {
            try
            {
                isSubmitting = true;
                // Mark as posted logic here
                Snackbar?.Add("Mark as posted functionality to be implemented", Severity.Info);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error marking as posted: {ex.Message}");
                Snackbar?.Add("Error marking as posted", Severity.Error);
            }
            finally
            {
                isSubmitting = false;
            }
        }

        private void AddERAPayment()
        {
            eraPayments.Add(new ERAPayments
            {
                PaymentId = Guid.NewGuid(),
                IsSelected = false,
                Status = "",
                Payer = "",
                PostedBy = "",
                PostedDate = DateTime.Now,
                Method = "",
                Dated = DateTime.Now,
                TraceNumber = "",
                Amount = 0.00m
            });

            StateHasChanged();
        }

        private async Task ERAPaymentActionBeginHandler(ActionEventArgs<ERAPayments> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (args.Data != null)
                    {
                        // Basic validation
                        if (string.IsNullOrWhiteSpace(args.Data.Payer) || args.Data.Amount <= 0)
                        {
                            args.Cancel = true;
                            Snackbar?.Add("Please enter a valid payer and amount.", Severity.Warning);
                            return;
                        }

                        // Assign new ID if it's not already set
                        if (args.Data.PaymentId == Guid.Empty)
                        {
                            args.Data.PaymentId = Guid.NewGuid();
                        }
                        args.Data.ERAId = CompleteERA.Id;

                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ERAPayments
                    {
                        PaymentId = Guid.NewGuid(),
                        ERAId = CompleteERA.Id,
                        IsSelected = false,
                        Status = "",
                        Payer = "",
                        PostedBy = "",
                        PostedDate = DateTime.Now,
                        Method = "",
                        Dated = DateTime.Now,
                        TraceNumber = "",
                        Amount = 0.00m
                    };
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting ERA Payment: {args.Data?.TraceNumber}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ERAPaymentActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing ERA payment grid action", Severity.Error);
            }
        }

        private async Task ERAPaymentActionCompletedHandler(ActionEventArgs<ERAPayments> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    Console.WriteLine($"ERA Payment saved: {args.Data?.PaymentId}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"ERA Payment deleted: {args.Data?.PaymentId}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    Console.WriteLine($"New ERA Payment added: {args.Data?.PaymentId}");
                }

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ERAPaymentActionCompletedHandler: {ex.Message}");
                Snackbar?.Add("Error completing ERA payment grid action", Severity.Error);
            }
        }

        private void OnImportERA(string selectedOption)
        {
            ERARecord.ImportOption = selectedOption;
            Console.WriteLine($"Selected import option: {selectedOption}");
            // Trigger import logic
        }

        private void AddClaimPosted()
        {
            claimsPostedData.Add(new ERAClaimPosted
            {
                ClaimPostedId = Guid.NewGuid(), // Assuming ERAClaimPosted has an Id property
                ClaimNo = "",
                ServiceDate = DateTime.Now,
                PatientName = "",
                Billed = 0.00m,
                Allowed = 0.00m,
                Deduct = 0.00m,
                Coins = 0.00m,
                Copay = 0.00m,
                Paid = 0.00m,
                Adjustment = 0.00m,
                Withhold = 0.00m,
                Code = ""
            });
            StateHasChanged();
        }

        private async Task ClaimPostedActionBeginHandler(ActionEventArgs<ERAClaimPosted> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (args.Data != null)
                    {
                        // Basic validation
                        if (string.IsNullOrWhiteSpace(args.Data.ClaimNo) ||
                            string.IsNullOrWhiteSpace(args.Data.PatientName))
                        {
                            args.Cancel = true;
                            Snackbar?.Add("Please enter a valid claim number and patient name.", Severity.Warning);
                            return;
                        }

                        if (args.Data.ClaimPostedId == Guid.Empty)
                        {
                            args.Data.ClaimPostedId = Guid.NewGuid();
                        }
                        // Assign CompleteERA Id for relationship
                        args.Data.ERAId = CompleteERA.Id; // Assuming there's a foreign key
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ERAClaimPosted
                    {
                        ClaimPostedId = Guid.NewGuid(),
                        ERAId = CompleteERA.Id,
                        ClaimNo = "",
                        ServiceDate = DateTime.Now,
                        PatientName = "",
                        Billed = 0.00m,
                        Allowed = 0.00m,
                        Deduct = 0.00m,
                        Coins = 0.00m,
                        Copay = 0.00m,
                        Paid = 0.00m,
                        Adjustment = 0.00m,
                        Withhold = 0.00m,
                        Code = ""
                    };
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting Claim Posted: {args.Data?.ClaimNo}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ClaimPostedActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing Claim Posted grid action", Severity.Error);
            }
        }

        private async Task ClaimPostedActionCompletedHandler(ActionEventArgs<ERAClaimPosted> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    Console.WriteLine($"Claim Posted saved: {args.Data?.ClaimNo}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Claim Posted deleted: {args.Data?.ClaimNo}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    Console.WriteLine($"New Claim Posted added: {args.Data?.ClaimNo}");
                }
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ClaimPostedActionCompletedHandler: {ex.Message}");
                Snackbar?.Add("Error completing Claim Posted grid action", Severity.Error);
            }
        }

        // Payment Advisory Modal Methods
        private void OpenPaymentAdvisoryModal()
        {
            isPaymentAdvisoryModalVisible = true;
            StateHasChanged();
        }

        private void Cancel()
        {
            isPaymentAdvisoryModalVisible = false;
            StateHasChanged();
        }

        private void CancelAdvisory()
        {
            isPaymentAdvisoryModalVisible = false;
            StateHasChanged();
        }

        private void Save()
        {
            isPaymentAdvisoryModalVisible = false;
            StateHasChanged();
        }

        private async Task PreventEscapeClose(RecordDoubleClickEventArgs<ERAClaimPosted> args)
        {
            // This method can be used to handle any specific logic when editing
            // The main purpose is to prevent ESC from closing the dialog during grid operations
        }

        private void OpenPaymentPostingModal()
        {
            isPaymentPostingModalVisible = true;
            StateHasChanged();
        }

        private void CancelPaymentPosting()
        {
            isPaymentPostingModalVisible = false;
            StateHasChanged();
        }

        private void SavePaymentPosting()
        {
            // Add save logic here
            isPaymentPostingModalVisible = false;
            StateHasChanged();
        }

        private void AddCPTPayment()
        {
            cptPaymentsData.Add(new ERACPTPayment
            {
                CPTPaymentId = Guid.NewGuid(), // Assuming ERACPTPayment has an Id property
                ServiceDate = DateTime.Now,
                POS = "",
                Units = 0.00m,
                Code = "",
                Billed = 0.00m,
                Balance = 0.00m,
                Allowed = 0.00m,
                Deduct = 0.00m,
                Coins = 0.00m,
                CoPay = 0.00m,
                Paid = 0.00m,
                Adjust = 0.00m,
                Withhold = 0.00m,
                DenialCode = ""
            });
            StateHasChanged();
        }

        private async Task CPTPaymentActionBeginHandler(ActionEventArgs<ERACPTPayment> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (args.Data != null)
                    {
                        // Basic validation
                        if (string.IsNullOrWhiteSpace(args.Data.Code) ||
                            args.Data.Units <= 0)
                        {
                            args.Cancel = true;
                            Snackbar?.Add("Please enter a valid CPT code and units.", Severity.Warning);
                            return;
                        }

                        // Assign CompleteERA Id for relationship
                        args.Data.ERAId = CompleteERA.Id; // Assuming there's a foreign key
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ERACPTPayment
                    {
                        CPTPaymentId = Guid.NewGuid(),
                        ERAId=CompleteERA.Id,
                        ServiceDate = DateTime.Now,
                        POS = "",
                        Units = 0.00m,
                        Code = "",
                        Billed = 0.00m,
                        Balance = 0.00m,
                        Allowed = 0.00m,
                        Deduct = 0.00m,
                        Coins = 0.00m,
                        CoPay = 0.00m,
                        Paid = 0.00m,
                        Adjust = 0.00m,
                        Withhold = 0.00m,
                        DenialCode = ""
                    };
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting CPT Payment: {args.Data?.Code}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CPTPaymentActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing CPT Payment grid action", Severity.Error);
            }
        }

        private async Task CPTPaymentActionCompletedHandler(ActionEventArgs<ERACPTPayment> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    Console.WriteLine($"CPT Payment saved: {args.Data?.Code}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"CPT Payment deleted: {args.Data?.Code}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    Console.WriteLine($"New CPT Payment added: {args.Data?.Code}");
                }
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CPTPaymentActionCompletedHandler: {ex.Message}");
                Snackbar?.Add("Error completing CPT Payment grid action", Severity.Error);
            }
        }

        // Validation methods - following DentalClaims pattern
        private bool ValidateForm()
        {
            try
            {
                if (OrganizationID == Guid.Empty)
                {
                    Snackbar?.Add("Organization information is missing. Please reload the page.", Severity.Error);
                    return false;
                }

                //// Add specific ERA validation logic here
                //if (string.IsNullOrEmpty(selectedERAType))
                //{
                //    Snackbar?.Add("Please select an ERA type.", Severity.Warning);
                //    return false;
                //}

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Validation error: {ex.Message}");
                return false;
            }
        }

        private void PopulateERAData()
        {
            try
            {
                // Populate ERARecord
                ERARecord.Id = CompleteERA.Id;
                ERARecord.OrganizationId = OrganizationID;
                ERARecord.Subscription = Subscription;
                ERARecord.ERAType = selectedERAType;
                ERARecord.ImportOption = selectedImportOption;
                ERARecord.Facility = selectedFacility;
                ERARecord.PostingStatus = selectedPostingStatus;
                ERARecord.SelectPayer = selectedPayer;
                ERARecord.PostedBy = selectedPostedBy;
                ERARecord.SelectPostedDate = selectedPostedDate;

                // Ensure all related entities have proper IDs
                foreach (var claimPosted in claimsPostedData)
                {
                    if (claimPosted.ClaimPostedId == Guid.Empty)
                    {
                        claimPosted.ClaimPostedId = Guid.NewGuid();
                    }
                    claimPosted.ERAId = CompleteERA.Id; // Set foreign key if exists
                }

                foreach (var erapayment in eraPayments)
                {
                    if (erapayment.PaymentId == Guid.Empty)
                    {
                        erapayment.PaymentId = Guid.NewGuid();
                    }
                    erapayment.ERAId = CompleteERA.Id; // Set foreign key if exists
                }

                foreach (var cptPayment in cptPaymentsData)
                {
                    if (cptPayment.CPTPaymentId == Guid.Empty)
                    {
                        cptPayment.CPTPaymentId = Guid.NewGuid();
                    }
                    cptPayment.ERAId = CompleteERA.Id; // Set foreign key if exists
                }

                // Update CompleteERA
                CompleteERA.eraRecord = ERARecord;
                CompleteERA.eraPayments = eraPayments;
                CompleteERA.eraClaimPosted = claimsPostedData;
                CompleteERA.eraCPTPayment = cptPaymentsData;
                CompleteERA.OrganizationID = OrganizationID;
                CompleteERA.Subscription = Subscription;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error populating ERA data: {ex.Message}");
                throw;
            }
        }

        // Commented out submission method - following DentalClaims pattern

        private async Task OnSubmit()
        {
            if (isSubmitting) return;

            try
            {
                isSubmitting = true;

                Console.WriteLine("Starting ERA form submission...");
                if (!ValidateForm())
                {
                    Console.WriteLine("ERA form validation failed");
                    return;
                }

                Console.WriteLine("ERA form validation passed");
                PopulateERAData();

                Console.WriteLine($"Populated ERA data with {claimsPostedData.Count} claim posted records and {cptPaymentsData.Count} CPT payment records");
                Console.WriteLine($"Submitting CompleteERA: ID={CompleteERA.Id}, ERAType={ERARecord.ERAType}");

                foreach (var claimPosted in claimsPostedData)
                {
                    Console.WriteLine($"Claim Posted: ID={claimPosted.ClaimPostedId}, ClaimNo={claimPosted.ClaimNo}, Paid={claimPosted.Paid}");
                }

                foreach (var cptPayment in cptPaymentsData)
                {
                    Console.WriteLine($"CPT Payment: ID={cptPayment.CPTPaymentId}, Code={cptPayment.Code}, Paid={cptPayment.Paid}");
                }
                foreach (var erapayment in eraPayments)
                {
                    Console.WriteLine($"CPT Payment: ID={erapayment.PaymentId}, Code={erapayment.Payer}, Paid={erapayment.Dated}");
                }

                // Submit to service
                await ERAService.AddERAAsync(new List<CompleteERA> { CompleteERA }, OrganizationID, Subscription);

                Console.WriteLine("Successfully submitted ERA to database");

                Snackbar?.Add("ERA submitted successfully!", Severity.Success);

                // Reset form for next ERA
                await ResetFormForNextERA();

                Console.WriteLine("ERA form reset for next entry");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error submitting ERA: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                var errorMessage = $"Error submitting ERA: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $" Inner exception: {ex.InnerException.Message}";
                }

                Snackbar?.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isSubmitting = false;
                StateHasChanged();
            }
        }

        private async Task ResetFormForNextERA()
        {
            try
            {
                // Reset form state
                selectedERAType = "";
                selectedImportOption = "";
                selectedFacility = "";
                makeDefaultFacility = false;
                selectedPostingStatus = "";
                selectedPayer = "";
                selectedPostedBy = "";
                selectedPostedDate = null;

                // Clear grid data
                eraPayments.Clear();
                claimsPostedData.Clear();
                cptPaymentsData.Clear();

                // Initialize new CompleteERA
                InitializeCompleteERA();

                // Add default entries
                AddERAPayment();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error resetting ERA form: {ex.Message}");
            }
        }
    }
}