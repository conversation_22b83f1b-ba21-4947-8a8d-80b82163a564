﻿@page "/era"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@using TeyaUIViewModels.ViewModel.Billing.Interfaces
@attribute [Authorize(Policy = "eraAccessPolicy")]
@using TeyaWebApp.Components.Layout
@layout Admin
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Popups
@using System.Net.Http
@using System.Net.Http.Json
@using TeyaWebApp.Services
@using TeyaWebApp.TeyaAIScribeResources
@using TeyaUIModels.Model.Billing
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IERAService ERAService
@inject HttpClient Http

<h1 class="form-title" style="margin-left: 17px;">ERA</h1>
<div class="header-accent" style="margin-left: 17px;"></div>


<div class="era-form" style="margin-top: 30px;">
    <SfTab>
        <TabItems>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="ERA"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="form-section" style="margin-top: 20px;">
                        <div class="era-main-container" style="display: flex; flex-direction: column; gap: 50px;">

                            <!-- ERA Process Section -->
                            <div class="era-process-container" style="border: 1px solid #ccc; border-radius: 6px; padding: 20px; position: relative; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 20px;">
                                <div class="era-process-header" style="position: absolute; top: -10px; left: 15px; background: #f8f9fa; padding: 4px 12px; font-weight: 600; color: #495057; font-size: 13px; border: 1px solid #dee2e6; border-radius: 4px;">
                                    ERA Process
                                </div>
                                <div class="era-process-inputs" style="display: flex; flex-direction: column; gap: 15px; margin-top: 15px;">
                                    <!-- Single horizontal row with all controls -->
                                    <div style="display: flex; align-items: center; gap: 25px; flex-wrap: wrap;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 10px;"></label>
                                            <SfDropDownList TValue="string" TItem="string"
                                                            CssClass="compact-textbox custom-dropdown"
                                                            Placeholder="File"
                                                            DataSource="@eraTypeOptions"
                                                            @bind-Value="ERARecord.ERAType"
                                                            Width="90px">
                                            </SfDropDownList>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 10px;"></label>
                                            <SfDropDownList TValue="string" TItem="string"
                                                            CssClass="compact-textbox custom-dropdown"
                                                            Placeholder="Import ERA"
                                                            DataSource="@importOptions"
                                                            @bind-Value="ERARecord.ImportOption"
                                                            Width="130px">
                                            </SfDropDownList>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; min-width: 25px;">Facility</label>
                                            <SfDropDownList TValue="string" TItem="string"
                                                            CssClass="compact-textbox custom-dropdown"
                                                            DataSource="@facilityOptions"
                                                            Placeholder="Select Facility"
                                                            @bind-Value="ERARecord.Facility"
                                                            Width="225px">
                                            </SfDropDownList>
                                        </div>
                                        <MudButton Variant="Variant.Outlined"
                                                   OnClick="OnEPost"
                                                   Color="Color.Primary"
                                                   Style="min-height: 30px; height: 30px; padding: 2px 16px; font-size: 0.8rem; width: 70px;"
                                                   Class="uniform-button">
                                            ePost
                                        </MudButton>
                                        <MudButton Variant="Variant.Outlined"
                                                   OnClick="OnMarkAsPosted"
                                                   Color="Color.Primary"
                                                   Style="min-height: 30px; height: 30px; padding: 2px 16px; font-size: 0.8rem; width: 140px;"
                                                   Class="uniform-button">
                                            Mark as Posted
                                        </MudButton>
                                    </div>
                                    <!-- Checkbox positioned below Facility dropdown -->
                                    <div style="display: flex; align-items: center; margin-left: 365px; margin-top: 5px;">
                                        <SfCheckBox Label="Make this as default Facility"
                                                    @bind-Checked="ERARecord.MakeDefaultFacility"
                                                    CssClass="service-checkbox">
                                        </SfCheckBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="filter-era-container" style="border: 1px solid #ccc; border-radius: 6px; padding: 20px; position: relative; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 20px;">
                            <div class="filter-era-header" style="position: absolute; top: -10px; left: 15px; background: #f8f9fa; padding: 4px 12px; font-weight: 600; color: #495057; font-size: 13px; border: 1px solid #dee2e6; border-radius: 4px;">
                                Filter ERA
                            </div>
                            <div class="filter-era-inputs" style="display: flex; flex-direction: column; gap: 20px; margin-top: 15px;">
                                <div style="display: flex; gap: 25px; flex-wrap: wrap;">
                                    <div style="display: flex; flex-direction: column; gap: 8px;">
                                        <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057;">Select Posting Status</label>
                                        <SfDropDownList TValue="string" TItem="string"
                                                        CssClass="compact-textbox custom-dropdown"
                                                        Placeholder="Select Status"
                                                        DataSource="@postingStatusOptions"
                                                        @bind-Value="ERARecord.SelectPostingStatus"
                                                        Width="180px">
                                        </SfDropDownList>
                                    </div>

                                    <div style="display: flex; flex-direction: column; gap: 8px;">
                                        <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057;">Select Payer</label>
                                        <SfDropDownList TValue="string" TItem="string"
                                                        CssClass="compact-textbox custom-dropdown"
                                                        Placeholder="Select Payer"
                                                        DataSource="@payerOptions"
                                                        @bind-Value="ERARecord.SelectPayer"
                                                        Width="180px">
                                        </SfDropDownList>
                                    </div>
                                    <div style="display: flex; flex-direction: column; gap: 8px;">
                                        <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057;">Select Posted By</label>
                                        <SfDropDownList TValue="string" TItem="string"
                                                        CssClass="compact-textbox custom-dropdown"
                                                        Placeholder="Select User"
                                                        DataSource="@postedByOptions"
                                                        @bind-Value="ERARecord.SelectPostedBy"
                                                        Width="180px">
                                        </SfDropDownList>
                                    </div>
                                    <div style="display: flex; flex-direction: column; gap: 8px;">
                                        <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057;">Select Posted Date</label>
                                        <SfDatePicker @bind-Value="ERARecord.SelectPostedDate"
                                                      Format="MM-dd-yyyy"
                                                      CssClass="compact-datepicker custom-datepicker"
                                                      Width="180px">
                                        </SfDatePicker>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="era-grid-container" style="border: 1px solid #ccc; border-radius: 6px; padding: 20px; position: relative; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 20px;">
                            <div class="era-grid-header" style="position: absolute; top: -10px; left: 15px; background: #f8f9fa; padding: 4px 12px; font-weight: 600; color: #495057; font-size: 13px; border: 1px solid #dee2e6; border-radius: 4px;">
                                ERA
                            </div>
                            <div class="era-grid-section" style="margin-top: 15px;">
                                <div class="grid-container">
                                    <SfGrid @ref="PaymentsGrid"
                                            TValue="ERAPayments"
                                            Style="font-size: 0.85rem; margin-top: 24px;"
                                            DataSource="@eraPayments"
                                            AllowPaging="true"
                                            PageSettings-PageSize="5"
                                            GridLines="GridLine.Both"
                                            AllowEditing="true"
                                            AllowScrolling="true"
                                            Width="100%"
                                            Height="400px"
                                            Toolbar="@(new List<string>() { "Add" })">
                                        <GridScrollSettings EnableVirtualization="false"></GridScrollSettings>
                                        
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridPageSettings PageSize="10"></GridPageSettings>
                                        <GridEvents OnActionComplete="ERAPaymentActionCompletedHandler" OnActionBegin="ERAPaymentActionBeginHandler" TValue="ERAPayments"></GridEvents>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(ERAPayments.PaymentId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.Status)"
                                                        HeaderText="Status"
                                                        Width="120"
                                                        MinWidth="100"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.Payer)"
                                                        HeaderText="Payer"
                                                        Width="200"
                                                        MinWidth="150"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        TextAlign="TextAlign.Center">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.PostedBy)"
                                                        HeaderText="Posted By"
                                                        Width="150"
                                                        MinWidth="130"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.PostedDate)"
                                                        HeaderText="Posted Date"
                                                        Width="150"
                                                        MinWidth="120"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        TextAlign="TextAlign.Left"
                                                        Format="d">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.Method)"
                                                        HeaderText="Method"
                                                        Width="120"
                                                        MinWidth="100"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.Dated)"
                                                        HeaderText="Dated"
                                                        Width="150"
                                                        MinWidth="130"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        TextAlign="TextAlign.Center"
                                                        Format="d">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.TraceNumber)"
                                                        HeaderText="Trace#"
                                                        Width="120"
                                                        MinWidth="100"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                            </GridColumn>
                                            <GridColumn Field="@nameof(ERAPayments.Amount)"
                                                        HeaderText="Amount"
                                                        Width="150"
                                                        MinWidth="130"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        Format="C2"
                                                        Type="ColumnType.Number">
                                            </GridColumn>
                                            <GridColumn HeaderText="Actions"
                                                        Width="150"
                                                        MinWidth="120"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                                <GridCommandColumns>
                                                    <GridCommandColumn Type="CommandButtonType.Edit"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Delete"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Save"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                                </GridCommandColumns>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            </div>
                        </div>

                        <!-- Transaction and Posting Information Section -->
                        <div style="display: flex; gap: 20px; margin-bottom: 20px; align-items: stretch;">
                            <!-- Transaction Information Box (60%) -->
                            <div class="transaction-info-container" style="flex: 0 0 60%; border: 1px solid #ccc; border-radius: 6px; padding: 20px; position: relative; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                <div class="transaction-info-header" style="position: absolute; top: -10px; left: 15px; background: #f8f9fa; padding: 4px 12px; font-weight: 600; color: #495057; font-size: 13px; border: 1px solid #dee2e6; border-radius: 4px;">
                                    Transaction Information
                                </div>
                                <div class="transaction-info-content" style="margin-top: 15px;">
                                    <!-- First Row -->
                                    <div style="display: flex; gap: 30px; margin-bottom: 15px;">
                                        <div style="flex: 1; display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057; min-width: 90px;">Payment Id :</label>
                                            <SfTextBox @bind-Value="@ERARecord.PaymentId"
                                                       CssClass="compact-textbox custom-textbox"
                                                       Width="160px">
                                            </SfTextBox>
                                        </div>
                                        <div style="flex: 1; display: flex; align-items: center; gap: 27px ;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057; min-width: 120px;">Transaction Type :</label>
                                            <SfTextBox @bind-Value="@ERARecord.TransactionType"
                                                       CssClass="compact-textbox custom-textbox"
                                                       Width="140px">
                                            </SfTextBox>
                                        </div>
                                    </div>
                                    <!-- Second Row -->
                                    <div style="display: flex; gap: 30px; margin-bottom: 15px;">
                                        <div style="flex: 1; display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057; min-width: 90px;">Posted By :</label>
                                            <SfTextBox @bind-Value="@ERARecord.PostedBy"
                                                       CssClass="compact-textbox custom-textbox"
                                                       Width="160px">
                                            </SfTextBox>
                                        </div>
                                        <div style="flex: 1; display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057; min-width: 135px;">Transaction Method :</label>
                                            <SfTextBox @bind-Value="@ERARecord.TransactionMethod"
                                                       CssClass="compact-textbox custom-textbox"
                                                       Width="140px">
                                            </SfTextBox>
                                        </div>
                                    </div>
                                    <!-- Third Row -->
                                    <div style="display: flex; gap: 30px;">
                                        <div style="flex: 1; display: flex; align-items: center; gap: 10px;">
                                            <label class="form-label" style="margin: 0; font-size: 14px; font-weight: 500; color: #495057; min-width: 90px;">Posted Date :</label>
                                            <SfTextBox @bind-Value="@ERARecord.PostedDateText"
                                                       CssClass="compact-textbox custom-textbox"
                                                       Width="160px">
                                            </SfTextBox>
                                        </div>
                                        <div style="flex: 1;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="posting-info-container" style="flex: 1; max-width: 40%; border: 1px solid #ccc; border-radius: 6px; padding: 20px; position: relative; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                <div class="posting-info-header" style="position: absolute; top: -10px; left: 15px; background: #f8f9fa; padding: 4px 12px; font-weight: 600; color: #495057; font-size: 13px; border: 1px solid #dee2e6; border-radius: 4px;">
                                    Posting Information
                                </div>
                                <div class="posting-info-content" style="margin-top: 15px;">
                                    <!-- Header Row -->
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; padding: 0 20px;">
                                        <div style="flex: 1;"></div>
                                        <div style="flex: 1; text-align: center; font-weight: 600; color: #495057; font-size: 14px;">Unposted</div>
                                        <div style="flex: 1; text-align: center; font-weight: 600; color: #495057; font-size: 14px;">Posted</div>
                                    </div>
                                    
                                    <!-- Page Amount Row -->
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 0 20px;">
                                        <div style="flex: 1; font-weight: 500; color: #495057; font-size: 14px;">Page Amnt</div>
                                        <div style="flex: 1; text-align: center; font-weight: 600; color: #28a745; font-size: 14px;">$0.00</div>
                                        <div style="flex: 1; text-align: center; font-weight: 600; color: #28a745; font-size: 14px;">$0.00</div>
                                    </div>
                                    
                                    <!-- Total Amount Row -->
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0 20px;">
                                        <div style="flex: 1; font-weight: 500; color: #495057; font-size: 14px;">Total Amnt</div>
                                        <div style="flex: 1; text-align: center; font-weight: 600; color: #28a745; font-size: 14px;">$0.00</div>
                                        <div style="flex: 1; text-align: center; font-weight: 600; color: #28a745; font-size: 14px;">$0.00</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="Payment Advisory"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="payment-advisory-content" style="padding: 20px; height: 100%; overflow-y: auto; margin-top: 20px;">
                        <div class="payment-info-section" style="display: flex; gap: 20px; margin-bottom: 20px; align-items: flex-start;">
                            <div class="payment-details-box" style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.1); width: fit-content;">
                                <div style="display: flex; flex-direction: column; gap: 12px;">
                                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 5px;">
                                        <span style="font-weight: 700; color: #2c3e50; font-size: 20px;">@ERARecord.PayerName</span>
                                    </div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 14px;">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="color: #6c757d; font-weight: 500; min-width: 94px;">Payment Id:</label>
                                            <SfTextBox @bind-Value="ERARecord.PaymentId" Width="130px" Style="height: 32px;"></SfTextBox>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="color: #6c757d; font-weight: 500; min-width: 77px;">Check No:</label>
                                            <SfTextBox @bind-Value="ERARecord.CheckNumber" Width="130px" Style="height: 32px;"></SfTextBox>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="color: #6c757d; font-weight: 500; min-width: 85px;">Payment Date:</label>
                                            <SfDatePicker @bind-Value="ERARecord.PaymentDate" Width="130px" Style="height: 32px;" Format="MM/dd/yyyy"></SfDatePicker>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <label style="color: #6c757d; font-weight: 500; min-width: 75px;">Check Date:</label>
                                            <SfDatePicker @bind-Value="ERARecord.CheckDate" Width="130px" Style="height: 32px;" Format="MM/dd/yyyy"></SfDatePicker>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px; grid-column: span 2;">
                                            <label style="color: #6c757d; font-weight: 500; min-width: 95px;">EOB Date:</label>
                                            <SfDatePicker @bind-Value="ERARecord.EobDate" Width="130px" Style="height: 32px;"></SfDatePicker>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Box - Payment Amounts -->
                            <div class="payment-amounts-box" style="background: #ffffff; padding: 20px; border-radius: 8px; border: 2px solid #3a4a90; min-width: 280px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <div style="display: flex; align-items: center;">
                                    <div style="display: flex; flex-direction: column; gap: 8px; text-align: right; margin-right: 15px; flex: 1;">
                                        <div style="display: flex; justify-content: space-between; gap: 25px; padding: 4px 0;">
                                            <span style="font-size: 14px; color: #495057; font-weight: 500;">Check Amount</span>
                                            <span style="font-weight: 700; color: #28a745; font-size: 14px;">@(ERARecord.CheckAmount.ToString("C2"))</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; gap: 25px; padding: 4px 0;">
                                            <span style="font-size: 14px; color: #495057; font-weight: 500;">Posted Amount</span>
                                            <span style="font-weight: 700; color: #dc3545; font-size: 14px;">@(ERARecord.PostedAmount.ToString("C2"))</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; gap: 25px; padding: 4px 0; border-top: 2px solid #3a4a90; margin-top: 4px; padding-top: 8px;">
                                            <span style="font-size: 14px; color: #495057; font-weight: 600;">Balance</span>
                                            <span style="font-weight: 700; color: #3a4a90; font-size: 16px;">@(ERARecord.BalanceAmount.ToString("C2"))</span>
                                        </div>
                                    </div>
                                    <div style="font-size: 32px; color: #3a4a90; font-weight: bold;">$</div>
                                </div>
                            </div>
                        </div>
                        <div class="claims-posted-section" style="border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <!-- Single Row with All Elements -->
                            <div style="padding: 15px 20px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                                <span style="font-weight: 600; font-size: 16px; color: #495057; margin-right: 10px;">Claims Posted</span>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <span style="font-size: 14px; color: #495057; font-weight: 500;">Claim Id</span>
                                    <SfTextBox @bind-Value="ERARecord.ClaimId" CssClass="compact-textbox" Width="130px" Style="height: 32px;"></SfTextBox>
                                </div>

                                <MudButton Variant="Variant.Filled" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #3a4a90; color: white;">
                                    Go (F3)
                                </MudButton>

                                <SfTextBox CssClass="compact-textbox" Width="160px" Style="height: 32px;"></SfTextBox>

                                <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                    Post CPT (F4)
                                </MudButton>

                                <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                    Scan(F5)
                                </MudButton>

                                <MudButton Variant="Variant.Filled" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #dc3545; color: white; border: none;">
                                    Show Msg Codes
                                </MudButton>
                            </div>
                            <div class="grid-container" style="padding: 0;">
                                <SfGrid @ref="ClaimPostedGrid"
                                        TValue="ERAClaimPosted"
                                        Style="font-size: 0.85rem;"
                                        DataSource="@claimsPostedData"
                                        AllowPaging="true"
                                        PageSettings-PageSize="5"
                                        GridLines="GridLine.Both"
                                        AllowEditing="true"
                                        AllowScrolling="true"
                                        Width="100%"
                                        Height="350px"
                                        Toolbar="@(new List<string>() { "Add" })">
                                    <GridScrollSettings EnableVirtualization="false"></GridScrollSettings>
                                    
                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                    <GridPageSettings PageSize="10"></GridPageSettings>
                                    <GridEvents OnActionComplete="ClaimPostedActionCompletedHandler" 
                                               OnActionBegin="ClaimPostedActionBeginHandler" 
                                               TValue="ERAClaimPosted"></GridEvents>
                                    <GridColumns>
                                        <GridColumn Field="@nameof(ERAClaimPosted.ClaimPostedId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.ClaimNo)"
                                                    HeaderText="Claim No"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.ServiceDate)"
                                                    HeaderText="Service Date"
                                                    Width="150"
                                                    MinWidth="120"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="MM/dd/yyyy"
                                                    Type="ColumnType.Date">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.PatientName)"
                                                    HeaderText="Patient Name"
                                                    Width="200"
                                                    MinWidth="150"
                                                    TextAlign="TextAlign.Left"
                                                    HeaderTextAlign="TextAlign.Center">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Billed)"
                                                    HeaderText="Billed"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Allowed)"
                                                    HeaderText="Allowed"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Deduct)"
                                                    HeaderText="Deduct"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Coins)"
                                                    HeaderText="Coins"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Copay)"
                                                    HeaderText="Copay"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Paid)"
                                                    HeaderText="Paid"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Adjustment)"
                                                    HeaderText="Adjustment"
                                                    Width="130"
                                                    MinWidth="110"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Withhold)"
                                                    HeaderText="Withhold"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERAClaimPosted.Code)"
                                                    HeaderText="Code"
                                                    Width="100"
                                                    MinWidth="80"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                        </GridColumn>
                                        <GridColumn HeaderText="Actions"
                                                    Width="150"
                                                    MinWidth="120"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                            <GridCommandColumns>
                                                <GridCommandColumn Type="CommandButtonType.Edit"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Delete"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Save"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                            </GridCommandColumns>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="Payment Posting"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="payment-posting-content" style="padding: 20px; height: 100%; overflow-y: auto;">
                        <div class="payment-posting-info-section" style="display: flex; gap: 20px; margin-bottom: 20px;">
                            <div class="payment-claim-info-box" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.1); flex: 0 0 350px;">
                                <div style="display: flex; flex-direction: column; gap: 10px;">
                                    <!-- Payment Info Row -->
                                    <div style="display: flex; align-items: center; gap: 15px;">
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <label style="color: #495057; font-weight: 500; min-width: 60px; font-size: 13px;">Payment :</label>
                                            <SfTextBox @bind-Value="ERARecord.PaymentPostingId" Width="70px" Style="height: 28px; font-weight: bold; font-size: 12px;"></SfTextBox>
                                        </div>
                                    </div>
                
                                    <!-- Claim Info Row -->
                                    <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <label style="color: #495057; font-weight: 500; min-width: 60px; font-size: 13px;">Claim No :</label>
                                            <SfTextBox @bind-Value="ERARecord.ClaimNumber" Width="80px" Style="height: 28px; font-size: 12px;"></SfTextBox>
                                        </div>
                    
                                        <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                                   Style="min-height: 28px; height: 28px; padding: 4px 12px; font-size: 0.75rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                            View Claim
                                        </MudButton>
                                    </div>
                
                                    <!-- Patient and Provider Row -->
                                    <div style="display: flex; flex-direction: column; gap: 6px;">
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <label style="color: #495057; font-weight: 500; min-width: 50px; font-size: 13px;">Patient :</label>
                                            <span style="font-weight: 600; color: #2c3e50; font-size: 13px;">@ERARecord.PatientName</span>
                                        </div>
                    
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <label style="color: #495057; font-weight: 500; min-width: 50px; font-size: 13px;">Provider :</label>
                                            <span style="font-weight: 600; color: #2c3e50; font-size: 13px;">@ERARecord.ProviderName</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
        
                            <!-- Right Section - Insurance and Payment Info Side by Side -->
                            <div class="insurance-payment-container" style="flex: 1; display: flex; gap: 15px;">
            
                                <!-- Insurance Section -->
                                <div class="insurance-box" style="background: #ffffff; padding: 12px; border-radius: 6px; border: 1px solid #3a4a90; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="font-weight: 600; color: #495057; font-size: 13px;">Insurance(s)</span>
                                        <MudButton Variant="Variant.Text" Size="Size.Small" Style="min-height: 22px; padding: 2px 6px; font-size: 0.7rem;">
                                            Refresh
                                        </MudButton>
                                    </div>
                                    <div style="background: #3a4a90; color: white; padding: 8px; border-radius: 4px; text-align: center;">
                                        <div style="font-weight: 600; font-size: 12px;">InsuranceType</div>
                                        <div style="font-size: 11px;">InsuranceDetails</div>
                                    </div>
                                </div>
            
                                <!-- Payment Section -->
                                <div class="payment-box" style="background: #ffffff; padding: 12px; border-radius: 6px; border: 1px solid #3a4a90; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="font-weight: 600; color: #495057; font-size: 13px;">Payment(s)</span>
                                        <MudButton Variant="Variant.Text" Size="Size.Small" Style="min-height: 22px; padding: 2px 6px; font-size: 0.7rem;">
                                            View Payment
                                        </MudButton>
                                    </div>
                                    <div style="background: #e9ecef; padding: 8px; border-radius: 4px;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 6px; font-size: 11px; text-align: center;">
                                            <div style="font-weight: 600;">Paid</div>
                                            <div style="font-weight: 600;">Date</div>
                                            <div style="font-weight: 600;">Pmt #</div>
                                            <div style="font-weight: 600;">Payer</div>
                                            <div style="color: #28a745;">@ERARecord.PaidAmount1</div>
                                            <div>@ERARecord.PaymentDate1</div>
                                            <div>@ERARecord.PaymentNumber1</div>
                                            <div>@ERARecord.PayerName1</div>
                                            <div style="color: #dc3545;">@ERARecord.PaidAmount2</div>
                                            <div>@ERARecord.PaymentDate2</div>
                                            <div>@ERARecord.PaymentNumber2</div>
                                            <div>@ERARecord.PayerName2</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- CPT Payments Section -->
                        <div class="cpt-payments-section" style="border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <!-- Controls Row -->
                            <div style="padding: 15px 20px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                                <span style="font-weight: 600; font-size: 16px; color: #495057; margin-right: 10px;">CPT Payments</span>
            
                                <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                    Check
                                </MudButton>

                                <label style="font-size: 0.8rem; color: #495057; margin-right: 8px; font-weight: 500;">
                                    Fee Schedule
                                </label>

                                <SfDropDownList TValue="string" TItem="string"
                                                CssClass="compact-textbox custom-dropdown"
                                                Placeholder="Select Fee Schedule"
                                                DataSource="@feeScheduleOptions"
                                                @bind-Value="ERARecord.FeeSchedule"
                                                Width="180px"
                                                Style="height: 32px;">
                                </SfDropDownList>



                                <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                    CPT Adjs
                                </MudButton>

                                <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                    CPT Pmts
                                </MudButton>

                                <MudButton Variant="Variant.Filled" Size="Size.Small"
                                           Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #dc3545; color: white; border: none;">
                                    Show Msg Codes
                                </MudButton>
                            </div>

                            <!-- CPT Payments Grid -->
                            <div class="grid-container" style="padding: 0;">
                                <SfGrid @ref="CPTPaymentsGrid"
                                        TValue="ERACPTPayment"
                                        Style="font-size: 0.85rem;"
                                        DataSource="@cptPaymentsData"
                                        AllowPaging="true"
                                        PageSettings-PageSize="5"
                                        GridLines="GridLine.Both"
                                        AllowEditing="true"
                                        AllowScrolling="true"
                                        Width="100%"
                                        Height="350px"
                                        Toolbar="@(new List<string>() { "Add" })">
                
                                    <!-- Add ScrollSettings for horizontal scrolling -->
                                    <GridScrollSettings EnableVirtualization="false"></GridScrollSettings>
                
                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                    <GridPageSettings PageSize="10"></GridPageSettings>
                                    <GridEvents OnActionComplete="CPTPaymentActionCompletedHandler" 
                                               OnActionBegin="CPTPaymentActionBeginHandler" 
                                               TValue="ERACPTPayment"></GridEvents>
                                    <GridColumns>
                                        <GridColumn Field="@nameof(ERACPTPayment.CPTPaymentId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.ServiceDate)"
                                                    HeaderText="Service Dt"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="MM/dd/yyyy"
                                                    Type="ColumnType.Date">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.POS)"
                                                    HeaderText="POS"
                                                    Width="80"
                                                    MinWidth="60"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Units)"
                                                    HeaderText="Units"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Code)"
                                                    HeaderText="Code"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Billed)"
                                                    HeaderText="Billed"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Balance)"
                                                    HeaderText="Balance"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Allowed)"
                                                    HeaderText="Allowed"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Deduct)"
                                                    HeaderText="Deduct"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Coins)"
                                                    HeaderText="Coins"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.CoPay)"
                                                    HeaderText="CoPay"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Paid)"
                                                    HeaderText="Paid"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Adjust)"
                                                    HeaderText="Adjust"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.Withhold)"
                                                    HeaderText="Withhold"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Right"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="C2"
                                                    Type="ColumnType.Number">
                                        </GridColumn>
                                        <GridColumn Field="@nameof(ERACPTPayment.DenialCode)"
                                                    HeaderText="Denial"
                                                    Width="120"
                                                    MinWidth="100"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                        </GridColumn>
                                        <GridColumn HeaderText="Actions"
                                                    Width="150"
                                                    MinWidth="120"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                            <GridCommandColumns>
                                                <GridCommandColumn Type="CommandButtonType.Edit"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Delete"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Save"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                            </GridCommandColumns>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>
        </TabItems>
    </SfTab>
    <div class="form-actions" style="display: flex; justify-content: flex-end; gap: 15px; padding: 20px 0; margin-top: 20px;">
        <MudButton Variant="Variant.Outlined"
                   OnClick="Cancel"
                   Color="Color.Secondary"
                   Style="min-height: 35px; height: 35px; padding: 8px 20px; font-size: 0.9rem; width: 100px;"
                   Class="uniform-button">
            Cancel
        </MudButton>
        <MudButton Variant="Variant.Filled"
                   OnClick="OnSubmit"
                   Color="Color.Primary"
                   Style="min-height: 35px; height: 35px; padding: 8px 20px; font-size: 0.9rem; width: 100px;"
                   Class="uniform-button">
            Submit
        </MudButton>
    </div>
</div>

<SfDialog @ref="PaymentPostingModal"
          Width="95%"
          Height="90%"
          IsModal="true"
          Visible="@isPaymentPostingModalVisible"
          CssClass="payment-posting-modal"
          ShowCloseIcon="true"
          CloseOnEscape="false">
    <DialogTemplates>
        <Header>
            <div class="modal-header-content" style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background-color: white;">
                <h3 style="color: black; margin: 0; font-weight: 600; font-size: 18px; flex-grow: 1;">
                    Payment Posting (@ERARecord.PayerName)
                </h3>
            </div>
        </Header>
        <Content>
            <div class="payment-posting-content" style="padding: 20px; height: 100%; overflow-y: auto;">
                
                <!-- Top Section with Payment and Claim Info -->
                <div class="payment-posting-info-section" style="display: flex; gap: 20px; margin-bottom: 20px;">
                    
                    <!-- Left Section - Payment and Claim Info (Reduced Size) -->
                    <div class="payment-claim-info-box" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.1); flex: 0 0 350px;">
                        <div style="display: flex; flex-direction: column; gap: 10px;">
                            <!-- Payment Info Row -->
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <label style="color: #495057; font-weight: 500; min-width: 60px; font-size: 13px;">Payment :</label>
                                    <SfTextBox @bind-Value="ERARecord.PaymentPostingId" Width="70px" Style="height: 28px; font-weight: bold; font-size: 12px;"></SfTextBox>
                                </div>
                            </div>
                            
                            <!-- Claim Info Row -->
                            <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <label style="color: #495057; font-weight: 500; min-width: 60px; font-size: 13px;">Claim No :</label>
                                    <SfTextBox @bind-Value="ERARecord.ClaimNumber" Width="80px" Style="height: 28px; font-size: 12px;"></SfTextBox>
                                </div>
                                
                                <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                           Style="min-height: 28px; height: 28px; padding: 4px 12px; font-size: 0.75rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                                    View Claim
                                </MudButton>
                            </div>
                            
                            <!-- Patient and Provider Row -->
                            <div style="display: flex; flex-direction: column; gap: 6px;">
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <label style="color: #495057; font-weight: 500; min-width: 50px; font-size: 13px;">Patient :</label>
                                    <span style="font-weight: 600; color: #2c3e50; font-size: 13px;">@ERARecord.PatientName</span>
                                </div>
                                
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <label style="color: #495057; font-weight: 500; min-width: 50px; font-size: 13px;">Provider :</label>
                                    <span style="font-weight: 600; color: #2c3e50; font-size: 13px;">@ERARecord.ProviderName</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Right Section - Insurance and Payment Info Side by Side -->
                    <div class="insurance-payment-container" style="flex: 1; display: flex; gap: 15px;">
                        
                        <!-- Insurance Section -->
                        <div class="insurance-box" style="background: #ffffff; padding: 12px; border-radius: 6px; border: 1px solid #3a4a90; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 600; color: #495057; font-size: 13px;">Insurance(s)</span>
                                <MudButton Variant="Variant.Text" Size="Size.Small" Style="min-height: 22px; padding: 2px 6px; font-size: 0.7rem;">
                                    Refresh
                                </MudButton>
                            </div>
                            <div style="background: #3a4a90; color: white; padding: 8px; border-radius: 4px; text-align: center; display: flex; justify-content: space-between;">
                                <div style="font-weight: 500; font-size: 11px; flex: 1; text-align: left;">InsuranceType</div>
                                <div style="font-size: 10px; flex: 1; text-align: right;">InsuranceDetails</div>
                            </div>
                        </div>

                        
                        <!-- Payment Section -->
                        <div class="payment-box" style="background: #ffffff; padding: 12px; border-radius: 6px; border: 1px solid #3a4a90; flex: 1; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 600; color: #495057; font-size: 13px;">Payment(s)</span>
                                <MudButton Variant="Variant.Text" Size="Size.Small" Style="min-height: 22px; padding: 2px 6px; font-size: 0.7rem;">
                                    View Payment
                                </MudButton>
                            </div>
                            <div style="background: #e9ecef; padding: 8px; border-radius: 4px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 6px; font-size: 11px; text-align: center;">
                                    <div style="font-weight: 600;">Paid</div>
                                    <div style="font-weight: 600;">Date</div>
                                    <div style="font-weight: 600;">Pmt #</div>
                                    <div style="font-weight: 600;">Payer</div>
                                    <div style="color: #28a745;">@ERARecord.PaidAmount1</div>
                                    <div>@ERARecord.PaymentDate1</div>
                                    <div>@ERARecord.PaymentNumber1</div>
                                    <div>@ERARecord.PayerName1</div>
                                    <div style="color: #dc3545;">@ERARecord.PaidAmount2</div>
                                    <div>@ERARecord.PaymentDate2</div>
                                    <div>@ERARecord.PaymentNumber2</div>
                                    <div>@ERARecord.PayerName2</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CPT Payments Section -->
                <div class="cpt-payments-section" style="border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <!-- Controls Row -->
                    <div style="padding: 15px 20px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                        <span style="font-weight: 600; font-size: 16px; color: #495057; margin-right: 10px;">CPT Payments</span>
                        
                        <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                   Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                            Check
                        </MudButton>

                        <label style="font-size: 0.8rem; color: #495057; margin-right: 8px; font-weight: 500;">
                            Fee Schedule
                        </label>

                        <SfDropDownList TValue="string" TItem="string"
                                        CssClass="compact-textbox custom-dropdown"
                                        Placeholder="Select Fee Schedule"
                                        DataSource="@feeScheduleOptions"
                                        @bind-Value="ERARecord.FeeSchedule"
                                        Width="180px"
                                        Style="height: 32px;">
                        </SfDropDownList>



                        <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                   Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                            CPT Adjs
                        </MudButton>

                        <MudButton Variant="Variant.Outlined" Size="Size.Small"
                                   Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #ffffff; border-color: #6c757d; color: #495057;">
                            CPT Pmts
                        </MudButton>

                        <MudButton Variant="Variant.Filled" Size="Size.Small"
                                   Style="min-height: 32px; height: 32px; padding: 6px 16px; font-size: 0.8rem; background: #dc3545; color: white; border: none;">
                            Show Msg Codes
                        </MudButton>
                    </div>

                    <!-- CPT Payments Grid -->
                    <div class="grid-container" style="padding: 0;">
                        <SfGrid @ref="CPTPaymentsGrid"
                                TValue="ERACPTPayment"
                                Style="font-size: 0.85rem;"
                                DataSource="@cptPaymentsData"
                                AllowPaging="true"
                                PageSettings-PageSize="5"
                                GridLines="GridLine.Both"
                                AllowEditing="true"
                                AllowScrolling="true"
                                Width="100%"
                                Height="350px"
                                Toolbar="@(new List<string>() { "Add" })">
                            
                            <!-- Add ScrollSettings for horizontal scrolling -->
                            <GridScrollSettings EnableVirtualization="false"></GridScrollSettings>
                            
                            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridEvents OnActionComplete="CPTPaymentActionCompletedHandler" 
                                       OnActionBegin="CPTPaymentActionBeginHandler" 
                                       TValue="ERACPTPayment"></GridEvents>
                            <GridColumns>
                                <GridColumn Field="@nameof(ERACPTPayment.CPTPaymentId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.ServiceDate)"
                                            HeaderText="Service Dt"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Center"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="MM/dd/yyyy"
                                            Type="ColumnType.Date">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.POS)"
                                            HeaderText="POS"
                                            Width="80"
                                            MinWidth="60"
                                            TextAlign="TextAlign.Center"
                                            HeaderTextAlign="TextAlign.Center">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Units)"
                                            HeaderText="Units"
                                            Width="80"
                                            MinWidth="60"
                                            TextAlign="TextAlign.Center"
                                            HeaderTextAlign="TextAlign.Center"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Code)"
                                            HeaderText="Code"
                                            Width="100"
                                            MinWidth="80"
                                            TextAlign="TextAlign.Center"
                                            HeaderTextAlign="TextAlign.Center">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Billed)"
                                            HeaderText="Billed"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Balance)"
                                            HeaderText="Balance"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Allowed)"
                                            HeaderText="Allowed"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Deduct)"
                                            HeaderText="Deduct"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Coins)"
                                            HeaderText="Coins"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.CoPay)"
                                            HeaderText="CoPay"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Paid)"
                                            HeaderText="Paid"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Adjust)"
                                            HeaderText="Adjust"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.Withhold)"
                                            HeaderText="Withhold"
                                            Width="120"
                                            MinWidth="100"
                                            TextAlign="TextAlign.Right"
                                            HeaderTextAlign="TextAlign.Center"
                                            Format="C2"
                                            Type="ColumnType.Number">
                                </GridColumn>
                                <GridColumn Field="@nameof(ERACPTPayment.DenialCode)"
                                            HeaderText="Denial"
                                            Width="100"
                                            MinWidth="80"
                                            TextAlign="TextAlign.Center"
                                            HeaderTextAlign="TextAlign.Center">
                                </GridColumn>
                                <GridColumn HeaderText="Actions"
                                            Width="150"
                                            MinWidth="120"
                                            TextAlign="TextAlign.Center"
                                            HeaderTextAlign="TextAlign.Center">
                                    <GridCommandColumns>
                                        <GridCommandColumn Type="CommandButtonType.Edit"
                                                           ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                        <GridCommandColumn Type="CommandButtonType.Delete"
                                                           ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                        <GridCommandColumn Type="CommandButtonType.Save"
                                                           ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                        <GridCommandColumn Type="CommandButtonType.Cancel"
                                                           ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                    </GridCommandColumns>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            </div>
        </Content>
        <FooterTemplate>
            <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 15px; padding: 15px 20px; border-top: 1px solid #dee2e6; background: #f8f9fa;">
                <MudButton Variant="Variant.Outlined"
                           OnClick="CancelPaymentPosting"
                           Color="Color.Secondary"
                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;"
                           Class="uniform-button">
                    Cancel
                </MudButton>
                <MudButton Variant="Variant.Filled"
                           OnClick="SavePaymentPosting"
                           Color="Color.Primary"
                           Style="min-height: 30px; height: 35px; padding: 2px 16px; font-size: 0.8rem; width: 90px;"
                           Class="uniform-button">
                    Save
                </MudButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>



<style>
    @@keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Main form container - properly contained */
    .era-form {
        max-width: none !important;
        width: 98%;
        margin: 2rem auto;
        padding: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        color: #333;
        overflow-x: auto; /* Allow horizontal scroll only when needed */
        overflow-y: visible; /* Allow floating headers to be visible */
        box-sizing: border-box;
    }

    /* Grid container styling - Constrain grid within container but allow header visibility */
    .grid-container {
        width: 100%;
        overflow: visible !important; /* Allow floating header to be visible */
        position: relative;
    }

        /* Force Syncfusion Grid to stay within container bounds */
        .grid-container .e-grid {
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
        }

            /* Let grid content handle horizontal scrolling within bounds */
            .grid-container .e-grid .e-gridcontent {
                overflow-x: auto !important;
                overflow-y: visible !important;
                max-width: 100% !important;
            }

    /* Style the grid's internal scrollbar */
    .e-grid .e-gridcontent::-webkit-scrollbar {
        height: 8px;
    }

    .e-grid .e-gridcontent::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .e-grid .e-gridcontent::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

        .e-grid .e-gridcontent::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

    /* ERA sections styling - make them responsive and contained */
    .era-main-container {
        display: flex !important;
        flex-direction: column !important;
        gap: 50px !important;
        width: 100%;
        max-width: 100%;
        overflow: visible; /* Allow floating headers to be visible */
        box-sizing: border-box;
    }

    .era-process-container,
    .filter-era-container,
    .era-grid-container {
        width: 100%;
        max-width: 100%; /* Prevent containers from exceeding parent width */
        min-width: 800px; /* Ensure minimum width for readability */
        overflow: visible; /* Allow floating headers to be visible */
        box-sizing: border-box; /* Include padding and border in width calculation */
    }

    /* Responsive adjustments */
    @@media (max-width: 1600px) {
        .era-form {
            width: 100%;
            padding: 1rem;
            margin: 1rem;
        }
    }

    /* Custom styling for dropdowns and datepicker */
    .custom-dropdown .e-input-group {
        border: 1px solid #ced4da !important;
        border-radius: 4px;
        background-color: #fff;
    }

        .custom-dropdown .e-input-group:hover {
            border-color: #80bdff !important;
        }

        .custom-dropdown .e-input-group.e-input-focus {
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

    .custom-datepicker .e-input-group {
        border: 1px solid #ced4da !important;
        border-radius: 4px;
        background-color: #fff;
    }

        .custom-datepicker .e-input-group:hover {
            border-color: #80bdff !important;
        }

        .custom-datepicker .e-input-group.e-input-focus {
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

    /* Custom styling for textboxes */
    .custom-textbox .e-input-group {
        border: 1px solid #ced4da !important;
        border-radius: 4px;
        background-color: #fff;
    }

        .custom-textbox .e-input-group:hover {
            border-color: #80bdff !important;
        }

        .custom-textbox .e-input-group.e-input-focus {
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

    /* Grid styling */
    .custom-grid .e-grid .e-gridheader {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
    }

    .custom-grid .e-grid .e-headercell {
        font-weight: 600;
        color: #495057;
    }

    /* ERA Form styling */
    .form-header {
        position: relative;
        margin-bottom: 2rem;
    }

    .form-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
        padding-bottom: 0.5rem;
    }

    .header-accent {
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
        width: 80px;
        margin-top: 0.5rem;
    }
</style>