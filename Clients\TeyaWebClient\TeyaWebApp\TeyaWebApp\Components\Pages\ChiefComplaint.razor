﻿@page "/chiefcomplaint"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using Syncfusion.Blazor.DropDowns
@using TeyaWebApp.Model
@inject IChiefComplaintService ChiefComplaintService
@inject ISnackbar Snackbar
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using MudBlazor
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@inject HttpClient Http
@using Blazored.SessionStorage;
@inject Blazored.SessionStorage.ISessionStorageService sessionStorage


<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(richTextContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)richTextContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor @ref="richTextEditor" SaveInterval="saveInterval" Value="@richTextContent" ValueChanged="@((string newValue) =>
                      HandleRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@GetToolbarItems()">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="add">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                               Size="Size.Small"
                                               OnClick="@(() => OpenBrowsePopupAsync())" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>


<MudDialog @ref="showBrowsePopup" Style="width: 60vw; max-width: 800px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["ChiefComplaint"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Medium" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
       
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="4">
                        <SfAutoComplete TValue="string" style="height:40px;" TItem="ChiefComplaintDTO" Placeholder="e.g. Headache"
                                        @bind-Value="complaintDescription"
                                        DataSource="LocalData"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.StartsWith">
                           
                            <AutoCompleteFieldSettings Value="Description" />
                        </SfAutoComplete>

                    </MudItem>

                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddComplaint"
                                   Variant="Variant.Filled"
                                   Dense="true"
                           Style="min-width: 80px; height: 40px; padding: 2px 16px; font-size: 0.8rem;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <SfGrid @ref="ComplaintsGrid" TValue="ChiefComplaintDTO" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@complaints" AllowPaging="true" PageSettings-PageSize="5" AllowEditing="true" GridLines="GridLine.Both">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="5"></GridPageSettings>
                    <GridEvents OnActionBegin="ActionBeginHandler" TValue="ChiefComplaintDTO"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="Description" HeaderText="@Localizer["Description"]" TextAlign="TextAlign.Center" Width="150"></GridColumn>
                        <GridColumn Field="DateOfComplaint" HeaderText="@Localizer["Date"]" Width="50"
                                    TextAlign="TextAlign.Center" Format="MM/dd/yy">
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="50">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            

            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           Dense="true"
                           OnClick="CancelChanges"
                       Style="min-width: 120px; height: 35px; font-weight: 600; padding: 2px 16px; font-size: 0.8rem;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                       Style="min-width: 120px; height: 35px; font-weight: 600; padding: 2px 16px; font-size: 0.8rem;">
                    @Localizer["Save"]
                </MudButton>

            </div>
       
    </DialogContent>
</MudDialog>

<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>
