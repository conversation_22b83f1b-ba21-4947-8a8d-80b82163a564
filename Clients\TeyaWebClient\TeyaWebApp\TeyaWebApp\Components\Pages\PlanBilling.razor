﻿@page "/planbilling"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "PlanBillingAccessPolicy")]
@layout Admin
@using TeyaWebApp.Components.Layout
@using TeyaUIViewModels.ViewModel
@using TeyaUIModels.Model
@using TeyaWebApp.ViewModel
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.RichTextEditor
@using MudBlazor

<MudContainer MaxWidth="MaxWidth.False" Class="p-6">
    <!-- Header Section -->
    <MudPaper Class="p-4 mb-4">
        <MudText Typo="Typo.h5">Billing (Name - 06/03/2025 12:35 AM)</MudText>
    </MudPaper>

    <!-- Tabs Section -->
    <MudTabs>
        <MudTabPanel Text="Patient Info">
            <MudText>Patient Information Goes Here</MudText>
        </MudTabPanel>
        <MudTabPanel Text="Encounter">
            <MudText>Encounter Details Go Here</MudText>
        </MudTabPanel>
        <MudTabPanel Text="Physical">
            <MudText>Physical Details Go Here</MudText>
        </MudTabPanel>
    </MudTabs>

    <!-- Attachments Section -->
    <MudPaper Class="p-4 mt-4">
        <MudGrid>
            <MudItem xs="12" Class="mb-3">
                <MudButton Variant="Variant.Outlined" Color="Color.Primary">Add</MudButton>
                <MudButton Variant="Variant.Outlined" Color="Color.Secondary" Class="ml-2">Remove</MudButton>
            </MudItem>
            <MudTable Bordered="true" Class="mb-3" T="object">
                <HeaderContent>
                    <MudTh>Code</MudTh>
                    <MudTh>Diagnosis</MudTh>
                    <MudTh>Specify</MudTh>
                    <MudTh>Notes</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd><MudCheckBox T="bool" /></MudTd>
                    <MudTd>382.00</MudTd>
                    <MudTd>Otitis Media</MudTd>
                    <MudTd><MudTextField T="string" Variant="Variant.Filled" FullWidth="true" /></MudTd>
                </RowTemplate>
            </MudTable>
        </MudGrid>
    </MudPaper>

    <!-- Procedure Codes Section -->
    <MudPaper Class="p-4 mt-4">
        <SfGrid DataSource="@ProcedureCodes" AllowPaging="true" AllowSorting="true">
            <GridColumns>
                <GridColumn Field="CHCode" HeaderText="CH. Code" Width="100"></GridColumn>
                <GridColumn Field="CPT" HeaderText="CPT" Width="150"></GridColumn>
                <GridColumn Field="Name" HeaderText="Name" Width="200"></GridColumn>
                <GridColumn Field="Units" HeaderText="Units" Width="100"></GridColumn>
                <GridColumn Field="ICD1" HeaderText="ICD1" Width="100"></GridColumn>
                <GridColumn Field="ICD2" HeaderText="ICD2" Width="100"></GridColumn>
            </GridColumns>
        </SfGrid>
    </MudPaper>

    <!-- Billing Notes and Follow-Up -->
    <MudPaper Class="p-4 mt-4">
        <MudGrid>
            <MudItem xs="12">
                <label style="font-size: 16px; font-weight: bold;">Billing Notes</label>
                <SfRichTextEditor @bind-Value="@BillingNotes" ToolbarSettings="@(new RichTextEditorToolbarSettings { Type = ToolbarType.Expand })" Width="100%" Height="300px">
                    <RichTextEditorToolbar>
                        <RichTextEditorToolbarItems>
                            <RichTextEditorToolbarItem Commands="new[] { ToolbarItem.Bold, ToolbarItem.Italic, ToolbarItem.Underline, ToolbarItem.Strikethrough, ToolbarItem.FontName, ToolbarItem.FontSize, ToolbarItem.Indent, ToolbarItem.Outdent, ToolbarItem.OrderedList, ToolbarItem.UnorderedList, ToolbarItem.CreateLink, ToolbarItem.Image, ToolbarItem.ClearFormat, ToolbarItem.SourceCode }" />
                        </RichTextEditorToolbarItems>
                    </RichTextEditorToolbar>
                    <RichTextEditorContent>
                        <p>Enter notes here...</p>
                    </RichTextEditorContent>
                </SfRichTextEditor>
            </MudItem>
            <MudItem xs="12" Class="mt-3">
                <MudCheckBox T="bool">Follow Up N/A</MudCheckBox>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- Footer Buttons -->
    <MudGrid Class="mt-4">
        <MudItem xs="4">
            <MudButton Variant="Variant.Outlined">Confidential Chart</MudButton>
        </MudItem>
        <MudItem xs="4">
            <MudButton Variant="Variant.Text">Close</MudButton>
        </MudItem>
        <MudItem xs="4">
            <MudButton Variant="Variant.Filled" Color="Color.Primary">Done</MudButton>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    public class ProcedureCode
    {
        public string CHCode { get; set; }
        public string CPT { get; set; }
        public string Name { get; set; }
        public string Units { get; set; }
        public string ICD1 { get; set; }
        public string ICD2 { get; set; }
    }

    private List<ProcedureCode> ProcedureCodes = new()
    {
        new ProcedureCode { CHCode = "0", CPT = "49392", Name = "EST - PREV 1-4YRS", Units = "1", ICD1 = "N/A", ICD2 = "N/A" },
    };

    private string BillingNotes { get; set; } = string.Empty;
}
