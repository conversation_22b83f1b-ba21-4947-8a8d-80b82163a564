using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIModels.Model.Billing;
using TeyaUIViewModels.ViewModel.Billing.Classes;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static TeyaWebApp.Components.Pages.Encounters;

namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class BatchPayments : ComponentBase
    {

        private List<BatchPaymentsData> BatchPayment;
        private List<Member> AllMembers;
        private Guid OrgID { get; set; }
        private bool Subscription { get; set; }
        private Guid? PatientID { get; set; }
        private bool IsReadOnly { get; set; } = true;
        private List<Member> ProviderMembers { get; set; }
        private string patientFilter = string.Empty;
        private string providerFilter = string.Empty;
        private DateTime? dateFromFilter;
        private DateTime? dateToFilter;
        private string? paymentFromFilter;
        private string? postedByFilter;
        private string? checkNoFilter;
        private DateTime? checkDateFilter;
        private string? paymentNoFilter;
        [Inject] 
        private ActiveUser User { get; set; }
        [Inject] 
        UserContext UserContext { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrgID = UserContext.ActiveUserOrganizationID;
                Subscription = UserContext.ActiveUserSubscription;
                AllMembers = await MemberService.GetAllMembersAsync(OrgID, Subscription);
                ProviderMembers = AllMembers.Where(m => m.RoleName == "Provider").ToList();
                BatchPayment = await BatchPaymentService.GetAllByOrgIdAsync(OrgID, Subscription);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing Encounters: {ex.Message}");
            }
        }
        private List<BatchPaymentsData> filteredPayments => BatchPayment?
             .Where(r =>
                 (!dateFromFilter.HasValue ||
                 (r.Date >= dateFromFilter.Value)))
             .Where(r =>
                 (!dateToFilter.HasValue ||
                 (r.Date <= dateToFilter.Value)))
             .ToList() ?? new List<BatchPaymentsData>();

        private void ResetFilters()
        {
            paymentFromFilter = string.Empty;
            checkNoFilter = string.Empty;
            checkDateFilter = null;
            paymentNoFilter = string.Empty;
            patientFilter = string.Empty;
            providerFilter = string.Empty;
            dateFromFilter = null;
            dateToFilter = null;
        }
    }
}

