﻿ @page "/examinations"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject ISnackbar Snackbar
@using TeyaUIModels.Model
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject HttpClient Http

<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="edit">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" Size="Size.Small" OnClick="OpenExaminationDialog" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>

<MudDialog @ref="_examinationDialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Examinations"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>

        <SfGrid @ref="ExaminationGrid" TValue="Examination" DataSource="@activeExaminationEntries" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both"
                Toolbar="@(new List<string>() { "Add"})">
            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
            <GridColumns>
                <GridColumn Field="ExaminationId" IsPrimaryKey="true" Visible="false"></GridColumn>
                <GridColumn Field="CreatedDate" HeaderText="@Localizer["CreatedDate"]" TextAlign="TextAlign.Center" Width="120" Format="MM/dd/yyyy" AllowEditing="false" ></GridColumn>
                <GridColumn Field="GeneralDescription" HeaderText="@Localizer["GeneralDescription"]" TextAlign="TextAlign.Center" Width="150"></GridColumn>
                <GridColumn Field="HEENT" HeaderText="@Localizer["HEENT"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                <GridColumn Field="Lungs" HeaderText="@Localizer["Lungs"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                <GridColumn Field="Abdomen" HeaderText="@Localizer["Abdomen"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                <GridColumn Field="PeripheralPulses" HeaderText="@Localizer["PeripheralPulses"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                <GridColumn Field="Skin" HeaderText="@Localizer["Skin"]" TextAlign="TextAlign.Center" Width="150"></GridColumn>
                <GridColumn Field="Others" HeaderText="@Localizer["Others"]" TextAlign="TextAlign.Center" Width="150"></GridColumn>
                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                    <GridCommandColumns>
                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                    </GridCommandColumns>
                </GridColumn>
            </GridColumns>
            <GridEvents TValue="Examination" OnActionBegin="ActionBeginHandler" OnActionComplete="ActionCompleteHandler"></GridEvents>
        </SfGrid>
        <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 16px;margin-bottom:6px;">
            <MudButton Style="width: 100px; height: 35px;padding: 2px 16px;font-size: 0.8rem;" Color="Color.Secondary" OnClick="CancelChanges" Variant="Variant.Outlined" Dense="true">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Style="width: 100px; height: 35px;padding: 2px 16px;font-size: 0.8rem;" Color="Color.Primary" OnClick="SaveChanges" Variant="Variant.Filled" Dense="true">
                @Localizer["Save"]
            </MudButton>
        </div>
    </DialogContent>
</MudDialog>


<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>
