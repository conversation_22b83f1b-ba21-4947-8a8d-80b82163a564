﻿@page "/encounters"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IProgressNotesService ProgressNotesService
@inject HttpClient Http
@using Markdig

<GenericCard>
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4 my-gen-card">
        @if (records == null)
        {
            <MudAlert Severity="Severity.Info">@Localizer["Loading..."]</MudAlert>
        }
         else if (!records.Any())
        {
           
            <MudAlert Severity="Severity.Info">@Localizer["NoRecordsFound"]</MudAlert>
        }
        else if (selectedRecord == null)
        {

            <div class="filter-container mb-5">
                <MudPaper Class="pa-2" Elevation="1">
                    <div class="d-flex flex-wrap gap-3">

                        @if (_PatientService.PatientData == null)
                        {
                            <div style="width: 220px;">
                                <MudTextField @bind-Value="patientFilter"
                                              Placeholder="Search By Patient Name..."
                                              Label="Patient"
                                              Variant="Variant.Outlined"
                                              Margin="Margin.Dense"
                                              Clearable="true"
                                              Immediate="true" />
                            </div>
                        }
                       

                        <div style="width: 220px;">
                            <MudTextField @bind-Value="providerFilter"
                                          Placeholder="Search By Provider Name..."
                                          Label="Provider"
                                          Variant="Variant.Outlined"
                                          Margin="Margin.Dense"
                                          Clearable="true"
                                          Immediate="true" />
                        </div>

                        <div style="width: 220px;">
                            <MudTextField @bind-Value="facilityFilter"
                                          Placeholder="Search By Facility..."
                                          Label="Facility"
                                          Variant="Variant.Outlined"
                                          Margin="Margin.Dense"
                                          Clearable="true"
                                          Immediate="true" />
                        </div>

                        <div style="width: 220px;">
                            <MudTextField @bind-Value="visitTypeFilter"
                                          Placeholder="Search By Visit Type..."
                                          Label="Visit Type"
                                          Variant="Variant.Outlined"
                                          Margin="Margin.Dense"
                                          Clearable="true"
                                          Immediate="true" />
                        </div>

                        <div style="width: 220px;">
                            <MudDatePicker @bind-Date="selectedDate"
                                           Placeholder="Search by Date..."
                                           Label="Date"
                                           Variant="Variant.Outlined"
                                           Margin="Margin.Dense"
                                           Clearable="true" />
                        </div>
                        @if (_PatientService.PatientData == null)
                        {
                            <div style="width: 220px;">
                                <MudTextField @bind-Value="mrnFilter"
                                              Placeholder="Search By MRN..."
                                              Label="MRN"
                                              Variant="Variant.Outlined"
                                              Margin="Margin.Dense"
                                              Clearable="true"
                                              Immediate="true" />
                            </div>
                        }
                       

                        <div style ="width: 220px;">
                            <MudSelect T="string"
                                       @bind-Value="statusFilter"
                                       Placeholder="Search by Status..."
                                       Label="Status"
                                       Variant="Variant.Outlined"
                                       Margin="Margin.Dense"
                                       Style="width: 225px;"
                                       Clearable="true">
                                <MudSelectItem Value="@("Draft")">@Localizer["Draft"]</MudSelectItem>
                                <MudSelectItem Value="@("Signed")">@Localizer["Signed"]</MudSelectItem>
                            </MudSelect>

                        </div>
                        
                        <div style="align-self: end;margin-bottom:7px;">
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Primary"
                                       Size="Size.Small"
                                       Style="height:35px;"
                                       OnClick="ResetFilters">
                                Reset
                            </MudButton>
                        </div>
                    </div>
                </MudPaper>
            </div>



            <div class="grid-container">
                <SfGrid @ref="Grid" DataSource="@filteredGridData" AllowPaging="true" GridLines="GridLine.Both">
                    <GridEvents TValue="GridRecord" RowSelected="@OnRowSelected"></GridEvents>

                    <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="false"></GridEditSettings>
                    <GridColumns>
                        <GridColumn Field=@nameof(GridRecord.MRN) HeaderText="MRN" Width="90" AllowEditing="true" TextAlign="TextAlign.Center">

                        </GridColumn>

                        <GridColumn Field=@nameof(GridRecord.DateTime) HeaderText="Date" Width="100" AllowEditing="false" TextAlign="TextAlign.Center"
                                    Format="MMM dd, yyyy">
                        </GridColumn>

                        <GridColumn Field=@nameof(GridRecord.Physician) HeaderText="Provider" Width="110" AllowEditing="false" TextAlign="TextAlign.Center">
                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.PatientName) HeaderText="Patient" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">

                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.ChiefComplaint) HeaderText="Chief Complaint" AllowEditing="false" TextAlign="TextAlign.Left">
                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.Status) HeaderText="Status" Width="90" AllowEditing="true" TextAlign="TextAlign.Center">

                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.VisitType) HeaderText="Visit Type" Width="90" AllowEditing="true" TextAlign="TextAlign.Center">

                        </GridColumn>
                        <GridColumn Field=@nameof(GridRecord.Facility) HeaderText="Facility" Width="90" AllowEditing="true" TextAlign="TextAlign.Center">

                        </GridColumn>
                        <GridColumn HeaderText="Appointment Details" Width="120" TextAlign="TextAlign.Center">
                            <Template Context="data">
                                @{
                                    var row = (Encounters.GridRecord)data;
                                }
                                <MudButton Variant="Variant.Outlined"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                          
                                           OnClick="@(async () => await ViewAppointment(row?.AppointmentId,row.MRN))"
                                           Style="min-width: 30px; height: 30px;font-size: 0.7rem;">
                                    View
                                </MudButton>

                            </Template>
                       </GridColumn>


                    </GridColumns>
                </SfGrid>
            </div>

            @if (showAppointmentSection && selectedAppointment != null)
            {
                <div class="mt-4 pa-4 mud-paper">
                    <h5 class="mb-4 pb-2 px-2 pt-2 d-flex align-items-center">
                        <MudIcon Icon="@Icons.Material.Filled.EventNote" Class="me-2" />
                        Appointment Details
                    </h5>


                    <MudGrid Class="appointment-grid">
                        <!-- Left Section -->
                        <MudItem xs="12" md="3">
                            <p><strong>@Localizer["Patient"]:</strong> @selectedAppointment.PatientName</p>
                            <p><strong>@Localizer["Provider"]:</strong> @selectedAppointment.Provider</p>
                            <p><strong>@Localizer["VisitType"]:</strong> @selectedAppointment.VisitType</p>
                            <p><strong>@Localizer["Date"]:</strong> @selectedAppointment.AppointmentDate.ToString("MMM dd, yyyy")</p>
                        </MudItem>

                        <!-- Middle Section -->
                        <MudItem xs="12" md="3">
                            <p><strong>@Localizer["Facility"]:</strong> @selectedAppointment.Facility</p>
                            <p><strong>@Localizer["RoomNo"]:</strong> @selectedAppointment.RoomNumber</p>
                            <p>
                                <strong>@Localizer["Timing"]:</strong>
                                @selectedAppointment.StartTime?.ToString("hh:mm tt")
                                @Localizer["to"]
                                @selectedAppointment.EndTime?.ToString("hh:mm tt")
                            </p>
                            <p><strong>@Localizer["PatientMRN"]:</strong> @selectedPatientMRN</p>
                        </MudItem>

                        <!-- Right Section (Wider) to  shows the noets and Reason-->
                        <MudItem xs="12" md="6">
                            <p><strong>@Localizer["Notes"]:</strong> @selectedAppointment.Notes</p>
                            <p><strong>@Localizer["Reason"]:</strong> @selectedAppointment.Reason</p>
                        </MudItem>
                    </MudGrid>


                    <div class="d-flex justify-content-end mt-3 me-2 mb-2">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Secondary"
                                   Size="Size.Small"
                                   Style="Width: 80px; height: 33px;"
                                   OnClick="@(() => showAppointmentSection = false)">
                            Close
                        </MudButton>
                    </div>


                </div>

            }
            else if (showAppointmentSection && selectedAppointment == null)
            {
                <MudPaper Class="mt-4 pa-4 d-flex align-items-center justify-content-center" Elevation="1" Style="min-height: 150px;">
                    <MudIcon Icon="@Icons.Material.Filled.Info" Color="Color.Primary" Class="me-2" />
                    <p class="m-0">No appointment details available</p>
                </MudPaper>

                <div class="d-flex justify-content-end mt-3 me-2 mb-2">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               Size="Size.Small"
                               Style="Width: 80px; height: 33px;"
                               OnClick="@(() => showAppointmentSection = false)">
                        Close
                    </MudButton>
                </div>
            }

        }
    </MudContainer>
           
        
</GenericCard>



<style>

    .filter-input {
        width: 220px;
        min-width: 220px;
        max-width: 250px;
    }


    .my-gen-card {
        padding: 0px !important;
    }

    .mud-card-header {
        padding: 0 !important;
    }

    .mud-card-content {
        padding: 0px !important;
        padding-left: 2% !important;
    }

    /* Grid Rows */
    .e-row {
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
    }

        .e-row:hover {
            background-color: #f5faff;
            cursor: pointer;
        }

    .e-altrow {
        background-color: #fcfcfc;

    }
</style>
