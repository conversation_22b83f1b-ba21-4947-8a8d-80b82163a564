﻿@page "/assessments"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System
@inject IDiagnosticImagingService DiagnosticImagingService
@using Syncfusion.Blazor.DropDowns


<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="Symbol">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                               OnClick="OpenNewDialogBox"
                                               Size="Size.Small" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>

<MudDialog @ref="__Assessments" Style="width: 80vw; max-width: 1100px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Assessments"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
                <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="3">
                        <MudSelect T="string"
                                   Label="@Localizer["Select Database"]"
                                   Value="selectedDatabase"
                                   ValueChanged="OnDatabaseChanged"
                                   Dense="true"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Style="width: 100%;">
                            <MudSelectItem T="string" Value="@("CMS")">@Localizer["CMS.gov"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@("FDB")">@Localizer["PharmaceuticalDataSource"]</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                </MudGrid>

                <!-- Drug name search and Add button row -->
                <MudGrid Spacing="3" Style="align-items: center; margin-top: 8px;">
                    <MudItem xs="4">
                        <MudAutocomplete T="string"
                                         Label="@Localizer["Search ICD By Codes or Description"]"
                                         Value="ICDName"
                                         ValueChanged="OnICDNameChanged"
                                         SearchFunc="SearchICDCodes"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense"
                                         MinCharacters="2"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewDiagnosis"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 70px; height: 35px;padding: 2px 16px;font-size: 0.8rem;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>
                <SfGrid @ref="AssessmentsGrid" TValue="AssessmentsData" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@_Assessments" AllowPaging="true" PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="AssessmentsData"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="AssessmentsID" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" TextAlign="TextAlign.Center" AllowEditing="false" Format="MM/dd/y" />
                        <GridColumn Field="Diagnosis" HeaderText="@Localizer["Diagnosis"]" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn Field="Specify" HeaderText="@Localizer["Specify"]" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Updated Date"]" TextAlign="TextAlign.Center" AllowEditing="false" Format="MM/dd/y" />
                        <GridColumn Field="@nameof(AssessmentsData.CheifComplaint)"
                                    HeaderText="@Localizer["Chief Complaint"]"
                                    TextAlign="TextAlign.Center"
                                    Width="200"
                                    EditTemplate="@ChiefComplaintEditTemplate">
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 100px; height: 35px;padding: 2px 16px;font-size: 0.8rem; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 100px; height: 35px;padding: 2px 16px;font-size: 0.8rem; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
            @* @if (showGrid) *@
            @* { *@
            @*     <MudPaper Class="pa-2" Style="height: 340px;"> *@
            @*         <MudGrid Spacing="2"> *@
            @*             <MudItem xs="12"> *@
            @*                 <MudText Typo="Typo.body1" Color="Color.Primary" Style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">"@Localizer["Diagnosis-Based Order Sets"]"</MudText> *@
            @*             </MudItem> *@
            @*             <MudItem xs="12"> *@
            @*                 <SfGrid TValue="CompleteOrderSet" DataSource="@filteredOrderSets" @ref=orderSetGrid AllowPaging="true"> *@
            @*                     <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings> *@
            @*                     <GridPageSettings PageSize="5"></GridPageSettings> *@
            @*                     <GridColumns> *@
            @*                         <GridColumn Field=orderSet.Id IsPrimaryKey="true" Visible="false"></GridColumn> *@
            @*                         <GridColumn Type="ColumnType.CheckBox" Width="50" ></GridColumn> *@
            @*                         <GridColumn Field=orderSet.OrderSetName HeaderText="Orderset Name" Width="200"></GridColumn> *@
            @*                         <GridColumn Field="orderSet.CreatedDate" HeaderText="Created Date" Width="200"></GridColumn> *@
            @*                     </GridColumns> *@
            @*                 </SfGrid> *@
            @*             </MudItem> *@

            @*         </MudGrid> *@
            @*         <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;"> *@
            @*             <MudButton Color="Color.Secondary" *@
            @*                        Variant="Variant.Outlined" *@
            @*                        OnClick="OnCancelClicked" *@
            @*                        Dense="true" *@
            @*                        Style="min-width: 120px; height: 40px; font-weight: 600;"> *@
            @*                 @Localizer["Cancel"] *@
            @*             </MudButton> *@
            @*             <MudButton Color="Color.Primary" *@
            @*                        Variant="Variant.Filled" *@
            @*                        OnClick="OnAddClicked" *@
            @*                        Dense="true" *@
            @*                        Style="min-width: 120px; height: 40px; font-weight: 600;"> *@
            @*                 @Localizer["Add"] *@
            @*             </MudButton> *@
            @*         </div> *@
            @*     </MudPaper> *@
            @* } *@
        </div>
    </DialogContent>
</MudDialog>


<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>