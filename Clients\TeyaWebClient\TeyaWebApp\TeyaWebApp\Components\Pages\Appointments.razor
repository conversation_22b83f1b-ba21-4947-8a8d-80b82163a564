﻿@page "/appointments"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIViewModels.ViewModels
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "AppointmentsAccessPolicy")]
@using Microsoft.Extensions.Localization
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Inputs
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using System.Collections.Generic
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns;
@using Syncfusion.Blazor.Grids;
@using Microsoft.AspNetCore.Components;
@using MudBlazor
@using TeyaWebApp.TeyaAIScribeResources
@layout Admin
@inject ILogger<SecuritySettings> Logger
@inject IJSRuntime JSRuntime
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IAppointmentService AppointmentService
@inject IFacilityService FacilityService
@inject IVisitTypeService VisitTypeService
@inject IVisitStatusService VisitStatusService
@inject IMemberService MemberService

<h3 style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 15px; margin-bottom: 2px; margin-left:27px;padding-left: 10px; padding-top : 8px; padding-bottom: 8px;"><strong>@Localizer["Appointments"]</strong></h3>

<div class="appointments-layout">
    <MudPaper Class="p-4" Style="margin-right: 16px;margin-left:27px;">
        <div class="controls-section" style="width:240px;">
            <SfCalendar TValue="DateTime" Value="@selectedDate" ValueChanged="@OnDateChanged"></SfCalendar>

            <p style="margin-bottom: 2px; padding-top : 8px">
                @Localizer["Facilities"]
            </p>
            <SfMultiSelect TValue="string[]" TItem="Facility"
                           CssClass="form-control-dropdown-compact"
                           Placeholder="@Localizer["Select Facility"]"
                           DataSource="@FacilityList"
                           Value="@selectedFacilities" ValueChanged="OnFacilitiesFilter"
                           PopupHeight="200px"
                           Width="243px"
                           Mode="VisualMode.Box">
                <MultiSelectFieldSettings Value="FacilityName" Text="FacilityName"></MultiSelectFieldSettings>
            </SfMultiSelect>

            <p style="margin-bottom: 2px; padding-top : 8px">
                @Localizer["Providers"]
            </p>
            <SfMultiSelect TValue="string[]" TItem="Member"
                           CssClass="form-control-dropdown-compact"
                           Placeholder="@Localizer["Select Provider"]"
                           DataSource="@ProviderListToFilter"
                           Value="@selectedProviders" ValueChanged="OnProvidersFilter"
                           PopupHeight="200px"
                           Width="243px"
                           Mode="VisualMode.Box">
                <MultiSelectFieldSettings Value="UserName" Text="UserName"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>
    </MudPaper>

    <MudPaper Class="p-4" Style="flex-grow: 1;">
        <SfButton CssClass="e-info" OnClick="OpenAddTaskDialog" style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;margin-bottom: 10px;font-weight: 500; color: white;">@Localizer["New Appointment"]</SfButton>
        @if (showSchedule)
        {
            <SfSchedule TValue="Appointment" CssClass="schedule-cell-dimension" @bind-SelectedDate="@selectedDate" StartHour="09:00" EndHour="17:30" CurrentView="View.Day" AllowDragAndDrop="false" Height="600px" @ref="ScheduleRef">
                <ScheduleViews>
                    <ScheduleView Option="View.Day" AllowVirtualScrolling="true"></ScheduleView>
                </ScheduleViews>
                <ScheduleTemplates>
                    <ResourceHeaderTemplate>
                        @{
                            var provider = (context as TemplateContext).ResourceData as ProviderResource;
                        }
                        <div style="font-size: 15px;">
                            @provider?.UserName
                        </div>
                    </ResourceHeaderTemplate>
                </ScheduleTemplates>
                <ScheduleGroup Resources="@resourceNames"></ScheduleGroup>
                <ScheduleResources>
                    <ScheduleResource TValue="Guid" TItem="ProviderResource" Field="ProviderId" Title="Provider" Name="Provider"
                                      DataSource="@CachedProviderResources"
                                      TextField="UserName" IdField="Id" ColorField="Color">
                    </ScheduleResource>
                </ScheduleResources>
                <ScheduleEvents TValue="Appointment"
                                OnEventClick="OnEventSelect"></ScheduleEvents>
                <ScheduleEventSettings DataSource="@appointments" AllowAdding="false">
                    <Template>
                        <div>@Localizer["Patient"]: @((context as Appointment).PatientName)</div>
                        <div>@Localizer["Provider"]: @((context as Appointment).Provider)</div>
                        <div>@Localizer["Time"]: @((context as Appointment).StartTime?.ToString("hh:mm tt")) - @((context as Appointment).EndTime?.ToString("hh:mm tt"))</div>
                    </Template>
                </ScheduleEventSettings>
            </SfSchedule>
        }
    </MudPaper>
</div>

<MudDialog @ref="_AddAppointmentdialog" Style="width: 85vw; max-width: 800px;">
    <TitleContent>
        <MudText Style="font-size: 1.5rem; font-weight: bold; color: #333;">
            @DialogTitle
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CloseAddTaskDialog" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <!-- Search Criteria, Search Term, and Button in the same line -->
        <MudPaper Class="p-4 mb-4">
        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px; width: 70%;">
            <SfDropDownList TValue="string" TItem="string"
                            Placeholder="Select Criteria"
                            DataSource="@searchCriteriaOptions"
                            @bind-Value="selectedCriteria"
                            Style="width: 180px;">
            </SfDropDownList>
            <SfTextBox id="searchUser"
                       Value="@selectedUserName"
                       ValueChanged="@(args => {
                           var safeValue = args?.ToString() ?? string.Empty;
                           selectedUserName = safeValue;
                           searchTerm = safeValue;
                       })"
                       Placeholder="@Localizer["Enter Search Term"]"
                       ShowClearButton="true"
                       Style="flex: 1; min-width: 200px; padding: 5px; border: 1px solid #ccc; border-radius: 5px;">
            </SfTextBox>
            <SfButton OnClick="@SearchUsers"
                      Style="background-color: #007bff; color: white; padding: 5px 15px; border-radius: 5px;">
                @Localizer["Search"]
            </SfButton>
        </div>

            @if (showNoResultsMessage)
            {
                <div style="margin-top: 5px;">@Localizer["No User Found"]</div>
            }
            else if (filteredUsers != null && filteredUsers.Count > 0)
            {
                <SfGrid TValue="Member" DataSource="@filteredUsers" AllowPaging="true" Style="margin-top: 10px;">
                    <GridPageSettings PageSize="3"></GridPageSettings>
                    <GridEvents RowSelected="RowSelectHandler" TValue="Member"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="@nameof(Member.SSN)" HeaderText="@Localizer["SSN"]"></GridColumn>
                        <GridColumn Field="@nameof(Member.MRN)" HeaderText="@Localizer["MRN"]"></GridColumn>
                       <GridColumn HeaderText="@Localizer["Name"]" Width="200">
                            <Template>
                                @{
                                    var member = context as Member;
                                }
                                @($"{member.FirstName} {member.LastName}")
                            </Template>
                        </GridColumn>
                        <GridColumn Field="@nameof(Member.PhoneNumber)" HeaderText="@Localizer["Phone Number"]"></GridColumn>
                        <GridColumn Field="@nameof(Member.DateOfBirth)" HeaderText="@Localizer["Date Of Birth"]" Format="d"></GridColumn>
                    </GridColumns>
                </SfGrid>
            }
        </MudPaper>

        <!-- Select Facility and Select Provider in the same line -->
       <MudPaper Class="p-4 mb-4">
        <div style="display: flex; gap: 10px; margin-top: 10px;">
           <SfAutoComplete TValue="string" TItem="Facility"
                    CssClass="form-control-dropdown-compact"
                    Placeholder="@Localizer["Select Facility"]"
                    DataSource="@FacilityList"
                    @bind-Value="selectedFacility"
                    AllowFiltering="true"
                    ShowClearButton="true"
                    FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                    PopupHeight="250px"
                    MinLength="0"
                    Autofill="false"
                    ShowPopupButton="true">
                    <AutoCompleteFieldSettings Value="FacilityName" Text="FacilityName" />
           </SfAutoComplete>


            <SfAutoComplete TValue="string" TItem="Member"
                CssClass="form-control-dropdown-compact"
                Placeholder="@Localizer["Select Provider"]"
                DataSource="@Provider_List"
                Value="@selectedProvider"
                ValueChanged="OnProviderChanged"
                AllowFiltering="true"
                ShowClearButton="true"
                FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                PopupHeight="250px"
                MinLength="0"
                Autofill="false"
                ShowPopupButton="true"
                Highlight="true">
                <AutoCompleteFieldSettings Value="UserName" Text="UserName"></AutoCompleteFieldSettings>
            </SfAutoComplete>


        </div>

            <div style="display: flex; gap: 10px; margin-top: 10px;">
                <SfDatePicker TValue="DateTime"
                              @bind-Value="@Date_Value"
                              Placeholder="@Localizer["Select Appointment Date"]">
                </SfDatePicker>
                <SfTimePicker TValue="DateTime?"
                              @bind-Value="@Start_Time"
                              Format="HH:mm"
                              ShowClearButton="false"
                              Step="30"
                              Min="@MinTime"
                              Max="@MaxTime"
                              Placeholder="@Localizer["Select Start Time"]">
                </SfTimePicker>
                <SfTimePicker TValue="DateTime?"
                              @bind-Value="@End_Time"
                              Format="HH:mm"
                              ShowClearButton="false"
                              Step="30"
                              Min="@MinTime"
                              Max="@MaxTime"
                              Placeholder="@Localizer["Select End Time"]">
                </SfTimePicker>
            </div>

        <!-- Visit Type and Visit Status in the same line -->
        <div style="display: flex; gap: 10px; margin-top: 10px;">
            <SfAutoComplete TValue="string" TItem="string"
                CssClass="form-control-dropdown-compact"
                Placeholder="@Localizer["Select Visit Type"]"
                DataSource="@visitTypeOptions"
                @bind-Value="selectedVisitType"
                AllowFiltering="true"
                ShowClearButton="true"
                FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                PopupHeight="250px"
                MinLength="0"
                Autofill="false"
                ShowPopupButton="true"
                Highlight="true">
            </SfAutoComplete>

            <SfAutoComplete TValue="string" TItem="string"
                CssClass="form-control-dropdown-compact"
                Placeholder="@Localizer["Select Visit Status"]"
                DataSource="@visitStatusOptions"
                @bind-Value="selectedVisitStatus"
                AllowFiltering="true"
                ShowClearButton="true"
                FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                PopupHeight="250px"
                MinLength="0"
                Autofill="false"
                ShowPopupButton="true"
                Highlight="true">
            </SfAutoComplete>

            <SfTextBox Placeholder="@Localizer["Enter Room Number"]"
                       @bind-Value="selectroomNumber">
            </SfTextBox>
        </div>

            <textarea id="reason" @bind="@selectedReason"
                      placeholder="@Localizer["Enter Reason"]"
                      style="width: 100%; margin-top: 10px;"></textarea>

            <textarea id="notes" @bind="@selectedNotes"
                      placeholder="@Localizer["Enter Notes"]"
                      style="width: 100%; margin-top: 10px;"></textarea>

            <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                <div>
                    @if (isEditMode)
                    {
                        <SfButton OnClick="@DeleteAppointment"
                                  Style="background-color: #dc3545; color: white;">
                            @Localizer["Delete"]
                        </SfButton>
                    }
                </div>
                <div>
                    <SfButton OnClick="@AddAppointment"
                              Style="background-color: #28a745; color: white;">
                        @Localizer["Submit"]
                    </SfButton>
                </div>
            </div>
        </MudPaper>
    </DialogContent>
</MudDialog>

<style>
    .e-schedule .e-header-cells {
        display: none;
    }

    .schedule-cell-dimension.e-schedule .e-vertical-view .e-date-header-wrap table col,
    .schedule-cell-dimension.e-schedule .e-vertical-view .e-content-wrap table col {
        width: 200px;
    }

    .schedule-cell-dimension.e-schedule .e-vertical-view .e-time-cells-wrap table td,
    .schedule-cell-dimension.e-schedule .e-vertical-view .e-work-cells {
        height: 55px;
    }
</style>
