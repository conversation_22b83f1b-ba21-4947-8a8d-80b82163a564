﻿@page "/Payment"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "ChartAccessPolicy")]
@inject NavigationManager Navigation
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@layout Admin
@inject TeyaUIViewModels.ViewModel.IChartService ChartService
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject NavigationManager NavigationManager
@using Microsoft.AspNetCore.WebUtilities



<MudItem Class="d-flex flex-column">
    <MudPaper Elevation="1">
        <MudTabs @bind-ActivePanelIndex="activeTabIndex" Elevation="0" Color="Color.Primary" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-3" Class="custom-tabs">
            <MudTabPanel Text="@Localizer["Payments"]" Class="@GetTabClass(0)" Style="margin-left: 20px;"><BatchPayments></BatchPayments> </MudTabPanel>
            <MudTabPanel Text="@Localizer["Batch Payments"]" Class="@GetTabClass(1)"> </MudTabPanel>
        </MudTabs>
    </MudPaper>
</MudItem>


<style>
    /* Dashboard content styling */
    .dashboard-content {
        font-family: 'Atlassian Text', Arial, sans-serif;
        line-height: 1.3; /* Reduced from 1.5 */
    }

    .fixed-dashboard {
        position: fixed;
        top: 65px; /* Change this value to adjust the gap */
        width: 21%;
        max-height: 85vh;
        overflow-y: auto;
        z-index: 100;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
    }

    /* Custom tabs styling */
    .custom-tabs .mud-tabs-toolbar {
        padding-left: 20px;
    }

    /* Tab active state styling */
    .tab-active {
        color: white !important;
        background-color: var(--mud-palette-primary) !important;
    }

    /* Ensure all tabs have consistent styling when active */
    .mud-tab.mud-tab-active {
        color: white !important;
        background-color: var(--mud-palette-primary) !important;
    }

        /* Override any conflicting styles for CDSS tab */
        .mud-tab.mud-tab-active .mud-tab-content {
            color: white !important;
        }

    /* Section headers */
    .section-header {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0px; /* Reduced from 2px */
        margin-top: 0px; /* Reduced from 2px */
        letter-spacing: 0.2px;
    }

    /* Replace dividers with thin black lines */
    .section-divider {
        height: 1px;
        background-color: #000000;
        border-bottom: 1px solid #000000;
        width: 100%;
        margin: 10px 0;
    }

    /* Default avatar styling - similar to WhatsApp */
    .default-avatar {
        width: 80px;
        height: 80px;
        background-color: #DFE5E7;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .default-person-icon {
        width: 48px;
        height: 48px;
        fill: #6B767B;
    }

</style>
