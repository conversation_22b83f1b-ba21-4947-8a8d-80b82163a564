﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Blazored.SessionStorage;
using Microsoft.Azure.Amqp.Framing;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Unity;
using static MudBlazor.Colors;
using System.Text.RegularExpressions;
using System.Text;

namespace TeyaWebApp.Components.Pages
{
    public partial class CurrentMedication : Microsoft.AspNetCore.Components.ComponentBase
    {

        [Inject] private SharedNotesService SharedNotesService { get; set; }
        private List<ChiefComplaintDTO> chiefComplaints = new();
        [Inject] public IMemberService MemberService { get; set; }
        [Inject] ISessionStorageService SessionStorage { get; set; }
        [Inject] public IRxNormService RxNormService { get; set; }
        [Inject] public IFDBService FDBService { get; set; }
        [Inject] public ICurrentMedicationService CurrentMedicationService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private IAlertService AlertService { get; set; }
        [Inject] private IDXAlertService DXAlertService { get; set; }
        [Inject] private ILogger<CurrentMedication> _logger { get; set; }
        [Inject] private UserContext UserContext { get; set; }

        // ===== ADD PRIVATE FIELDS =====
        private Patient _PatientData = new Patient();
        private static readonly char[] SplitChars = { ' ', ',', '-', '(', ')', '/' };
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        protected List<string> BrandNames { get; set; } = new List<string>();
        public List<string> BrandSBD { get; set; } = new List<string>();
        public string drugName { get; set; }
        public string finalSBD { get; set; }
        private MudDialog _currentmedicdialog;
        public string _value;
        private string _Quantity;
        private string _Frequency;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        new ToolbarItemModel() { Name = "close" },
        };

        public SfGrid<ActiveMedication> MedicinesGrid { get; set; }
        [Inject]
        private IMemberService _MemberService { get; set; }

        private Guid? OrgID { get; set; }
        private string editorContent;
        private List<ActiveMedication> deleteList { get; set; } = new List<ActiveMedication>();
        private List<ActiveMedication> AddList = new();
        private List<ActiveMedication> medications { get; set; }
        [Inject]
        private IChiefComplaintService ChiefComplaintService { get; set; }

        [Inject] private IDialogService DialogService { get; set; }
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        private string complaintDescription = string.Empty;
        private List<string> chiefComplaintDescriptions = new List<string>();
        private List<FDBRouteLookUp> FDBRouteLookUps { get; set; }
        private List<FDBTakeLookUp> FDBTakeLookUps { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        public string? ManualContent { get; set; }
        public Guid PatientId { get; set; }
        public Guid Id { get; set; }

        private Dictionary<string, string> routeNameLookup;
        private Dictionary<string, string> takeNameLookup;

        /// <summary>
        /// get list of all Brand Names from RxNorm and Patient Prescription Medication from database
        /// </summary>

        [Parameter]
        public string SpeechData { get; set; }

        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            ManualContent = Data;
            OrgID = OrgId;
            PatientId = PatientID;
            Subscription = UserContext.ActiveUserSubscription;
            medications = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            // Generate editor content immediately with available data
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);

            // Start Phase 2 in background without awaiting
            _ = LoadMedicationDataAsync();
        }

        private async Task LoadMedicationDataAsync()
        {
            // Phase 2: Load additional medication data
            var patient = await MemberService.GetMemberByIdAsync(PatientId, OrgID ?? Guid.Empty, false);
            _PatientData.DOB = patient.DateOfBirth;
            _PatientData.Sex = patient.SexualOrientation;
            _PatientData.Name = patient.FirstName;

            LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription))
                    .GroupBy(c => c.Description)
                    .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
                    .ToList();

            SharedNotesService.OnChange += UpdateComplaints;
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            FDBRouteLookUps = await FDBService.GetFDBRouteLookUp();
            FDBTakeLookUps = await FDBService.GetFDBTakeLookUp();
            routeNameLookup = FDBRouteLookUps.ToDictionary(x => x.MED_ROUTE_ID, x => x.Route_Name);
            takeNameLookup = FDBTakeLookUps.ToDictionary(x => x.MED_DOSAGE_FORM_ID, x => x.Take_Name);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not ActiveMedication medication) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", GetCurrentComplaintOptions());
            builder.AddAttribute(2, "Value", medication.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    medication.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        medication.CheifComplaintId = selectedComplaint.Id;
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };

        private List<string> GetCurrentComplaintOptions()
        {
            // Use the already-loaded LocalData (from async initialization)
            return LocalData?
                .Where(c => !string.IsNullOrEmpty(c.Description))
                .Select(c => c.Description)
                .Distinct()
                .ToList() ?? new List<string>();
        }

        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();
        }


        /// <summary>
        /// Update value in Drug Name List
        /// </summary>

        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;

            if (!string.IsNullOrEmpty(value))
            {
                var BrandSBDList = await RxNormService.GetRxNormSBDCMedications(value);
                BrandSBD = BrandSBDList.Select(m => m.STR).Distinct().ToList();
                finalSBD = null;
                StateHasChanged();

            }
            else
            {
                BrandSBD.Clear();
                finalSBD = null;
                StateHasChanged();
            }
        }
        private CancellationTokenSource _brandSearchCancellationTokenSource = new();

        /// <summary>
        /// Search function for auto complete bar 
        /// </summary>

        protected async Task<IEnumerable<string>> SearchBrandNames(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<string>();

            // Cancel previous search if still running
            _brandSearchCancellationTokenSource?.Cancel();
            _brandSearchCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Add debounce delay (300ms)
                await Task.Delay(300, _brandSearchCancellationTokenSource.Token);

                // Call service with the current search term
                var medications = await RxNormService.GetAllRxNormMedicationsBySearchTerm(searchTerm);

                // Extract brand names and return
                return medications.Select(m => m.STR)
                                 .Distinct()
                                 .Where(name => !string.IsNullOrWhiteSpace(name));
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to search brand names");
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// Add medication to database & Update to grid,RichTextEditor
        /// </summary>
        private async void AddNewMedication()
        {
            if (selectedDatabase == @Localizer["RxNorm"])
            {
                if (string.IsNullOrEmpty(drugName) || string.IsNullOrEmpty(finalSBD))
                {
                    Snackbar.Add(@Localizer["Please fill in both Brand Name and Drug Details fields"], Severity.Warning);
                    return;
                }
                var newMedication = new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = PatientId,
                    PCPId = Guid.Parse(User.id),
                    OrganizationId = OrgID ?? Guid.Empty,
                    CreatedBy = Guid.Parse(User.id),
                    UpdatedBy = Guid.Parse(User.id),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    BrandName = drugName,
                    DrugDetails = finalSBD,
                    Quantity = _Quantity ?? "",
                    Frequency = _Frequency ?? "",
                    isActive = true,
                    StartDate = null,
                    EndDate = null,
                };

                AddList.Add(newMedication);

                medications.Add(newMedication);

                await MedicinesGrid.Refresh();
                ResetInputFields();
            }
            else if (selectedDatabase == @Localizer["FDB"])
            {
                if (selectedMedication == null ||
                    selectedRoutedMedication == null ||
                    selectedRoutedDosageFormMedication == null ||
                    selectedFinalMedication == null)
                {
                    Snackbar.Add(@Localizer["Please fill all fields"], Severity.Warning);
                    return;
                }
                string selectedstrength;
                if (selectedFinalMedication.MED_STRENGTH != null && selectedFinalMedication.MED_STRENGTH_UOM != null)
                {
                    selectedstrength = selectedFinalMedication.MED_STRENGTH + " " + selectedFinalMedication.MED_STRENGTH_UOM;
                }
                else
                {
                    selectedstrength = selectedFinalMedication.MED_MEDID_DESC;
                }
                var newMedication = new ActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = PatientId,
                    PCPId = Guid.Parse(User.id),
                    OrganizationId = OrgID ?? Guid.Empty,
                    CreatedBy = Guid.Parse(User.id),
                    UpdatedBy = Guid.Parse(User.id),
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    BrandName = selectedMedication.MED_NAME,
                    DrugDetails = selectedFinalMedication.MED_MEDID_DESC,
                    Route = GetRouteName(selectedRoutedMedication.MED_ROUTE_ID),
                    Take = GetTakeName(selectedRoutedDosageFormMedication.MED_DOSAGE_FORM_ID),
                    Strength = selectedstrength,
                    Quantity = _Quantity ?? "",
                    Frequency = _Frequency ?? "",
                    isActive = true,
                    StartDate = null,
                    EndDate = null,
                };

                AddList.Add(newMedication);

                medications.Add(newMedication);

                await MedicinesGrid.Refresh();
                ResetInputFields();
            }

        }

        /// <summary>
        /// Reset Fields for closing
        /// </summary>
        private async void ResetInputFields()
        {
            drugName = string.Empty;
            finalSBD = null;
            BrandSBD.Clear();
            _Quantity = null;
            _Frequency = null;
            selectedMedication = null;
            selectedRoutedMedication = null;
            RoutedMedications.Clear();
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// To store deleted rows locally in SFgrid 
        /// </summary>

        public void ActionCompletedHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedMedication = args.Data as ActiveMedication;
                var existingItem = AddList.FirstOrDefault(v => v.MedicineId == deletedMedication.MedicineId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.isActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }


        public async Task ActionBeginHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.StartDate.HasValue && args.Data.EndDate.HasValue &&
                    args.Data.StartDate > args.Data.EndDate)
                {
                    Snackbar.Add(@Localizer["Validation.StartDateAfterEndDate"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;

            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }

            }

        }
        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Changes to be made when clicking on save in dialog
        /// </summary>

        private async Task SaveChanges()
        {
            bool? saveResult = await DialogService.ShowMessageBox(
                Localizer["Confirm Save"],
                Localizer["Are you sure you want to save these changes?"],
                yesText: Localizer["Yes"],
                noText: Localizer["No"]);
            if (saveResult != true)
            {
                return; // User canceled
            }

            // Create a copy of AddList for alert checking
            var alertsCheckList = new List<ActiveMedication>(AddList);

            try
            {
                // Validation checks

                //if (AddList.Any(medication => string.IsNullOrWhiteSpace(medication.CheifComplaint)))
                //{
                //    Snackbar.Add("Validation.MedicationRequiresChiefComplaint", Severity.Warning);
                //    return;
                //}

                // Check for alerts and show them in snackbar only, but don't save to database yet
                // bool hasUnsafeMedications = await CheckMedicationsForAlerts(alertsCheckList, showInSnackbarOnly: true);
                // if (hasUnsafeMedications)
                // {
                //     bool? confirmSave = await DialogService.ShowMessageBox(
                //         Localizer["Safety Alert"],
                //         Localizer["The Medication may not be appropriate with the selected chief complaints. Do you want to save anyway?"],
                //         yesText: Localizer["Save Anyway"],
                //         noText: Localizer["Cancel"]);
                //     if (confirmSave != true)
                //     {
                //         return; // Don't save if user cancels
                //     }
                // }
                // Save the data
                if (AddList.Count != 0)
                {
                    await CurrentMedicationService.AddMedicationAsync(AddList, OrgID, Subscription);
                    AddList.Clear();
                }

                await CurrentMedicationService.UpdateMedicationListAsync(deleteList, OrgID, Subscription);
                await CurrentMedicationService.UpdateMedicationListAsync(medications, OrgID, Subscription);
                deleteList.Clear();

                editorContent = GenerateRichTextContent(ManualContent);
                await HandleDynamicComponentUpdate();
                await InvokeAsync(StateHasChanged);
                CloseAddTaskDialog();

                // Now save alerts to database since user confirmed to save
                // if (hasUnsafeMedications)
                // {
                //     await CheckMedicationsForAlerts(alertsCheckList, showInSnackbarOnly: false);
                // }
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving medications");
                Snackbar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        /// <summary>
        /// Undo Changes When click on cancel
        /// </summary>

        private async Task CancelChanges()
        {
            deleteList.Clear();
            AddList.Clear();
            medications = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Open Dialog
        /// </summary>
        private async Task OpenAddTaskDialog()
        {
            _currentmedicdialog.ShowAsync();
            
            //Update the chief complaint 
            LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription))
                   .GroupBy(c => c.Description)
                   .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
                   .ToList();
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            ResetInputFields();
            _currentmedicdialog.CloseAsync();
        }

        private string GenerateRichTextContent(string manualData)
        {
            string medicationContent = medications != null && medications.Any()
                ? string.Join("", medications.OrderByDescending(m => m.StartDate)
                    .Select(m => $"<ul><li><b>{(m.StartDate.HasValue ? m.StartDate.Value.ToString("yyyy-MM-dd") : "N/A")}</b>: " +
                        $"{m.BrandName}, {m.DrugDetails}, Quantity: {m.Quantity}, " +
                        $"End: {(m.EndDate.HasValue ? m.EndDate.Value.ToString("yyyy-MM-dd") : "N/A")}</li></ul>"))
                : string.Empty;

            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {medicationContent}
            </div>
            </div>";
        }

        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }


        public enum Source { FDB, RxNorm }
        private string selectedDatabase = Source.RxNorm.ToString();

        private List<RxNormConcept> RxMedication { get; set; } = new();
        private RxNormConcept selectedRxMedication;

        private List<RxNormConcept> RxSBDC { get; set; } = new();
        private RxNormConcept selectedRxSBDC;

        private List<FDBMedicationName> FDBmedications { get; set; } = new();
        private FDBMedicationName selectedMedication;

        private List<FDBRoutedMedication> RoutedMedications { get; set; } = new();
        private FDBRoutedMedication selectedRoutedMedication;

        private List<FDBRoutedDosageFormMedication> RoutedDosageFormMedications { get; set; } = new();
        private FDBRoutedDosageFormMedication selectedRoutedDosageFormMedication;

        private List<FDBMedication> FinalMedications { get; set; } = new();
        private FDBMedication selectedFinalMedication;
        private CancellationTokenSource _searchCancellationTokenSource;

        //Search for MedicationNames
        private async Task<IEnumerable<FDBMedicationName>> SearchMedications(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<FDBMedicationName>();

            // Cancel previous search if still running
            _searchCancellationTokenSource?.Cancel();
            _searchCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Add debounce delay (300ms)
                await Task.Delay(300, _searchCancellationTokenSource.Token);

                // Call service with the current search term
                return await FDBService.GetFDBMedicationBySearchTerm(searchTerm);
            }
            catch (TaskCanceledException)
            {
                // Search was canceled (new input arrived)
                return Enumerable.Empty<FDBMedicationName>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to search medications");
                return Enumerable.Empty<FDBMedicationName>();
            }
        }



        //When Medication name is selected
        private async Task OnMedicationSelected(FDBMedicationName med)
        {
            selectedMedication = med;
            selectedRoutedMedication = null;
            RoutedMedications.Clear();
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.MED_NAME_ID))
            {
                RoutedMedications = await FDBService.GetFDBRoutedMedications(med.MED_NAME_ID);
            }
        }

        //When Route is selected
        private async Task OnRoutedMedicationSelected(FDBRoutedMedication med)
        {
            selectedRoutedMedication = med;
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.ROUTED_MED_ID))
            {
                RoutedDosageFormMedications = await FDBService.GetFDBRoutedDosageFormMedications(med.ROUTED_MED_ID);
            }
        }

        //When Dosage Form is selected
        private async Task OnRoutedDosageFormMedicationSelected(FDBRoutedDosageFormMedication med)
        {
            selectedRoutedDosageFormMedication = med;
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.ROUTED_DOSAGE_FORM_MED_ID))
            {
                FinalMedications = await FDBService.GetFDBFinalMedications(med.ROUTED_DOSAGE_FORM_MED_ID);
            }
        }

        private async Task OnFinalMedicationSelected(FDBMedication med)
        {
            selectedFinalMedication = med;
        }


        private string GetRouteName(string routeIdStr)
        {
            if (string.IsNullOrEmpty(routeIdStr))
                return string.Empty;

            if (routeNameLookup.TryGetValue(routeIdStr, out string routeName))
                return routeName;

            return $"Route {routeIdStr}";
        }

        private string GetTakeName(string takeIdStr)
        {
            if (string.IsNullOrEmpty(takeIdStr))
                return string.Empty;

            if (takeNameLookup.TryGetValue(takeIdStr, out string takeName))
                return takeName;

            return $"Route {takeIdStr}";
        }

        private async Task<bool> CheckMedicationsForAlerts(List<ActiveMedication> medicationsToCheck, bool showInSnackbarOnly = false)
        {
            if (medicationsToCheck == null || !medicationsToCheck.Any())
                return false;

            int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string patientGender = _PatientData.Sex ?? "Unknown";
            var alertsToAdd = new List<Alert>();

            var customMedicationAlerts = await GetCustomMedicationAlertsForMedications(medicationsToCheck);

            if (customMedicationAlerts.Count > 0)
            {
                foreach (var customAlert in customMedicationAlerts)
                {
                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = PatientId,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = OrgID ?? Guid.Empty,
                        Severity = "Not Configured",
                        AlertType = "Configured Medication Alert",
                        Description = customAlert.Description ?? $"Custom medication alert for {customAlert.Name}",
                        Solution = $"Follow the guidelines for {customAlert.Name}. See reference: {customAlert.WebReference}",
                        AdditionalInfo = $"Order Set: {customAlert.OrderSet}, Age Range: {customAlert.AgeLowerBound}-{customAlert.AgeUpperBound}, Gender: {customAlert.Gender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    Snackbar.Add($"[CONFIGURED ALERT] {customAlert.Name} - {customAlert.Description}",
                                Severity.Info,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.MedicalServices;
                                });
                }
            }

            foreach (var medication in medicationsToCheck)
            {
                if (string.IsNullOrEmpty(medication.CheifComplaint) || string.IsNullOrEmpty(medication.BrandName))
                    continue;

                bool isAppropriate = await CheckMedicationMatchesComplaint(medication.BrandName, medication.DrugDetails, medication.CheifComplaint);

                if (!isAppropriate)
                {
                    string severityResponse = await GetMedicationSeverity(medication.BrandName, medication.DrugDetails, medication.CheifComplaint);
                    string severityLevel = ExtractSeverityLevel(severityResponse);

                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = PatientId,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = OrgID ?? Guid.Empty,
                        Severity = severityLevel,
                        AlertType = "AI Suggested Medication Alert",
                        Description = $"The medication '{medication.BrandName}' may not be appropriate for chief complaint '{medication.CheifComplaint}'.",
                        Solution = $"Consider reviewing the medication prescription or consulting with a specialist. {severityResponse}",
                        AdditionalInfo = $"Medication: {medication.BrandName}, Drug Details: {medication.DrugDetails}, Chief Complaint: {medication.CheifComplaint}, Patient Age: {patientAge}, Gender: {patientGender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    var snackbarSeverity = severityLevel switch
                    {
                        "High" => Severity.Error,
                        "Medium" => Severity.Warning,
                        "Low" => Severity.Info,
                        _ => Severity.Warning
                    };

                    Snackbar.Add($"[AI SUGGESTED] ({patientAge}y, {patientGender}): {medication.BrandName} may not be appropriate for {medication.CheifComplaint}. Severity: {severityLevel}",
                                snackbarSeverity,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.SmartToy;
                                });
                }
            }

            // Only save alerts to database if showInSnackbarOnly is false
            if (alertsToAdd.Count > 0 && !showInSnackbarOnly)
            {
                try
                {
                    await AlertService.AddAlertsAsync(alertsToAdd, OrgID, false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error adding medication alerts");
                }
            }

            return alertsToAdd.Count > 0;
        }

        private async Task<List<DiagnosisAlert>> GetCustomMedicationAlertsForMedications(List<ActiveMedication> medicationsToCheck)
        {
            try
            {
                var customMedicationAlerts = await DXAlertService.GetAllByIdAndIsActiveAsync(Id, OrgID, false);
                if (customMedicationAlerts == null || customMedicationAlerts.Count == 0)
                    return new List<DiagnosisAlert>();

                int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
                string patientGender = _PatientData.Sex ?? "Unknown";

                var matchingAlerts = new List<DiagnosisAlert>();

                var medicationDetails = new List<(string MedicationName, string DrugDetails, string ChiefComplaint)>();
                foreach (var medication in medicationsToCheck)
                {
                    if (string.IsNullOrEmpty(medication.CheifComplaint) || string.IsNullOrEmpty(medication.BrandName))
                        continue;

                    medicationDetails.Add((medication.BrandName, medication.DrugDetails ?? "", medication.CheifComplaint));
                }

                if (medicationDetails.Count == 0)
                    return matchingAlerts;

                var medicationNames = medicationDetails.Select(m => m.MedicationName).ToList();

                StringBuilder alertsDescription = new StringBuilder();
                for (int i = 0; i < customMedicationAlerts.Count; i++)
                {
                    var alert = customMedicationAlerts[i];
                    alertsDescription.AppendLine($"Alert {i + 1}:");
                    alertsDescription.AppendLine($"- Name: {alert.Name}");
                    alertsDescription.AppendLine($"- Description: {alert.Description}");
                    alertsDescription.AppendLine($"- Order Set: {alert.OrderSet}");
                    alertsDescription.AppendLine($"- Age Range: {(alert.AgeLowerBound.HasValue ? alert.AgeLowerBound.Value.ToString() : "Any")} to {(alert.AgeUpperBound.HasValue ? alert.AgeUpperBound.Value.ToString() : "Any")}");
                    alertsDescription.AppendLine($"- Gender: {alert.Gender ?? "Any"}");
                    alertsDescription.AppendLine();
                }

                StringBuilder medicationComplaintInfo = new StringBuilder();
                foreach (var detail in medicationDetails)
                {
                    medicationComplaintInfo.AppendLine($"- {detail.MedicationName} ({detail.DrugDetails}) for {detail.ChiefComplaint}");
                }

                StringBuilder alertsSummary = new StringBuilder();
                for (int i = 0; i < customMedicationAlerts.Count; i++)
                {
                    var alert = customMedicationAlerts[i];
                    alertsSummary.AppendLine($"Alert {i + 1}: {alert.Name} - {alert.Description}");
                }

                string prompt = $"Determine which medication alerts apply to these medication-complaint pairs:\n\n" +
                    $"Patient: {patientAge}y, {patientGender}\n" +
                    $"Medication-Complaint Pairs:\n{medicationComplaintInfo}\n\n" +
                    $"Alerts:\n{alertsSummary}\n" +
                    $"Return comma-separated alert numbers that apply (e.g., '1,3,5'). If none apply, return 'None'.";

                string response = await AskGptModel(prompt);

                if (!response.Trim().Equals("None", StringComparison.OrdinalIgnoreCase))
                {
                    var matches = Regex.Matches(response, @"\d+");
                    foreach (Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int alertIndex) &&
                            alertIndex >= 1 &&
                            alertIndex <= customMedicationAlerts.Count)
                        {
                            matchingAlerts.Add(customMedicationAlerts[alertIndex - 1]);
                        }
                    }
                }

                // Fallback keyword matching if GPT doesn't find matches
                if (matchingAlerts.Count == 0)
                {
                    foreach (var alert in customMedicationAlerts)
                    {
                        bool medicationMatch = false;

                        if (!string.IsNullOrEmpty(alert.Name))
                        {
                            foreach (var medicationName in medicationNames)
                            {
                                if (alert.Name.Contains(medicationName, StringComparison.OrdinalIgnoreCase))
                                {
                                    medicationMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!medicationMatch && !string.IsNullOrEmpty(alert.Description))
                        {
                            foreach (var medicationName in medicationNames)
                            {
                                if (alert.Description.Contains(medicationName, StringComparison.OrdinalIgnoreCase))
                                {
                                    medicationMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!medicationMatch && !string.IsNullOrEmpty(alert.OrderSet))
                        {
                            foreach (var medicationName in medicationNames)
                            {
                                if (alert.OrderSet.Contains(medicationName, StringComparison.OrdinalIgnoreCase))
                                {
                                    medicationMatch = true;
                                    break;
                                }
                            }
                        }

                        if (medicationMatch)
                        {
                            bool ageMatch = true;
                            if (alert.AgeLowerBound.HasValue && patientAge < alert.AgeLowerBound.Value)
                                ageMatch = false;
                            if (alert.AgeUpperBound.HasValue && patientAge > alert.AgeUpperBound.Value)
                                ageMatch = false;

                            bool genderMatch = true;
                            if (!string.IsNullOrEmpty(alert.Gender) && alert.Gender != "Both")
                            {
                                if (_PatientData.Sex != alert.Gender)
                                    genderMatch = false;
                            }

                            if (ageMatch && genderMatch)
                            {
                                matchingAlerts.Add(alert);
                            }
                        }
                    }
                }

                return matchingAlerts;
            }
            catch (Exception ex)
            {
                return new List<DiagnosisAlert>();
            }
        }

        private static string ExtractSeverityLevel(string response)
        {
            response = response.ToLower();

            if (response.Contains("critical") || response.Contains("severe") || response.Contains("high"))
                return "High";
            else if (response.Contains("medium") || response.Contains("moderate"))
                return "Medium";
            else if (response.Contains("low") || response.Contains("minor"))
                return "Low";
            else
                return "Medium";
        }

        private async Task<bool> CheckMedicationMatchesComplaint(string medicationName, string drugDetails, string chiefComplaint)
        {
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string prompt = $"Is the medication '{medicationName}' ({drugDetails}) appropriate for treating the chief complaint '{chiefComplaint}' in a {age}-year-old {gender} patient? Answer only with 'yes' or 'no'.";
            string response = await AskGptModel(prompt);

            return response.Trim().ToLower().Contains("yes", StringComparison.OrdinalIgnoreCase);
        }

        private async Task<string> GetMedicationSeverity(string medicationName, string drugDetails, string chiefComplaint)
        {
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";
            string patientName = _PatientData.Name ?? "Unknown";

            string prompt = $"What is the severity level (Low, Medium, High) of prescribing medication '{medicationName}' ({drugDetails}) for a {age}-year-old {gender} patient named {patientName} with chief complaint '{chiefComplaint}'? Consider age and gender-specific factors, potential contraindications, and drug interactions. Explain briefly why.";
            string response = await AskGptModel(prompt);

            return response.Trim();
        }

        private async Task<string> AskGptModel(string prompt)
        {
            int age = _PatientData.DOB.HasValue ?
                      (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string systemMessage = $"You are a medical assistant helping to evaluate the appropriateness of medications for specific chief complaints. " +
                                   $"The patient is {age} years old and {gender}. " +
                                   $"Consider age-appropriate and gender-specific medical considerations, contraindications, drug interactions, and therapeutic appropriateness in your evaluation. " +
                                   $"Provide concise, accurate information that takes into account the patient's demographic factors and potential risks.";

            return await MeasureService.AskGptAsync(systemMessage, prompt);
        }

    }
}