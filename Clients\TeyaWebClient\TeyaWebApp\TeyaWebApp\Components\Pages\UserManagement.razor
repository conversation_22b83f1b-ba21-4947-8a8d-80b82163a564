﻿@page "/usermanagement"
@attribute [Authorize(Policy = "usermanagementAccessPolicy")]
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using System.Text.Json
@using BusinessLayer.Services
@using TeyaWebApp.Authorization
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@using TeyaWebApp.Services
@using TeyaUIViewModels
@using TeyaUIModels.ViewModel
@inject GraphApiService customAuthenticationService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IDialogService DialogService
@inject IRoleService RolesService
@inject IOrganizationService OrganizationService
@layout Admin
<GenericCard Heading="@Localizer["UserManagementHeading"]">
    <!-- Add Member Button -->
    <div class="mb-2 d-flex justify-content-start" style="margin-left: 10px;">
        <MudButton Color="Color.Primary"
                   Variant="Variant.Outlined"
                   StartIcon="@Icons.Material.Filled.PersonAdd"
                   OnClick="OpenDialogAsync"
                   Style="padding: 6px 18px; border: 1px solid var(--mud-palette-primary); font-weight: 500;">
            @Localizer["AddMember"]
        </MudButton>
    </div>
    
    <MudDivider Class="mb-3" />

    <!-- User Management Grid with max-width for size control -->
    <div style="max-width: 800px; margin-left: 10px;">
        <SfGrid @ref="UserGrid"
                DataSource="@allMembers"
                AllowPaging="true"
                AllowSorting="true"
                GridLines="GridLine.Both"
                Toolbar="@ToolBarItems"
                Width="100%">
            <GridEvents TValue="Member"
                        OnToolbarClick="OnToolbarItemClicked"
                        OnActionBegin="OnActionBegin"
                        OnActionComplete="OnActionComplete" />
            <GridEditSettings AllowEditing="true" Mode="EditMode.Dialog" />
            <GridColumns>
                <GridColumn Field="Email"
                            HeaderText="@Localizer["Email"]"
                            TextAlign="TextAlign.Left"
                            HeaderTextAlign="TextAlign.Left"
                            Width="150"
                            IsPrimaryKey="true">
                    <Template>
                        <div >
                            @((context as Member).Email)
                        </div>
                    </Template>
                </GridColumn>

                <GridColumn Field="UserName"
                            HeaderText="@Localizer["User"]"
                            TextAlign="TextAlign.Left"
                            Width="100"
                            AllowEditing="false" />

                <GridColumn Field="Country"
                            HeaderText="@Localizer["Country"]"
                            TextAlign="TextAlign.Left"
                            Width="80"
                            AllowEditing="false" />

                <GridColumn Field="OrganizationName"
                            HeaderText="@Localizer["Organization"]"
                            TextAlign="TextAlign.Left"
                            Width="120"
                            AllowEditing="false" />

                <GridColumn Field="RoleName"
                            HeaderText="@Localizer["Role Name"]"
                            TextAlign="TextAlign.Left"
                            Width="80"
                            AllowEditing="false" />
            </GridColumns>
        </SfGrid>
    </div>
</GenericCard>
