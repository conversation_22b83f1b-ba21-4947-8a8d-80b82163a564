﻿@page "/preventivemedicine"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids

<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="add">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                               Size="Size.Small"
                                               OnClick="OpenPreventiveMedicineDialog" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>

<MudDialog @ref="_preventiveMedicineDialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Preventive Medicine"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close"
                       Size="Size.Small"
                       OnClick="CancelChanges"
                       Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <!-- Input Fields -->
                <MudGrid Spacing="3" Style="align-items: center;">
                    <!-- Category Dropdown -->
                    <MudItem xs="3">
                        <SfAutoComplete TValue="string" TItem="PMCategory" @bind-Value="SelectedCategory"
                                        DataSource="@Categories"
                                        Placeholder="Select Category"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains">
                            <AutoCompleteFieldSettings Value="PMCategoryName" Text="PMCategoryName"></AutoCompleteFieldSettings>
                            <AutoCompleteEvents TValue="string" TItem="PMCategory"
                                                ValueChange="@OnCategoryChange" />
                        </SfAutoComplete>
                    </MudItem>

                    <!-- SubCategory Dropdown -->
                    <MudItem xs="3">
                        <SfAutoComplete TValue="string" TItem="PMSubCategory" @bind-Value="SelectedSubCategory"
                                        DataSource="@FilteredSubCategories"
                                        Placeholder="Select Sub-Category"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        Enabled="@(!string.IsNullOrEmpty(SelectedCategory))">
                            <AutoCompleteFieldSettings Value="PMSubcategoryName" Text="PMSubcategoryName"></AutoCompleteFieldSettings>
                            <AutoCompleteEvents TValue="string" TItem="PMSubCategory"
                                                ValueChange="@OnSubCategoryChange" />
                        </SfAutoComplete>
                    </MudItem>

                    <!-- Symptoms Dropdown -->
                    <MudItem xs="3">
                        <SfAutoComplete TValue="string" TItem="PMSymptoms" @bind-Value="SelectedSymptom"
                                        DataSource="@FilteredSymptoms"
                                        Placeholder="Symptom"
                                        AllowFiltering="true"
                                        FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                                        Enabled="@(!string.IsNullOrEmpty(SelectedSubCategory))">
                            <AutoCompleteFieldSettings Value="PMSymptomName" Text="PMSymptomName"></AutoCompleteFieldSettings>
                        </SfAutoComplete>
                    </MudItem>

                    <!-- Add Button -->
                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center; margin-top: 4px;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewEntry"
                                   Variant="Variant.Filled"
                                   Style="height:32px;width:80px;padding: 2px 16px;font-size: 0.8rem;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <!-- Preventive Medicine Grid -->
                <SfGrid @ref="PreventiveMedicineGrid"
                        TValue="PreventiveMedicines"
                        Style="font-size: 0.85rem; margin-top: 24px;"
                        DataSource="@preventiveMedicineEntries"
                        AllowPaging="true"
                        GridLines="GridLine.Both"
                        PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="PreventiveMedicines"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="PreventiveMedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CreatedDate"
                                    HeaderText="@Localizer["Date"]"
                                    TextAlign="TextAlign.Center"
                                    Width="130"
                                    Format="MM-dd-yyyy">
                            <Template>
                                @{
                                    var entry = (PreventiveMedicines)context;
                                }
                                <div class="truncated-cell" title="@entry.CreatedDate.ToString("MM/dd/yyyy")">
                                    @entry.CreatedDate.ToString("MM/dd/yyyy")
                                </div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Category"
                                    HeaderText="@Localizer["Category"]"
                                    TextAlign="TextAlign.Center"
                                    Width="130">
                            <Template>
                                @{
                                    var entry = (PreventiveMedicines)context;
                                }
                                <div class="truncated-cell" title="@entry.Category">@entry.Category</div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="SubCategory"
                                    HeaderText="@Localizer["SubCategory"]"
                                    TextAlign="TextAlign.Center"
                                    Width="130">
                            <Template>
                                @{
                                    var entry = (PreventiveMedicines)context;
                                }
                                <div class="truncated-cell" title="@entry.SubCategory">@entry.SubCategory</div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Symptoms"
                                    HeaderText="@Localizer["Symptoms"]"
                                    TextAlign="TextAlign.Center"
                                    Width="130">
                            <Template>
                                @{
                                    var entry = (PreventiveMedicines)context;
                                }
                                <div class="truncated-cell" title="@entry.Symptoms">@entry.Symptoms</div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Detection"
                                    HeaderText="@Localizer["Detection"]"
                                    TextAlign="TextAlign.Center"
                                    Width="140">
                            <Template>
                                @{
                                    var entry = (PreventiveMedicines)context;
                                }
                                <div class="truncated-cell" title="@entry.Detection">@entry.Detection</div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Notes"
                                    HeaderText="@Localizer["Notes"]"
                                    TextAlign="TextAlign.Left">
                            <Template>
                                @{
                                    var entry = (PreventiveMedicines)context;
                                }
                                <div class="truncated-cell" title="@entry.Notes">@entry.Notes</div>
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="70">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 100px; height: 35px; font-weight: 600;padding: 2px 16px;font-size: 0.8rem;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 100px; height: 35px; font-weight: 600;padding: 2px 16px;font-size: 0.8rem;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>

<style>
    .description-container {
        margin-bottom: 20px;
    }

    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
            min-height: 50px;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    /* Truncated cell styling for tooltips */
    .truncated-cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        cursor: pointer;
    }

        .truncated-cell:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

    /* Grid cell styling */
    .e-grid .e-gridcontent .e-table .e-row .e-rowcell {
        padding: 4px 8px;
    }

    .e-grid .e-gridheader .e-table .e-headerrow .e-headercell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
