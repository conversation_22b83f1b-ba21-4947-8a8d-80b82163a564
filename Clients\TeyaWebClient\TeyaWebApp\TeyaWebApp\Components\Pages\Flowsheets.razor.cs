﻿
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Identity.Client;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Charts;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Net.NetworkInformation;
using System.Text.RegularExpressions;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Charts;
using Syncfusion.Blazor.Grids.Internal;
using Syncfusion.PdfExport;
using Syncfusion.XlsIO;
using System.Drawing;
using System.Linq.Expressions;
using static TeyaWebApp.Components.Pages.Flowsheets;
using static MudBlazor.Colors;
using Microsoft.AspNetCore.Components.Web;



namespace TeyaWebApp.Components.Pages
{
    public partial class Flowsheets : ComponentBase
    {
        private string _selectedFlowsheet = null;
        private bool _isExporting = false;
        private MudDropContainer<DropItem> _container;

        private List<DropItem> DroppedItems = new();


        List<FlowsheetSection> flowsheetData = new();
        List<FlowsheetSection> serializedFlowsheet = new();
        private List<FamilyMember> familyMembers { get; set; }
        private List<PatientVitals> patientVitals { get; set; }
        private List<DiagnosticImage> DiagnosticImagingList = new();
        private List<ImmunizationData> immunization { get; set; }

        private PatientVitals MostRecentVitals { get; set; }
        private List<BPVitals> formattedVitals { get; set; }
        private List<Allergy> allergies { get; set; }
        private FlowsheetReading currentFlowsheet = new();
        private Flowsheet FlowsheetRecord = new();
        private Flowsheet showFlowsheet = new();
        private List<Flowsheet> FlowsheetList = new();
        //private string selectedFlowsheet;
        private bool showNoResultsMessage; 
        private string selectedView = "Graph";
        public SfGrid<PatientVitals> VitalsGrid { get; set; }
        public SfGrid<ActiveMedication> MedicinesGrid { get; set; }
        private List<MedicalHistoryDTO> medicalHistory { get; set; }
        public SfGrid<LabTests> PlanLabsGrid { get; set; }
        private SfChart TemperatureChart;
        private SfChart BloodPressureChart;
        private SfChart PulseChart;
        private SfChart HeightChart;
        private SfChart WeightChart;
        private SfChart MedicationChart;
        private SfChart ProcedureChart;
        private SfChart ImmunizationChart;
        private SfChart DIChart;
        private SfChart LabChart;
        private int yValue = 1;
        private List<ActiveMedication> activeMedications { get; set; }
        private List<LabTests> planlabs { get; set; } = new List<LabTests>();

        [Inject] private PatientService _PatientService { get; set; }
        [Inject] IJSRuntime JS { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] public ILabTestsService _labTestsService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IFlowsheetService FlowsheetService { get; set; }
        [Inject] private IAssessmentsService assessmentsService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        List<MedicationData> medicationLine;
        List<MedicationData> LabTestLine;
        List<MedicationData> DITestLine;
        List<MedicationData> ProcedureTestLine;
        List<MedicationData> ImmunizationTestLine;
        private bool IsDropDisabled = false;

        private bool Subscription = false; 
        public string fontFamily { get; set; } = "font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif";
        private string FlowsheetName;
        private Guid PatientID { get; set; }
        private List<string> AssessmentDiagnosis = new List<string>();
        private Guid? OrganizationID { get; set; }
        public List<AssessmentsData> Localdata { get; set; } = new List<AssessmentsData>();

        private List<string> chiefComplaints = new List<string>();
        private SfGrid<DiagnosticImage> futureImagingGrid { get; set; }
        private List<Procedures> procedure = new();
        private SfGrid<Procedures> ProcedureGrid;
        private List<ChiefComplaintDTO> chiefComplaintData = new List<ChiefComplaintDTO>();
        [Inject] public IImmunizationService _ImmunizationService { get; set; }
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] private ILogger<Procedures> Logger { get; set; }
        [Inject] public IProcedureService ProcedureService { get; set; }
        private List<DropItem> AvailableItems = new()
    {
        new DropItem { Order = 0,Name = "Heading", Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 1,Name = "Vitals",  Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 2,Name = "Labs", Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 3,Name = "DI", Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 4,Name = "Procedure", Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 5,Name = "Medication", Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 6,Name = "Immunization", Identifier = "Drop Zone 1", IsEditing=false },
        new DropItem { Order = 7,Name = "Comments", Identifier = "Drop Zone 1", IsEditing=false }
    };
        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            OrganizationID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            await LoadProcedureAsync();
            Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientID, OrganizationID, false))
                .GroupBy(a => a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
            AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
            DiagnosticImagingList = await DiagnosticImagingService.GetDiagnosticImagingByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription);
            var existingImmunizations = await _ImmunizationService.GetImmunizationByIdAsyncAndIsActive(PatientID, activeUserOrganizationId, Subscription);
            immunization = existingImmunizations?.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList() ?? new List<ImmunizationData>();
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientID);
            FlowsheetList = await FlowsheetService.GetFlowsheetAsync(PatientID, activeUserOrganizationId, Subscription);
            patientVitals = (await VitalService.GetVitalsByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                 .OrderBy(v => v.CreatedDate)
            .Select(v => {
                v.Temperature = VitalUtils.ExtractNumeric(v.Temperature);
                v.Weight = VitalUtils.ExtractNumeric(v.Weight);
                v.Height = VitalUtils.ExtractNumeric(v.Height);
                v.Pulse = VitalUtils.ExtractNumeric(v.Pulse);
                return v;
            })
                 .ToList();

            MostRecentVitals = patientVitals
                              ?.OrderByDescending(v => v.CreatedDate).FirstOrDefault();

            familyMembers = (await FamilyMemberService.GetFamilyMemberByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                            .OrderByDescending(fm => fm.CreatedDate)
                            .Take(5)
                            .ToList();
            allergies = (await AllergyService.GetAllergyByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                        .OrderByDescending(fm => fm.CreatedOn)
                            .Take(5)
                            .ToList();
            medicalHistory = (await MedicalHistoryService.GetAllByIdAndIsActiveAsync(PatientID, OrganizationID, Subscription))
                              .OrderByDescending(fm => fm.CreatedDate)
                               .Take(5)
                               .ToList();
            activeMedications = (await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrganizationID, Subscription))
                                .OrderByDescending(fm => fm.CreatedDate)
                                .Take(5)
                                .ToList();
            formattedVitals = patientVitals
                            .Where(v => !string.IsNullOrEmpty(v.BP))
                            .Select(v => {
                                var bpParts = v.BP.Split('/');
                                return new BPVitals
                                {
                                    CreatedDate = v.CreatedDate,
                                    Systolic = int.TryParse(bpParts[0], out int sys) ? sys : 0,
                                    Diastolic = int.TryParse(bpParts[1], out int dia) ? dia : 0
                                };
                            })
                            .ToList();

            Refactordata();
            RefactorMedicationdata();
            RefactorImmunizationdata();
            RefactorDIdata();
            RefactorProceduredata();
        }

        private void Refactordata()
        {
            LabTestLine = new List<MedicationData>();

            foreach (var lab in planlabs)
            {
                if (lab.CreatedDate.HasValue)
                {
                    LabTestLine.Add(new MedicationData
                    {
                        date = lab.CreatedDate.Value.ToString("MMM dd"),
                        Y = 1,
                        name = $"Test: {lab.LabTest1}"
                    });

                }
            }

        }

        private void RefactorMedicationdata()
        {
            medicationLine = new List<MedicationData>();

            foreach (var med in activeMedications)
            {
                if (med.StartDate.HasValue)
                {
                    medicationLine.Add(new MedicationData
                    {
                        date = med.StartDate.Value.ToString("MMM dd"),
                        Y = yValue,
                        name = med.DrugDetails
                    });
                }

                if (med.EndDate.HasValue)
                {
                    medicationLine.Add(new MedicationData
                    {
                        date = med.EndDate.Value.ToString("MMM dd"),
                        Y = yValue,
                        name = med.DrugDetails
                    });
                }
                yValue++;
            }
        }

        private void SaveSection(FlowsheetSection section)
        {
            if (!string.IsNullOrWhiteSpace(section.Component?.Name))
            {
                // Component is empty -> assign here
                var item = new DropItem { Name = section.Component?.Name };
                var name = "";
                string temp = "";
                if (item.Name == "Vitals")
                {
                    if (currentFlowsheet.BloodPressure)
                    {
                        temp += "Blood Pressure, ";
                        name = item.Name;
                    }
                    if (currentFlowsheet.Pulse)
                    {
                        temp += "Pulse, ";
                        name = item.Name;
                    }
                    if (currentFlowsheet.Temperature)
                    {
                        temp += "Temperature, ";
                        name = item.Name;
                    }
                    if (currentFlowsheet.Height)
                    {
                        temp += "Height, ";
                        name = item.Name;
                    }
                    if (currentFlowsheet.Weight)
                    {
                        temp += "Weight, ";
                        name = item.Name;
                    }
                }
                else
                {
                    if(item.Name == "Procedure")
                    {
                        if(currentFlowsheet.ProcedureTest)
                        {
                            temp = item.Name;
                            name = item.Name;
                        }
                    }
                    if (item.Name == "Labs")
                    {
                        if (currentFlowsheet.LabTest)
                        {
                            temp = item.Name;
                            name = item.Name;
                        }
                    }
                    if (item.Name == "DI")
                    {
                        if (currentFlowsheet.DiTest)
                        {
                            temp = item.Name;
                            name = item.Name;
                        }
                    }
                    if (item.Name == "Medication")
                    {
                        if (currentFlowsheet.Medication)
                        {
                            temp = item.Name;
                            name = item.Name;
                        }
                    }
                    if (item.Name == "Immunization")
                    {
                        if (currentFlowsheet.Immunization)
                        {
                            temp = item.Name;
                            name = item.Name;
                        }
                    }
                }

                section.Component = new FlowsheetComponent
                {
                    Name =name,
                    Element = temp
                };
                    if (string.IsNullOrWhiteSpace(section.Heading) &&
                string.IsNullOrWhiteSpace(section.Component?.Name) &&
                string.IsNullOrWhiteSpace(section.Component?.Element))
                {
                    flowsheetData.Remove(section);
                }
            }
            ResetField();
            section.IsEditing = false;
            IsDropDisabled = false;
        }

        private void ItemUpdated(MudItemDropInfo<DropItem> dropItem)
        {

            if (dropItem.DropzoneIdentifier == "Drop Zone 2")
            {
                DroppedItems.Add(new DropItem
                {
                    Id = Guid.NewGuid(),
                    Name = dropItem.Item.Name,
                    Identifier = dropItem.DropzoneIdentifier,
                    Order = DroppedItems.Count
                });
            }
            AvailableItems = AvailableItems
                .OrderBy(x => x.Order)
                .Select(item => new DropItem
                {
                    Id = item.Id,
                    Order = item.Order,
                    Name = item.Name,
                    Identifier = item.Identifier
                })
                .ToList();
            IsDropDisabled = true;
            RefreshContainer();
        }
        private void RefreshContainer()
        {
            StateHasChanged();

            _container.Refresh();
        }


        private RenderFragment RenderDroppedComponent(DropItem item) => builder =>
        {
            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "class", "my-2 pa-2 mud-elevation-1 rounded");
            builder.AddAttribute(2, "style", "background-color: #f9f9f9; border: 1px solid #ddd;");

            builder.OpenElement(3, "div");

            switch (item.Name)
            {
                case "Heading":
                    builder.OpenElement(4, "label");
                    builder.AddContent(5, "Heading");
                    builder.CloseElement();

                    builder.OpenComponent<MudTextField<string>>(6);
                    builder.AddAttribute(7, "Placeholder", "Enter heading");
                    builder.AddAttribute(8, "Value", currentFlowsheet.Heading);
                    builder.AddAttribute(9, "ValueChanged", EventCallback.Factory.Create<string>(this, value => currentFlowsheet.Heading = value));
                    builder.SetKey($"heading-{item.Id}");
                    builder.CloseComponent();
                    break;

                case "Vitals":
                    builder.OpenElement(10, "div");
                    builder.AddAttribute(11, "class", "d-flex flex-column gap-2");

                    void AddCheckBox(string label, Func<bool> getValue, Action<bool> setValue, Expression<Func<bool>> valueExpr, string id)
                    {
                        builder.OpenComponent<MudCheckBox<bool>>(0);
                        builder.AddAttribute(1, "Label", label);
                        builder.AddAttribute(2, "Value", getValue());
                        builder.AddAttribute(3, "ValueChanged", EventCallback.Factory.Create<bool>(this, setValue));
                        builder.AddAttribute(4, "ValueExpression", valueExpr);
                        builder.SetKey($"{id}-{item.Id}-{Guid.NewGuid()}");
                        builder.CloseComponent();
                    }

                    AddCheckBox("Blood Pressure", () => currentFlowsheet.BloodPressure, v => currentFlowsheet.BloodPressure = v, () => currentFlowsheet.BloodPressure, "bp");
                    AddCheckBox("Temperature", () => currentFlowsheet.Temperature, v => currentFlowsheet.Temperature = v, () => currentFlowsheet.Temperature, "temp");
                    AddCheckBox("Pulse", () => currentFlowsheet.Pulse, v => currentFlowsheet.Pulse = v, () => currentFlowsheet.Pulse, "pulse");
                    AddCheckBox("Height", () => currentFlowsheet.Height, v => currentFlowsheet.Height = v, () => currentFlowsheet.Height, "height");
                    AddCheckBox("Weight", () => currentFlowsheet.Weight, v => currentFlowsheet.Weight = v, () => currentFlowsheet.Weight, "weight");

                    builder.CloseElement(); // div
                    break;

                case "Labs":
                    builder.OpenComponent<MudCheckBox<bool>>(32);
                    builder.AddAttribute(33, "Label", "Lab Test");
                    builder.AddAttribute(34, "Value", currentFlowsheet.LabTest);
                    builder.AddAttribute(35, "ValueChanged", EventCallback.Factory.Create<bool>(this, v => currentFlowsheet.LabTest = v));
                    builder.AddAttribute(36, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.LabTest));
                    builder.SetKey($"labs-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;

                case "DI":
                    builder.OpenComponent<MudCheckBox<bool>>(36);
                    builder.AddAttribute(37, "Label", "DI Test");
                    builder.AddAttribute(38, "Value", currentFlowsheet.DiTest);
                    builder.AddAttribute(39, "ValueChanged", EventCallback.Factory.Create<bool>(this, v => currentFlowsheet.DiTest = v));
                    builder.AddAttribute(40, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.DiTest));
                    builder.SetKey($"di-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;


                case "Procedure":
                    builder.OpenComponent<MudCheckBox<bool>>(41);
                    builder.AddAttribute(42, "Label", "Procedure Test");
                    builder.AddAttribute(43, "Value", currentFlowsheet.ProcedureTest);
                    builder.AddAttribute(44, "ValueChanged", EventCallback.Factory.Create<bool>(this, v =>
                    {
                        currentFlowsheet.ProcedureTest = v;
                        StateHasChanged(); // Ensure the UI re-renders
                    }));
                    builder.AddAttribute(45, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.ProcedureTest));
                    builder.SetKey($"procedure-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;

                case "Medication":
                    builder.OpenComponent<MudCheckBox<bool>>(41);
                    builder.AddAttribute(42, "Label", "Medication Test");
                    builder.AddAttribute(43, "Value", currentFlowsheet.Medication);
                    builder.AddAttribute(44, "ValueChanged", EventCallback.Factory.Create<bool>(this, v =>
                    {
                        currentFlowsheet.Medication = v;
                        StateHasChanged(); // Ensure the UI re-renders
                    }));
                    builder.AddAttribute(45, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.Medication));
                    builder.SetKey($"medication-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;

                case "Immunization":
                    builder.OpenComponent<MudCheckBox<bool>>(41);
                    builder.AddAttribute(42, "Label", "Immunization Test");
                    builder.AddAttribute(43, "Value", currentFlowsheet.Immunization);
                    builder.AddAttribute(44, "ValueChanged", EventCallback.Factory.Create<bool>(this, v =>
                    {
                        currentFlowsheet.Immunization = v;
                        StateHasChanged(); // Ensure the UI re-renders
                    }));
                    builder.AddAttribute(45, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.Immunization));
                    builder.SetKey($"immunization-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;

                case "Comments":
                    builder.OpenElement(4, "label");
                    builder.AddContent(5, "Enter Free Text");
                    builder.CloseElement();

                    builder.OpenComponent<MudTextField<string>>(6);
                    builder.AddAttribute(7, "Placeholder", "Enter Text");
                    builder.AddAttribute(8, "Value", currentFlowsheet.FreeText);
                    builder.AddAttribute(9, "ValueChanged", EventCallback.Factory.Create<string>(this, value => currentFlowsheet.FreeText = value));
                    builder.SetKey($"comments-{item.Id}");
                    builder.CloseComponent();
                    break;
            }

            builder.CloseElement(); // close inner content

            if (!item.IsEditing)
            {
                builder.OpenElement(21, "div");
                builder.AddAttribute(22, "class", "d-flex justify-end mt-2 gap-2");

                builder.OpenComponent<MudButton>(23);
                builder.AddAttribute(24, "Color", MudBlazor.Color.Primary);
                builder.AddAttribute(25, "Variant", Variant.Filled);
                builder.AddAttribute(26, "ChildContent", (RenderFragment)(btnBuilder => btnBuilder.AddContent(0, "Save")));
                builder.AddAttribute(27, "OnClick", EventCallback.Factory.Create<MouseEventArgs>(this, _ => SaveItem(item, true)));

                builder.CloseComponent();

                builder.OpenComponent<MudButton>(28);
                builder.AddAttribute(29, "Color", MudBlazor.Color.Secondary);
                builder.AddAttribute(30, "Variant", Variant.Text);
                builder.AddAttribute(31, "ChildContent", (RenderFragment)(btnBuilder => btnBuilder.AddContent(0, "Cancel")));
                builder.AddAttribute(32, "OnClick", EventCallback.Factory.Create<MouseEventArgs>(this, _ => CancelItem(item, false)));
                builder.CloseComponent();

                builder.CloseElement();
            }
            builder.CloseElement();
        };
        private async void LockChanges()
        {
            FlowsheetRecord.IsEditable = false;
            FlowsheetRecord.UpdatedBy = Guid.Parse(User.id);
            FlowsheetRecord.UpdatedDate = DateTime.Now;
            FlowsheetRecord.UpdatedDate = DateTime.Now;
            FlowsheetRecord.SerializedData = JsonSerializer.Serialize(flowsheetData);
            await FlowsheetService.UpdateFlowsheetAsync(FlowsheetRecord, activeUserOrganizationId, Subscription);
            FlowsheetList = await FlowsheetService.GetFlowsheetAsync(PatientID, activeUserOrganizationId, Subscription);
            currentFlowsheet.IsEditable = FlowsheetRecord.IsEditable;
            currentFlowsheet.SerializedData = FlowsheetRecord.SerializedData;
            IsDropDisabled = true;
            StateHasChanged();

        }
        public class FlowsheetComponent
        {
            public string Name { get; set; }
            public string Element { get; set; }
        }

        public class FlowsheetSection
        {
            public string Heading { get; set; }
            public bool IsEditing { get; set; }
            public List<string> FreeText { get; set; }
            public FlowsheetComponent Component { get; set; }
        }

        public class DropItem
        {
            public int Order { get; set; }
            public Guid Id { get; set; } = Guid.NewGuid();
            public string Name { get; init; }
            public string Identifier { get; set; }
            public bool IsEditing { get; set; }
        }
        private void SaveItem(DropItem item, bool isSaved)
        {
            IsDropDisabled = false;
            if (item.Name == "Comments")
            {
                var lastSection = flowsheetData.LastOrDefault();

                if (lastSection != null)
                {
                    if (lastSection.FreeText == null)
                        lastSection.FreeText = new List<string>();

                    lastSection.FreeText.Add(currentFlowsheet.FreeText);
                }
                else
                {
                    var newSection = new FlowsheetSection
                    {
                        Heading = "",
                        Component = new FlowsheetComponent
                        {
                            Name = "",
                            Element = ""
                        },
                        FreeText = new List<string> { currentFlowsheet.FreeText },
                        IsEditing = false
                    };
                    flowsheetData.Add(newSection);
                }
            }
            else if (item.Name != "Heading")
            {
                var lastSection = flowsheetData.LastOrDefault();

                if (lastSection != null)
                {
                    if (string.IsNullOrWhiteSpace(lastSection.Component?.Name))
                    {
                        string temp = "";
                        if (item.Name == "Vitals")
                        {
                            if (currentFlowsheet.BloodPressure)
                            {
                                temp += "Blood Pressure, ";
                            }
                            if (currentFlowsheet.Pulse)
                            {
                                temp += "Pulse, ";
                            }
                            if (currentFlowsheet.Temperature)
                            {
                                temp += "Temperature, ";
                            }
                            if (currentFlowsheet.Height)
                            {
                                temp += "Height, ";
                            }
                            if (currentFlowsheet.Weight)
                            {
                                temp += "Weight, ";
                            }
                        }
                        else
                        {
                            if (item.Name == "Procedure")
                            {
                                if (currentFlowsheet.ProcedureTest)
                                {
                                    temp = item.Name;
                                }
                            }
                            if (item.Name == "Labs")
                            {
                                if (currentFlowsheet.LabTest)
                                {
                                    temp = item.Name;
                                }
                            }
                            if (item.Name == "DI")
                            {
                                if (currentFlowsheet.DiTest)
                                {
                                    temp = item.Name;
                                }
                            }
                            if (item.Name == "Medication")
                            {
                                if (currentFlowsheet.Medication)
                                {
                                    temp = item.Name;
                                }
                            }
                            if (item.Name == "Immunization")
                            {
                                if (currentFlowsheet.Immunization)
                                {
                                    temp = item.Name;
                                }
                            }
                        }

                        lastSection.Component = new FlowsheetComponent
                        {
                            Name = item.Name,
                            Element = temp
                        };
                    }
                    else
                    {
                        string temp = "";
                        if (item.Name == "Vitals")
                        {
                            if (currentFlowsheet.BloodPressure)
                            {
                                temp += "Blood Pressure, ";
                            }
                            if (currentFlowsheet.Pulse)
                            {
                                temp += "Pulse, ";
                            }
                            if (currentFlowsheet.Temperature)
                            {
                                temp += "Temperature, ";
                            }
                            if (currentFlowsheet.Height)
                            {
                                temp += "Height, ";
                            }
                            if (currentFlowsheet.Weight)
                            {
                                temp += "Weight, ";
                            }
                        }
                        else
                        {
                            temp = item.Name;
                        }
                        var newSection = new FlowsheetSection
                        {
                            Heading = "",
                            Component = new FlowsheetComponent
                            {
                                Name = item.Name,
                                Element = temp
                            },
                            FreeText = lastSection.FreeText ?? new List<string>(),
                            IsEditing = false
                        };
                        flowsheetData.Add(newSection);
                    }
                }
                else
                {
                    var newSection = new FlowsheetSection
                    {
                        Heading = "",
                        Component = new FlowsheetComponent
                        {
                            Name = item.Name,
                            Element = item.Name
                        },
                        FreeText = lastSection.FreeText ?? new List<string>(),
                        IsEditing = false
                    };
                    flowsheetData.Add(newSection);
                }
            }
            else
            {
                var newSection = new FlowsheetSection
                {
                    Heading = currentFlowsheet.Heading,
                    Component = new FlowsheetComponent
                    {
                        Name = "",
                        Element = ""
                    },
                    FreeText = new List<string>(),
                    IsEditing = false
                };
                flowsheetData.Add(newSection);
            }
            item.IsEditing = false;
            DroppedItems.Remove(item);
            ResetField();
            StateHasChanged();
        }

        private void ResetField()
        {
            currentFlowsheet.Heading = string.Empty;
            currentFlowsheet.BloodPressure = false;
            currentFlowsheet.Temperature = false;
            currentFlowsheet.Pulse = false;
            currentFlowsheet.Height = false;
            currentFlowsheet.Weight = false;
            currentFlowsheet.LabTest = false;
            currentFlowsheet.DiTest = false;
            currentFlowsheet.ProcedureTest = false;
            currentFlowsheet.Medication = false;
            currentFlowsheet.Immunization = false;
            currentFlowsheet.FreeText = string.Empty;
        }
        private void CancelItem(DropItem item, bool isSaved)
        {
            DroppedItems.Remove(item);
            item.IsEditing = false;
            IsDropDisabled = false;
            StateHasChanged();
        }
        private void StartEdit(FlowsheetSection section)
        {
            IsDropDisabled = true;
            currentFlowsheet.Heading = section?.Heading;
            currentFlowsheet.FreeText = string.Join(", ", section?.FreeText ?? new List<string>());
            DroppedItems = new();
            section.IsEditing = true;
        }

        private RenderFragment RenderEditSection(FlowsheetSection section) => builder =>
        {
            var item = new DropItem { Name = section.Component?.Name };

            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "class", "my-2 pa-2 mud-elevation-1 rounded");
            builder.AddAttribute(2, "style", "background-color: #f9f9f9; border: 1px solid #ddd;");

            // 🔒 Outer wrapper for section content
            builder.OpenElement(3, "div");

            if (!string.IsNullOrWhiteSpace(section.Heading))
            {
                builder.OpenElement(4, "label");
                builder.AddContent(5, "Heading");
                builder.CloseElement();

                builder.OpenComponent<MudTextField<string>>(6);
                builder.AddAttribute(7, "Placeholder", "Enter heading");
                builder.AddAttribute(8, "Value", section.Heading); // Binding to section, not currentFlowsheet
                builder.AddAttribute(9, "ValueChanged", EventCallback.Factory.Create<string>(this, value =>
                {
                    section.Heading = value;
                    StateHasChanged();
                }));
                builder.AddAttribute(10, "ValueExpression", (Expression<Func<string>>)(() => section.Heading));
                builder.SetKey($"heading-{Guid.NewGuid()}");
                builder.CloseComponent();
            }

            if (section.FreeText != null && section.FreeText.Any())
            {
                builder.OpenElement(11, "label");
                builder.AddContent(12, "Free Text");
                builder.CloseElement();

                builder.OpenComponent<MudTextField<string>>(13);
                builder.AddAttribute(14, "Placeholder", "Enter comment");
                builder.AddAttribute(15, "Value", string.Join(", ", section.FreeText));
                builder.AddAttribute(16, "ValueChanged", EventCallback.Factory.Create<string>(this, value =>
                {
                    section.FreeText = value
                        .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                        .ToList();
                }));
                builder.SetKey($"comments-{Guid.NewGuid()}");
                builder.CloseComponent();
            }

            switch (item.Name)
            {
                case "Vitals":
                    builder.OpenElement(17, "div");
                    builder.AddAttribute(18, "class", "d-flex flex-column gap-2");

                    var elements = section.Component?.Element?
                        .Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                        ?? Array.Empty<string>();

                    void AddCheckBox(string label, Func<bool> getValue, Action<bool> setValue, Expression<Func<bool>> valueExpr, string id)
                    {
                        builder.OpenComponent<MudCheckBox<bool>>(19);
                        builder.AddAttribute(20, "Label", label);
                        builder.AddAttribute(21, "Value", getValue());
                        builder.AddAttribute(22, "ValueChanged", EventCallback.Factory.Create<bool>(this, setValue));
                        builder.AddAttribute(23, "ValueExpression", valueExpr);
                        builder.SetKey($"{id}-{Guid.NewGuid()}");
                        builder.CloseComponent();
                    }

                    AddCheckBox("Blood Pressure", () => currentFlowsheet.BloodPressure, v => currentFlowsheet.BloodPressure = v, () => currentFlowsheet.BloodPressure, "Blood Pressure");
                    AddCheckBox("Temperature", () => currentFlowsheet.Temperature, v => currentFlowsheet.Temperature = v, () => currentFlowsheet.Temperature, "Temperature");
                    AddCheckBox("Pulse", () => currentFlowsheet.Pulse, v => currentFlowsheet.Pulse = v, () => currentFlowsheet.Pulse, "pulse");
                    AddCheckBox("Height", () => currentFlowsheet.Height, v => currentFlowsheet.Height = v, () => currentFlowsheet.Height, "height");
                    AddCheckBox("Weight", () => currentFlowsheet.Weight, v => currentFlowsheet.Weight = v, () => currentFlowsheet.Weight, "weight");

                    builder.CloseElement(); // ✅ Close inner div for Vitals
                    break;

                case "Immunization":
                    builder.OpenComponent<MudCheckBox<bool>>(24);
                    builder.AddAttribute(25, "Label", "Immunization Test");
                    builder.AddAttribute(26, "Value", currentFlowsheet.Immunization);
                    builder.AddAttribute(27, "ValueChanged", EventCallback.Factory.Create<bool>(this, v =>
                    {
                        currentFlowsheet.Immunization = v;
                        StateHasChanged(); // Ensure the UI re-renders
                    }));
                    builder.AddAttribute(28, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.Immunization));
                    builder.SetKey($"immunization-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;
                case "Labs":
                    builder.OpenComponent<MudCheckBox<bool>>(29);
                    builder.AddAttribute(30, "Label", "Lab Test");
                    builder.AddAttribute(31, "Value", currentFlowsheet.LabTest);
                    builder.AddAttribute(32, "ValueChanged", EventCallback.Factory.Create<bool>(this, v => currentFlowsheet.LabTest = v));
                    builder.AddAttribute(33, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.LabTest));
                    builder.SetKey($"labs-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;

                case "DI":
                    builder.OpenComponent<MudCheckBox<bool>>(34);
                    builder.AddAttribute(35, "Label", "DI Test");
                    builder.AddAttribute(36, "Value", currentFlowsheet.DiTest);
                    builder.AddAttribute(37, "ValueChanged", EventCallback.Factory.Create<bool>(this, v => currentFlowsheet.DiTest = v));
                    builder.AddAttribute(38, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.DiTest));
                    builder.SetKey($"di-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;


                case "Procedure":
                    builder.OpenComponent<MudCheckBox<bool>>(39);
                    builder.AddAttribute(40, "Label", "Procedure Test");
                    builder.AddAttribute(41, "Value", currentFlowsheet.ProcedureTest);
                    builder.AddAttribute(42, "ValueChanged", EventCallback.Factory.Create<bool>(this, v =>
                    {
                        currentFlowsheet.ProcedureTest = v;
                        StateHasChanged(); 
                    }));
                    builder.AddAttribute(45, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.ProcedureTest));
                    builder.SetKey($"procedure-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;

                case "Medication":
                    builder.OpenComponent<MudCheckBox<bool>>(43);
                    builder.AddAttribute(44, "Label", "Medication Test");
                    builder.AddAttribute(45, "Value", currentFlowsheet.Medication);
                    builder.AddAttribute(46, "ValueChanged", EventCallback.Factory.Create<bool>(this, v =>
                    {
                        currentFlowsheet.Medication = v;
                        StateHasChanged();
                    }));
                    builder.AddAttribute(47, "ValueExpression", (Expression<Func<bool>>)(() => currentFlowsheet.Medication));
                    builder.SetKey($"medication-{item.Id}-{Guid.NewGuid()}");
                    builder.CloseComponent();
                    break;
            }

            builder.CloseElement(); 
            builder.CloseElement(); 
        };

        public byte[] ExportChartsToPdf(List<string> base64ChartImages)
        {
            using var pdfDoc = new PdfDocument();

            foreach (var base64 in base64ChartImages)
            {
                var cleanedBase64 = base64.Replace("data:image/png;base64,", "");

                byte[] imageBytes = Convert.FromBase64String(cleanedBase64);

                using var stream = new MemoryStream(imageBytes);
                var image = new PdfBitmap(stream);

                var page = pdfDoc.Pages.Add();
                var pageWidth = page.GetClientSize().Width;
                var imageWidth = image.Width;
                var imageHeight = image.Height;

                float ratio = pageWidth / imageWidth;
                float newHeight = imageHeight * ratio;

                page.Graphics.DrawImage(image, 0, 0, pageWidth, newHeight);
            }

            using var outputStream = new MemoryStream();
            pdfDoc.Save(outputStream);
            return outputStream.ToArray();
        }
        private void RefactorDIdata()
         {
            DITestLine = new List<MedicationData>();

            foreach (var di in DiagnosticImagingList)
            {
                if (di.CreatedDate.HasValue)
                {
                    DITestLine.Add(new MedicationData
                    {
                        date = di.CreatedDate.Value.ToString("MMM dd"),
                        Y = 1,
                        name = di.OrderName,
                    });

                }
            }
        }
        private void RefactorProceduredata()
        {
            ProcedureTestLine = new List<MedicationData>();

            foreach (var procedureData in procedure)
            {
                if (procedureData.OrderDate != null)
                {
                    ProcedureTestLine.Add(new MedicationData
                    {
                        date = procedureData.OrderDate.ToString("MMM dd"),
                        Y = 1,
                        name = procedureData.Description,
                    });

                }
            }

        }

        private void RefactorImmunizationdata()
        {
            ImmunizationTestLine = new List<MedicationData>();

            foreach (var imm in immunization)
            {
                if (imm.CreatedDate != null)
                {
                    ImmunizationTestLine.Add(new MedicationData
                    {
                        date = imm.CreatedDate.Value.ToString("MMM dd"),
                        Y = 1,
                        name = imm.CPTCode,
                    });

                }
            }

        }
        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaints);
            builder.AddAttribute(2, "Value", process.ChiefComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.ChiefComplaint = value;
                    var selectedComplaint = chiefComplaintData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        process.ChiefComplaintId = selectedComplaint.Id;
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests Labs) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", Labs.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    Labs.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        Labs.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(Labs.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");
            builder.CloseComponent();
        };
        private async Task LoadProcedureAsync()
        {
            try
            {
                procedure = await ProcedureService.LoadProcedureAsync(PatientID, OrganizationID, Subscription);

                foreach (var proc in procedure)
                {
                    if (!string.IsNullOrEmpty(proc.ChiefComplaintId?.ToString()) && string.IsNullOrEmpty(proc.ChiefComplaint))
                    {
                        var complaint = chiefComplaintData.FirstOrDefault(c => c.Id == proc.ChiefComplaintId);
                        if (complaint != null)
                        {
                            proc.ChiefComplaint = complaint.Description;
                        }
                    }
                }

               
                if (ProcedureGrid != null)
                    await ProcedureGrid.Refresh();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading Procedure");
            }

        }
        private List<string> OrganizationOptions => new List<string>
        {
                Localizer["Quest Inc"].Value,
                Localizer["Lab Corp"].Value
        };

        private void OnEditViewClicked()
        {
            if (string.IsNullOrWhiteSpace(_selectedFlowsheet))
            {
                SnackBar.Add("Please select a flowsheet first.", Severity.Warning);
                return;
            }
            showFlowsheet = FlowsheetList.FirstOrDefault(f => f.FlowsheetName == _selectedFlowsheet);
            flowsheetData = JsonSerializer.Deserialize<List<FlowsheetSection>>(showFlowsheet?.SerializedData);
            selectedView = "Edit";

        }

        private Task OnFlowsheetSelected(SelectEventArgs<string> args)
        {
            string value = args.ItemData;

            if (string.IsNullOrWhiteSpace(value))
            {
                _selectedFlowsheet = null;
                showFlowsheet = null;
                StateHasChanged();
                return Task.CompletedTask;
            }

            _selectedFlowsheet = value;
            showFlowsheet = FlowsheetList.FirstOrDefault(f => f.FlowsheetName == _selectedFlowsheet);
            FlowsheetRecord = showFlowsheet;
            IsDropDisabled = showFlowsheet.IsEditable==true ? false : true;
            currentFlowsheet.SerializedData = showFlowsheet.SerializedData;
            currentFlowsheet.IsEditable = showFlowsheet.IsEditable;
            currentFlowsheet.FlowsheetName = showFlowsheet.FlowsheetName;
            flowsheetData = JsonSerializer.Deserialize<List<FlowsheetSection>>(showFlowsheet?.SerializedData);


            StateHasChanged();
            return Task.CompletedTask;
        }


        private async Task ExportAllToExcel()
        {
            using ExcelEngine excelEngine = new();
            IApplication application = excelEngine.Excel;
            application.DefaultVersion = ExcelVersion.Xlsx;

            IWorkbook workbook = application.Workbooks.Create(1);
            IWorksheet sheet = workbook.Worksheets[0];
            sheet.Name = "Patient Report";

            _isExporting = true;
            StateHasChanged();

            int row = 1;

            void AddSectionHeader(string title, string[] headers,Syncfusion.Drawing.Color bgColor)
            {
                // Merge and style the section title
                sheet.Range[row, 1, row, headers.Length].Merge();
                sheet.Range[row, 1].Text = title;
                sheet.Range[row, 1].CellStyle.Font.Bold = true;
                sheet.Range[row, 1].CellStyle.Font.Size = 14;
                sheet.Range[row, 1].CellStyle.Color = Syncfusion.Drawing.Color.LightGray;
                row++;

                // Add column headers
                for (int i = 0; i < headers.Length; i++)
                {
                    var cell = sheet.Range[row, i + 1];
                    cell.Text = headers[i];
                    cell.CellStyle.Font.Bold = true;
                    cell.CellStyle.Color = bgColor;
                }
                row++;
            }

            // --- Vitals ---
            if (flowsheetData.Any(s => s.Component?.Name?.Equals("Vitals", StringComparison.OrdinalIgnoreCase) == true))
            {
                AddSectionHeader("Vitals", new[] { "Date", "Temperature", "BP", "Pulse", "Weight", "Height" }, Syncfusion.Drawing.Color.LightBlue);
                foreach (var v in patientVitals)
                {
                    sheet.Range[row, 1].Text = v.CreatedDate.ToString("MM/dd/yyyy");
                    double.TryParse(v.Temperature, out double temp);
                    sheet.Range[row, 2].Number = temp;
                    sheet.Range[row, 3].Text = v.BP ?? "";
                    double.TryParse(v.Pulse, out double pulse);
                    sheet.Range[row, 4].Number = pulse;
                    double.TryParse(v.Weight, out double weight);
                    sheet.Range[row, 5].Number = weight;
                    double.TryParse(v.Height, out double height);
                    sheet.Range[row, 6].Number = height;
                    row++;
                }
                int vitalsStartRow = row - patientVitals.Count - 2;
                int chartOffset = 9;

                var chartData = new Dictionary<string, (SfChart? chart, string title)>
                {
                    { "TemperatureChart", (TemperatureChart, "Temperature Trend") },
                    { "BloodPressureChart", (BloodPressureChart, "Blood Pressure Trend") },
                    { "PulseChart", (PulseChart, "Pulse Trend") },
                    { "HeightChart", (HeightChart, "Height Chart") },
                    { "WeightChart", (WeightChart, "Weight Chart") }
                };

                var vitalsChartData = chartData.Where(entry =>
                                                    entry.Key switch
                                                    {
                                                        "TemperatureChart" => flowsheetData.Any(s =>
                                                            s.Component?.Name?.Equals("Vitals", StringComparison.OrdinalIgnoreCase) == true &&
                                                            s.Component.Element?.IndexOf("Temperature", StringComparison.OrdinalIgnoreCase) >= 0),

                                                        "BloodPressureChart" => flowsheetData.Any(s =>
                                                            s.Component?.Name?.Equals("Vitals", StringComparison.OrdinalIgnoreCase) == true &&
                                                            s.Component.Element?.IndexOf("Blood Pressure", StringComparison.OrdinalIgnoreCase) >= 0),

                                                        "PulseChart" => flowsheetData.Any(s =>
                                                            s.Component?.Name?.Equals("Vitals", StringComparison.OrdinalIgnoreCase) == true &&
                                                            s.Component.Element?.IndexOf("Pulse", StringComparison.OrdinalIgnoreCase) >= 0),

                                                        "HeightChart" => flowsheetData.Any(s =>
                                                            s.Component?.Name?.Equals("Vitals", StringComparison.OrdinalIgnoreCase) == true &&
                                                            s.Component.Element?.IndexOf("Height", StringComparison.OrdinalIgnoreCase) >= 0),

                                                        "WeightChart" => flowsheetData.Any(s =>
                                                            s.Component?.Name?.Equals("Vitals", StringComparison.OrdinalIgnoreCase) == true &&
                                                            s.Component.Element?.IndexOf("Weight", StringComparison.OrdinalIgnoreCase) >= 0),

                                                        _ => false
                                                    }).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                foreach (var (chartId, (chartRef, title)) in vitalsChartData)
                {
                    await InsertChartRightOfGrid(chartId, title, vitalsStartRow , row, chartOffset);
                    chartOffset += 6;
                }

                row = Math.Max(row, vitalsStartRow + 12);

                row++;
            }

            if (flowsheetData.Any(s => s.Component?.Name?.Equals("Immunization", StringComparison.OrdinalIgnoreCase) == true))
            {
                AddSectionHeader("Immunizations", new[] { "Date", "Vaccine", "CPT Code", "CVX Code", "Comments" }, Syncfusion.Drawing.Color.LightGreen);

                foreach (var record in immunization)
                {
                    sheet.Range[row, 1].Text = record.GivenDate?.ToString("MM/dd/yyyy");
                    sheet.Range[row, 2].Text = record.Immunizations?.Replace(",", " ");
                    sheet.Range[row, 3].Text = record.CPTCode?.Replace(",", " ");
                    sheet.Range[row, 4].Text = record.CVXCode?.Replace(",", " ");
                    sheet.Range[row, 5].Text = record.Comments?.Replace(",", " ");
                    row++;
                }
                int immunStartRow = row - immunization.Count - 2;
                await InsertChartRightOfGrid("ImmunizationChart", "Immunization Trends", immunStartRow, row, 8);
                row = Math.Max(row, immunStartRow + 12);

                row++;
            }

            // --- Lab Tests ---
            if (flowsheetData.Any(s => s.Component?.Name?.Equals("Labs", StringComparison.OrdinalIgnoreCase) == true))
            {
                AddSectionHeader("Lab Tests", new[] { "Primary Test", "Organization", "Related Assessment", "Created Date", "Updated Date" }, Syncfusion.Drawing.Color.LightCoral);
                foreach (var lab in planlabs)
                {
                    sheet.Range[row, 1].Text = lab.LabTest1?.Replace(",", " ");
                    sheet.Range[row, 2].Text = lab.TestOrganization?.Replace(",", " ");
                    sheet.Range[row, 3].Text = lab.AssessmentData?.Replace(",", " ");
                    sheet.Range[row, 4].Text = lab.CreatedDate?.ToString("dd-MM-yyyy");
                    sheet.Range[row, 5].Text = lab.UpdatedDate?.ToString("dd-MM-yyyy");
                    row++;
                }
                int labStartRow = row - planlabs.Count - 2;
                await InsertChartRightOfGrid("LabChart", "Lab Results Overview", labStartRow, row, 8);
                row = Math.Max(row, labStartRow + 12);

                row++;
            }

            // --- Diagnostic Imaging ---
            if (flowsheetData.Any(s => s.Component?.Name?.Equals("DI", StringComparison.OrdinalIgnoreCase) == true))
            {
                AddSectionHeader("Diagnostic Imaging", new[] { "DI Company", "Type", "Lookup", "Order Name", "Starts With", "CC Results To", "Created Date" }, Syncfusion.Drawing.Color.LightSalmon);
                foreach (var di in DiagnosticImagingList)
                {
                    sheet.Range[row, 1].Text = di.DiCompany?.Replace(",", " ");
                    sheet.Range[row, 2].Text = di.Type?.Replace(",", " ");
                    sheet.Range[row, 3].Text = di.Lookup?.Replace(",", " ");
                    sheet.Range[row, 4].Text = di.OrderName?.Replace(",", " ");
                    sheet.Range[row, 5].Text = di.StartsWith?.Replace(",", " ");
                    sheet.Range[row, 6].Text = di.ccResults?.Replace(",", " ");
                    sheet.Range[row, 7].Text = di.CreatedDate?.ToString("MM/dd/yyyy");
                    row++;
                }
                int diStartRow = row - DiagnosticImagingList.Count - 2;
                await InsertChartRightOfGrid("DIChart", "DI Trends", diStartRow, row, 10);
                row = Math.Max(row, diStartRow + 12);
            }

            // --- Procedures ---
            if (flowsheetData.Any(s => s.Component?.Name?.Equals("Procedure", StringComparison.OrdinalIgnoreCase) == true))
            {
                AddSectionHeader("Procedures", new[] { "CPT", "Description", "Notes", "Related Assessment", "Chief Complaint", "Ordered By", "Order Date" }, Syncfusion.Drawing.Color.LightGreen);
                foreach (var p in procedure)
                {
                    sheet.Range[row, 1].Text = p.CPTCode;
                    sheet.Range[row, 2].Text = p.Description?.Replace(",", " ");
                    sheet.Range[row, 3].Text = p.Notes?.Replace(",", " ");
                    sheet.Range[row, 4].Text = p.AssessmentData?.Replace(",", " ");
                    sheet.Range[row, 5].Text = p.ChiefComplaint?.Replace(",", " ");
                    sheet.Range[row, 6].Text = p.OrderedBy?.Replace(",", " ");
                    sheet.Range[row, 7].Text = p.OrderDate.ToString("MM/dd/yyyy");
                    row++;
                }
                int procedureStartRow = row - procedure.Count - 2;
                await InsertChartRightOfGrid("ProcedureChart", "Procedure Overview", procedureStartRow, row, 10);
                row = Math.Max(row, procedureStartRow + 12);  // adjust for chart height

                row++;
            }
            // --- Medications ---
            if (flowsheetData.Any(s => s.Component?.Name?.Equals("Medication", StringComparison.OrdinalIgnoreCase) == true))
            {
                AddSectionHeader("Medications", new[] { "Brand Name", "Drug Details", "Quantity", "Frequency", "Route", "Take", "Strength", "Start Date", "End Date", "Chief Complaint" }, Syncfusion.Drawing.Color.Khaki);
                foreach (var med in activeMedications)
                {
                    sheet.Range[row, 1].Text = med.BrandName?.Replace(",", " ");
                    sheet.Range[row, 2].Text = med.DrugDetails?.Replace(",", " ");
                    sheet.Range[row, 3].Text = med.Quantity?.ToString();
                    sheet.Range[row, 4].Text = med.Frequency?.Replace(",", " ");
                    sheet.Range[row, 5].Text = med.Route?.Replace(",", " ");
                    sheet.Range[row, 6].Text = med.Take?.Replace(",", " ");
                    sheet.Range[row, 7].Text = med.Strength?.Replace(",", " ");
                    sheet.Range[row, 8].Text = med.StartDate?.ToString("MM/dd/yyyy");
                    sheet.Range[row, 9].Text = med.EndDate?.ToString("MM/dd/yyyy");
                    sheet.Range[row, 10].Text = med.CheifComplaint?.Replace(",", " ");
                    row++;
                }
                int medStartRow = row - activeMedications.Count - 2;
                await InsertChartRightOfGrid("MedicationChart", "Medication Overview", medStartRow, row, 13);
                row = Math.Max(row, medStartRow + 12);

                row++;
            }
           
            row += 2;
            sheet.UsedRange.AutofitColumns();

            async Task<int> InsertChartRightOfGrid(string chartId, string title, int dataGridStartRow, int dataGridEndRow, int chartCol)
            {
                string base64Image = await JS.InvokeAsync<string>("getChartBase64", chartId);
                if (string.IsNullOrEmpty(base64Image)) return 0;

                try
                {
                    // Title row for chart
                    sheet.Range[dataGridStartRow, chartCol, dataGridStartRow, chartCol + 4].Merge();
                    sheet.Range[dataGridStartRow, chartCol].Text = title;
                    sheet.Range[dataGridStartRow, chartCol].CellStyle.Font.Bold = true;
                    sheet.Range[dataGridStartRow, chartCol].CellStyle.Font.Size = 12;
                    sheet.Range[dataGridStartRow, chartCol].CellStyle.Color = Syncfusion.Drawing.Color.LightGray;

                    byte[] imageBytes = Convert.FromBase64String(base64Image.Split(',')[1]);
                    using MemoryStream imageStream = new(imageBytes) { Position = 0 };

                    var pic = sheet.Pictures.AddPicture(dataGridStartRow + 1, chartCol, imageStream);
                    pic.Width = 300;
                    pic.Height = 180;

                    // Ensure a minimum height of 10 rows
                    int estimatedRows = Math.Max(10, pic.Height / 15 + 3);
                    return estimatedRows;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to insert chart {chartId}: {ex.Message}");
                    return 0;
                }
            }

            // Save workbook
            try
            {
                using var stream = new MemoryStream();
                workbook.SaveAs(stream);
                stream.Position = 0;

                var base64 = Convert.ToBase64String(stream.ToArray());
                var fileName = $"{showFlowsheet.FlowsheetName}_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                await JS.InvokeVoidAsync("downloadFileFromBytes", fileName, base64);

                SnackBar.Add("Excel file exported successfully", Severity.Success);
            }
            catch (Exception ex)
            {
                SnackBar.Add($"Failed to export Excel: {ex.Message}", Severity.Error);
            }
            finally
            {
                _isExporting = false;
                StateHasChanged(); // Hide loading indicator
            }

        }

        private async void OnDeleteClicked()
        {
            if (string.IsNullOrWhiteSpace(_selectedFlowsheet))
            {
                _selectedFlowsheet = null;
                showFlowsheet = null;
                flowsheetData = new();
                StateHasChanged();
                return;
            }
            showFlowsheet = FlowsheetList.FirstOrDefault(f => f.FlowsheetName == _selectedFlowsheet);
            showFlowsheet.IsActive = false;
            await FlowsheetService.DeleteFlowsheetAsync(showFlowsheet.Id, activeUserOrganizationId, Subscription);
            SnackBar.Add(Localizer["Flowsheet Deleted Successfully"], Severity.Success);
            FlowsheetList = await FlowsheetService.GetFlowsheetAsync(PatientID, activeUserOrganizationId, Subscription);
            currentFlowsheet = new();
            FlowsheetRecord = new();
            flowsheetData = new();
            _selectedFlowsheet = null;
            IsDropDisabled = false;
            StateHasChanged();
        }

        private void CancelChanges()
        {
            if (string.IsNullOrWhiteSpace(_selectedFlowsheet))
            {
                SnackBar.Add(Localizer["No Flowsheet Selected"], Severity.Warning);
                StateHasChanged();
                return;
            }

            showFlowsheet = FlowsheetList.FirstOrDefault(f => f.FlowsheetName == _selectedFlowsheet);
            IsDropDisabled = showFlowsheet.IsEditable == true ? false : true;
            currentFlowsheet.SerializedData = showFlowsheet.SerializedData;
            flowsheetData = JsonSerializer.Deserialize<List<FlowsheetSection>>(showFlowsheet?.SerializedData);

        }
        private void AddNewFlowsheet()
        {
            IsDropDisabled = false;
            _selectedFlowsheet = null;
            FlowsheetRecord = new();
            flowsheetData = new();
            currentFlowsheet = new();
        }
        
        private async void SaveChanges()
        {
            if (string.IsNullOrWhiteSpace(FlowsheetRecord.FlowsheetName))
            {
                SnackBar.Add(Localizer["FlowsheetName cannot be empty"], Severity.Warning);
                return;
            }
            FlowsheetRecord.Id = Guid.NewGuid();
            FlowsheetRecord.PatientId = PatientID;
            FlowsheetRecord.IsEditable = true;
            FlowsheetRecord.OrganizationId = OrganizationID ?? Guid.Empty;
            FlowsheetRecord.CreatedBy = Guid.Parse(User.id);
            FlowsheetRecord.UpdatedBy = Guid.Parse(User.id);
            FlowsheetRecord.CreatedDate = DateTime.Now;
            FlowsheetRecord.UpdatedDate = DateTime.Now;
            FlowsheetRecord.UpdatedDate = DateTime.Now;
            FlowsheetRecord.SerializedData = JsonSerializer.Serialize(flowsheetData);
            FlowsheetRecord.IsActive = true;
            await FlowsheetService.CreateFlowsheetAsync(new List<Flowsheet> { FlowsheetRecord }, activeUserOrganizationId, Subscription);
            SnackBar.Add(Localizer["Flowsheet Created Successfully"], Severity.Success);
            FlowsheetList = await FlowsheetService.GetFlowsheetAsync(PatientID, activeUserOrganizationId, Subscription);
            currentFlowsheet = new();
            FlowsheetRecord = new();
            flowsheetData = new();
            StateHasChanged();
        }

        private async void UpdateFlowsheet()
        {
            if (string.IsNullOrWhiteSpace(FlowsheetRecord.FlowsheetName))
            {
                SnackBar.Add(Localizer["FlowsheetName cannot be empty"], Severity.Warning);
                return;
            }
            FlowsheetRecord.UpdatedBy = Guid.Parse(User.id);
            FlowsheetRecord.UpdatedDate = DateTime.Now;
            FlowsheetRecord.UpdatedDate = DateTime.Now;
            FlowsheetRecord.SerializedData = JsonSerializer.Serialize(flowsheetData);
            await FlowsheetService.UpdateFlowsheetAsync( FlowsheetRecord , activeUserOrganizationId, Subscription);
            FlowsheetList = await FlowsheetService.GetFlowsheetAsync(PatientID, activeUserOrganizationId, Subscription);
            //currentFlowsheet = new();
            SnackBar.Add(Localizer["Flowsheet Updated Successfully"], Severity.Success);
            ResetField();
            StateHasChanged();
        }

        private RenderFragment<object> OrganizationEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests labTest) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", OrganizationOptions);
            builder.AddAttribute(2, "Value", labTest.TestOrganization);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    labTest.TestOrganization = value;
                }));
            builder.AddAttribute(4, "Placeholder", "Select Organization");
            builder.CloseComponent();
        };
    
        public static class VitalUtils
        {
            public static string? ExtractNumeric(string? input)
            {
                if (string.IsNullOrWhiteSpace(input))
                    return null;

                var match = Regex.Match(input, @"-?\d+(\.\d+)?");
                return match.Success ? match.Value : null;
            }
        }

    }

}