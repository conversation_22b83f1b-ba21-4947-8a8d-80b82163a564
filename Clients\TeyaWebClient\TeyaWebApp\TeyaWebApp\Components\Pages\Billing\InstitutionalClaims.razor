﻿@page "/institutionalclaims"
@using TeyaUIModels.Model
@using MudBlazor
@using MudBlazor.Utilities
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "InstitutionalclaimsAccessPolicy")]
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Data
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<div class="institutional-claims-container">
    <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="py-6">
        <!-- Step Navigation -->
        <MudPaper Elevation="1" Class="mb-4 step-navigation-container">
            <div class="step-navigation">
                <div class="step-item @(activeTabIndex == 0 ? "active" : "completed")" @onclick="() => GoToStep(0)">
                    <div class="step-circle">
                        <MudIcon Icon="@Icons.Material.Filled.Business" />
                    </div>
                    <div class="step-content">
                        <div class="step-title">Personal Info</div>
                        <div class="step-description">Facility & Patient Details</div>
                    </div>
                </div>
                
                <div class="step-connector @(activeTabIndex > 0 ? "completed" : "")"></div>
                
                <div class="step-item @(activeTabIndex == 1 ? "active" : activeTabIndex > 1 ? "completed" : "")" @onclick="() => GoToStep(1)">
                    <div class="step-circle">
                        <MudIcon Icon="@Icons.Material.Filled.Payment" />
                    </div>
                    <div class="step-content">
                        <div class="step-title">Payer Info</div>
                        <div class="step-description">Occurrence & Service Details</div>
                    </div>
                </div>
                
                <div class="step-connector @(activeTabIndex > 1 ? "completed" : "")"></div>
                
                <div class="step-item @(activeTabIndex == 2 ? "active" : activeTabIndex > 2 ? "completed" : "")" @onclick="() => GoToStep(2)">
                    <div class="step-circle">
                        <MudIcon Icon="@Icons.Material.Filled.HealthAndSafety" />
                    </div>
                    <div class="step-content">
                        <div class="step-title">Insurance</div>
                        <div class="step-description">Insurance & Payments</div>
                    </div>
                </div>
                
                <div class="step-connector @(activeTabIndex > 2 ? "completed" : "")"></div>
                
                <div class="step-item @(activeTabIndex == 3 ? "active" : activeTabIndex > 3 ? "completed" : "")" @onclick="() => GoToStep(3)">
                    <div class="step-circle">
                        <MudIcon Icon="@Icons.Material.Filled.MedicalServices" />
                    </div>
                    <div class="step-content">
                        <div class="step-title">Medical Info</div>
                        <div class="step-description">Diagnosis & Services</div>
                    </div>
                </div>
            </div>
        </MudPaper>

        <!-- Content container -->
        <MudPaper Elevation="1" Class="mb-3 modern-content-container">
            <MudContainer MaxWidth="MaxWidth.Large" Class="pa-4">
                @if (activeTabIndex == 0)
                {
                    <MudGrid>
                        <!-- Facility Information (Read-only) -->
                        <MudItem xs="12" md="6">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Business" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Facility Information</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudTextField @bind-Value="@Model.FacilityName"
                                                  Label="Facility Name"
                                                  Variant="Variant.Outlined"
                                                  Class="mb-3 modern-textfield" />
                                    <MudTextField @bind-Value="@Model.FacilityAddress"
                                                  Label="Address"
                                                  Variant="Variant.Outlined"
                                                  scrollbar="true"
                                                  Class="mb-3 modern-textfield" />
                                    <MudTextField @bind-Value="@Model.FacilityPhone"
                                                  Label="Phone"
                                                  Variant="Variant.Outlined"
                                                  Class="modern-textfield" />
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Local Use Fields -->
                        <MudItem xs="12" md="6">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Settings" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Local Use (LU) Fields</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudTextField @bind-Value="@Model.LocalUseField1"
                                                  Label="LU Field 1"
                                                  Variant="Variant.Outlined"
                                                  Class="mb-3 modern-textfield" />
                                    <MudTextField @bind-Value="@Model.LocalUseField2"
                                                  Label="LU Field 2"
                                                  Variant="Variant.Outlined"
                                                  Class="mb-3 modern-textfield" />
                                    <MudTextField @bind-Value="Model.LocalUseField3"
                                                  Label="LU Field 3"
                                                  Variant="Variant.Outlined"
                                                  Class="modern-textfield" />
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Patient Control and Bill Information -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.ControlPoint" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Control Information</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudGrid>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="@Model.PatientControlNo"
                                                          Label="Patient Control No"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="@Model.BillType"
                                                          Label="Bill Type"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="@Model.FedTaxNo"
                                                          Label="Fed. Tax No."
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Statement Covers Period -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.DateRange" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Statement Covers Period</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudGrid>
                                        <MudItem xs="12" md="3">
                                            <MudDatePicker @bind-Date="@Model.StatementFromDate"
                                                           Label="From Date"
                                                           Variant="Variant.Outlined"
                                                           Class="mb-3 modern-datepicker" />
                                        </MudItem>
                                        <MudItem xs="12" md="3">
                                            <MudDatePicker @bind-Date="@Model.StatementThroughDate"
                                                           Label="Through Date"
                                                           Variant="Variant.Outlined"
                                                           Class="mb-3 modern-datepicker" />
                                        </MudItem>
                                        <MudItem xs="12" md="2">
                                            <MudTextField @bind-Value="@Model.CoveredDays"
                                                          Label="Covered Days"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="2">
                                            <MudTextField @bind-Value="@Model.NonCoveredDays"
                                                          Label="Non-Covered Days"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="2">
                                            <MudTextField @bind-Value="@Model.CoinsuranceDays"
                                                          Label="Coinsurance Days"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Patient Name and Address -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Person" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Patient Information</MudText>
                                        </div>
                                    </CardHeaderContent>
                                    <CardHeaderActions>
                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Info"
                                                   Size="Size.Small"
                                                   StartIcon="@Icons.Material.Filled.Info"
                                                   Class="action-button">
                                            Info
                                        </MudButton>
                                    </CardHeaderActions>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudGrid>
                                        <MudItem xs="12" md="6">
                                            <MudTextField @bind-Value="@Model.PatientName"
                                                          Label="Patient Name"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="6">
                                            <MudTextField @bind-Value="@Model.PatientAddress"
                                                          Label="Patient Address"
                                                          Variant="Variant.Outlined"
                                                          scrollbar="true"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Admission Information -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.LocalHospital" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Admission Information</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudGrid>
                                        <MudItem xs="12" md="2">
                                            <MudDatePicker @bind-Date="@Model.BirthDate"
                                                         Label="Birth Date"
                                                         Variant="Variant.Outlined"
                                                         Class="mb-3 modern-datepicker" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudSelect T="string" @bind-Value="@Model.Sex" Label="Sex" Variant="Variant.Outlined" Class="mb-3">
                                                <MudSelectItem Value="@("M")">M</MudSelectItem>
                                                <MudSelectItem Value="@("F")">F</MudSelectItem>
                                            </MudSelect>
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudSelect T="string" @bind-Value="@Model.MaritalStatus" Label="MS" Variant="Variant.Outlined" Class="mb-3">
                                                <MudSelectItem Value="@("M")">M</MudSelectItem>
                                                <MudSelectItem Value="@("S")">S</MudSelectItem>
                                            </MudSelect>
                                        </MudItem>
                                        <MudItem xs="12" md="2">
                                            <MudDatePicker @bind-Date="@Model.AdmissionDate"
                                                         Label="Adm Date"
                                                         Variant="Variant.Outlined"
                                                         Class="mb-3 modern-datepicker" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.AdmissionHour"
                                                        Label="AHR"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.AdmissionType"
                                                        Label="Type"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.AdmissionSource"
                                                        Label="SRC"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.DischargeHour"
                                                        Label="DHR"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.Status"
                                                        Label="STAT"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="3">
                                            <MudTextField @bind-Value="@Model.MedicalRecordNo"
                                                        Label="Medical Record No."
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Condition Codes -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.ListAlt" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Condition Codes</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudGrid>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode24"
                                                        Label="24"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode25"
                                                        Label="25"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode26"
                                                        Label="26"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode27"
                                                        Label="27"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode28"
                                                        Label="28"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode29"
                                                        Label="29"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="1">
                                            <MudTextField @bind-Value="@Model.ConditionCode30"
                                                        Label="30"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="6" md="2">
                                            <MudTextField @bind-Value="@Model.AccidentState"
                                                        Label="ACDT STATE"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="3">
                                            <MudTextField @bind-Value="@Model.LocalUseField"
                                                        Label="LU"
                                                        Variant="Variant.Outlined"
                                                        Class="mb-3 modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    </MudGrid>
                }
                @if (activeTabIndex == 1)
                {
                    <MudGrid>
                        <!-- Occurrence Information -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Event" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Occurrence Information</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <SfGrid DataSource="@OccurrenceItems"
                                           AllowPaging="true"
                                           AllowSelection="true"
                                            GridLines="GridLine.Both"
                                           @bind-SelectedRecords="@selectedOccurrenceRecords"
                                           Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(OccurrenceItem.Code)" HeaderText="Code" Width="120"></GridColumn>
                                            <GridColumn Field="@nameof(OccurrenceItem.Date)" HeaderText="Date" Width="150" Format="d"></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Value Codes -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Code" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Value Codes</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <SfGrid DataSource="@ValueCodeItems"
                                           AllowPaging="true"
                                           AllowSelection="true"
                                            GridLines="GridLine.Both"
                                           @bind-SelectedRecords="@selectedValueCodeRecords"
                                           Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(ValueCodeItem.Code)" HeaderText="Code" Width="100"></GridColumn>
                                            <GridColumn Field="@nameof(ValueCodeItem.Amount)" HeaderText="Amount" Width="120" Format="C2"></GridColumn>
                                            <GridColumn Field="@nameof(ValueCodeItem.Description)" HeaderText="Description" Width="200"></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Service Details -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Service Details</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <SfGrid DataSource="@ServiceDetailItems"
                                           AllowPaging="true"
                                            GridLines="GridLine.Both"
                                           AllowSelection="true"
                                           @bind-SelectedRecords="@selectedServiceDetailRecords"
                                           Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(ServiceDetailItem.ServiceCode)" HeaderText="Service Code" Width="120"></GridColumn>
                                            <GridColumn Field="@nameof(ServiceDetailItem.Description)" HeaderText="Description" Width="200"></GridColumn>
                                            <GridColumn Field="@nameof(ServiceDetailItem.Date)" HeaderText="Date" Width="120" Format="d"></GridColumn>
                                            <GridColumn Field="@nameof(ServiceDetailItem.Amount)" HeaderText="Amount" Width="100" Format="C2"></GridColumn>
                                            <GridColumn Field="@nameof(ServiceDetailItem.Units)" HeaderText="Units" Width="80"></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </MudCardContent> 
                            </MudCard>
                        </MudItem>
                    </MudGrid>
                }
                @if (activeTabIndex == 2)
                {
                    <MudGrid>
                        <!-- Bill to Patient -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                        <MudCardContent Class="card-content-padding">
                                            <MudCheckBox T="bool"
                                                         @bind-Checked="@Model.BillToPatient"
                                                         Label="Bill to Patient"
                                                         Color="Color.Primary" />
                                        </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Insurance Information -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.HealthAndSafety" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Insurance Information</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <SfGrid DataSource="@Model.InsuranceItems"
                                           AllowPaging="true"
                                            GridLines="GridLine.Both"
                                           @bind-SelectedRecords="@selectedInsuranceRecords"
                                           SelectionSettings="@(new GridSelectionSettings { Type = Syncfusion.Blazor.Grids.SelectionType.Single })"
                                           AllowSelection="true"
                                            Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                        <GridEvents RowSelected="OnInsuranceSelectedHandler" TValue="InsuranceItem" />
                                        <GridColumns>
                                            <GridColumn Field="@nameof(InsuranceItem.ID)" HeaderText="ID" Width="100" TextAlign="TextAlign.Center" Type="ColumnType.String"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.InstitutionalClaimId)" HeaderText="Claim ID" Width="150" TextAlign="TextAlign.Center" Type="ColumnType.String"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.PolicyNumber)" HeaderText="Policy #" Width="150"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.InsurancePlanName)" HeaderText="Plan Name" Width="180"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.InsuranceCompanyName)" HeaderText="Company Name" Width="200"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.InsuranceType)" HeaderText="Type" Width="120"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.CoverageAmount)" HeaderText="Coverage" Width="130" Format="C2" TextAlign="TextAlign.Right" Type="ColumnType.Number"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.PriorAuthorizationNumber)" HeaderText="Auth #" Width="150"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.RelationshipToSubscriber)" HeaderText="Relationship" Width="140"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.SubscriberName)" HeaderText="Subscriber Name" Width="200"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.SubscriberBirthDate)" HeaderText="Birth Date" Width="140" Format="d" Type="ColumnType.Date" TextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(InsuranceItem.SubscriberEmployer)" HeaderText="Employer" Width="180"></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- Payments/Adjustments/Refunds -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Payment" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Payments/Adjustments/Refunds</MudText>
                                        </div>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <SfGrid DataSource="@Model.PaymentItems"
                                           AllowPaging="true"
                                            GridLines="GridLine.Both"
                                           @bind-SelectedRecords="@selectedPaymentRecords"
                                           SelectionSettings="@(new GridSelectionSettings { Type = Syncfusion.Blazor.Grids.SelectionType.Single })"
                                           AllowSelection="true"
                                            Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                        <GridEvents RowSelected="OnPaymentSelectedHandler" TValue="PaymentItem" />
                                        <GridColumns>
                                            <GridColumn Field="@nameof(PaymentItem.ID)" HeaderText="ID" Width="100" TextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.InstitutionalClaimId)" HeaderText="Claim ID" Width="150" TextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.Amount)" HeaderText="Amount" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.PaymentDate)" HeaderText="Payment Date" Width="150" Format="d" Type="ColumnType.Date" TextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.PaymentMethod)" HeaderText="Method" Width="120"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.PaymentSource)" HeaderText="Source" Width="120"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.TransactionId)" HeaderText="Transaction ID" Width="200"></GridColumn>
                                            <GridColumn Field="@nameof(PaymentItem.Notes)" HeaderText="Notes" Width="250"></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>

                        <!-- ICD-9 Codes and LU Fields -->
                        <MudItem xs="12">
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <div class="card-header-content">
                                                    <MudIcon Icon="@Icons.Material.Filled.MedicalInformation" Class="header-icon" />
                                                    <MudText Typo="Typo.h6" Class="header-title">ICD-9 Codes</MudText>
                                                </div>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent Class="card-content-padding">
                                            <SfGrid DataSource="@ICD9CodeItems"
                                                   AllowPaging="true"
                                                   GridLines="GridLine.Both"
                                                   @bind-SelectedRecords="@selectedICD9Records"
                                                   SelectionSettings="@(new GridSelectionSettings { Type = Syncfusion.Blazor.Grids.SelectionType.Single })"
                                                   AllowSelection="true"
                                                    Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                                <GridEvents RowSelected="OnICD9CodeSelectedHandler" TValue="ICD9CodeItem" />
                                                <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                                <GridColumns>
                                                    <GridColumn Field="@nameof(ICD9CodeItem.Code)" HeaderText="Code" Width="120"></GridColumn>
                                                    <GridColumn Field="@nameof(ICD9CodeItem.Name)" HeaderText="Name" Width="200"></GridColumn>
                                                </GridColumns>
                                            </SfGrid>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <div class="card-header-content">
                                                    <MudIcon Icon="@Icons.Material.Filled.DataObject" Class="header-icon" />
                                                    <MudText Typo="Typo.h6" Class="header-title">LU Fields</MudText>
                                                </div>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent Class="card-content-padding">
                                            <SfGrid DataSource="@LUFieldItems"
                                                   AllowPaging="true"
                                                   GridLines="GridLine.Both"
                                                   @bind-SelectedRecords="@selectedLURecords"
                                                   AllowSelection="true"
                                                    Toolbar="@(new List<string>() { "Add", "Edit", "Delete", "Update", "Cancel" })">
                                                <GridEditSettings AllowAdding="true" AllowEditing="true" Mode="EditMode.Normal"></GridEditSettings>
                                                <GridColumns>
                                                    <GridColumn Field="@nameof(LUFieldItem.Field)" HeaderText="LU" AllowEditing="false" Width="120"></GridColumn>
                                                    <GridColumn Field="@nameof(LUFieldItem.Value)" HeaderText="Value" Width="200"></GridColumn>
                                                </GridColumns>
                                            </SfGrid>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                            </MudGrid>
                        </MudItem>
                    </MudGrid>
                }
                @if (activeTabIndex == 3)
                {
                    <MudGrid>
                        <!-- Top Row - Field Summary Cards -->
                        <MudItem xs="12">
                            <MudGrid>
                                <MudItem xs="12" md="3">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardContent Class="card-content-padding">
                                            <MudText Typo="Typo.h6" Color="Color.Primary">ADM. DIAG. CD</MudText>
                                            <MudTextField T="string"
                                                          @bind-Value="@Model.AdmDiagCD"
                                                          Variant="Variant.Outlined"
                                                          Margin="Margin.Dense"
                                                          Class="modern-textfield" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                                <MudItem xs="12" md="3">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardContent Class="card-content-padding">
                                            <MudText Typo="Typo.h6" Color="Color.Primary">E CODE</MudText>
                                            <MudTextField T="string"
                                                          @bind-Value="@Model.ECode"
                                                          Variant="Variant.Outlined"
                                                          Margin="Margin.Dense"
                                                          Class="modern-textfield" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                                <MudItem xs="12" md="3">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardContent Class="card-content-padding">
                                            <MudText Typo="Typo.h6" Color="Color.Primary">LU1</MudText>
                                            <MudTextField T="string"
                                                          @bind-Value="@Model.LU1"
                                                          Variant="Variant.Outlined"
                                                          Margin="Margin.Dense"
                                                          Class="modern-textfield" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                                <MudItem xs="12" md="3">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardContent Class="card-content-padding">
                                            <MudText Typo="Typo.h6" Color="Color.Primary">LU2</MudText>
                                            <MudTextField T="string"
                                                          @bind-Value="@Model.LU2"
                                                          Variant="Variant.Outlined"
                                                          Margin="Margin.Dense"
                                                          Class="modern-textfield" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                            </MudGrid>
                        </MudItem>

                        <!-- Middle Row - Detailed Information Cards -->
                        <MudItem xs="12">
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <div class="card-header-content">
                                                    <MudIcon Icon="@Icons.Material.Filled.Description" Class="header-icon" />
                                                    <MudText Typo="Typo.h6" Class="header-title">Principal Procedure</MudText>
                                                </div>
                                            </CardHeaderContent>
                                            <CardHeaderActions>
                                                <MudButton Variant="Variant.Filled" 
                                                           Color="Color.Success" 
                                                           StartIcon="@Icons.Material.Filled.Add" 
                                                           OnClick="AddICD9Code"
                                                           Size="Size.Small"
                                                           Class="me-2 action-button">
                                                    Add
                                                </MudButton>
                                                <MudButton Variant="Variant.Filled" 
                                                           Color="Color.Error" 
                                                           StartIcon="@Icons.Material.Filled.Remove" 
                                                           OnClick="RemoveICD9Code"
                                                           Size="Size.Small"
                                                           Class="action-button">
                                                    Remove
                                                </MudButton>
                                            </CardHeaderActions>
                                        </MudCardHeader>
                                        <MudCardContent Class="card-content-padding">
                                            <MudTextField @bind-Value="@Model.PrincipalProcedureCode"
                                                          Label="Procedure Code"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                            <MudDatePicker @bind-Date="@Model.PrincipalProcedureDate"
                                                           Label="Procedure Date"
                                                           Variant="Variant.Outlined"
                                                           Class="mb-3 modern-datepicker" />
                                            <MudTextField @bind-Value="@Model.AttendingPhysID"
                                                          Label="Attending Physician ID"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                                <MudItem xs="12" md="6">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <div class="card-header-content">
                                                    <MudIcon Icon="@Icons.Material.Filled.ListAlt" Class="header-icon" />
                                                    <MudText Typo="Typo.h6" Class="header-title">Other Procedures</MudText>
                                                </div>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent Class="card-content-padding">
                                            <!-- Other Procedures -->
                                            <MudGrid>
                                                @for (int i = 0; i < OtherProcedures.Count; i++)
                                                {
                                                    <MudItem xs="12" md="6">
                                                        <MudTextField @bind-Value="OtherProcedures[i]"
                                                                      Label=@($"Procedure {i + 1}")
                                                                      Variant="Variant.Outlined"
                                                                      Class="mb-3 modern-textfield" />
                                                    </MudItem>
                                                }
                                            </MudGrid>

                                            <MudButton Variant="Variant.Filled"
                                                       Color="Color.Primary"
                                                       StartIcon="@Icons.Material.Filled.Add"
                                                       OnClick="@(() => OtherProcedures.Add(string.Empty))"
                                                       Class="mt-2">
                                                Add Another Procedure
                                            </MudButton>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                            </MudGrid>
                        </MudItem>

                        <!-- Bottom Row - Provider and Remarks Cards -->
                        <MudItem xs="12">
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <div class="card-header-content">
                                                    <MudIcon Icon="@Icons.Material.Filled.Person" Class="header-icon" />
                                                    <MudText Typo="Typo.h6" Class="header-title">Provider Representative</MudText>
                                                </div>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent Class="card-content-padding">
                                            <MudTextField @bind-Value="@Model.ProviderRepresentative"
                                                          Label="Provider Representative"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                                <MudItem xs="12" md="6">
                                    <MudCard Class="mb-4 member-details-card">
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <div class="card-header-content">
                                                    <MudIcon Icon="@Icons.Material.Filled.DateRange" Class="header-icon" />
                                                    <MudText Typo="Typo.h6" Class="header-title">Provider Date</MudText>
                                                </div>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent Class="card-content-padding">
                                            <MudDatePicker @bind-Date="@Model.ProviderDate"
                                                           Label="Provider Date"
                                                           Variant="Variant.Outlined"
                                                           Class="mb-3 modern-datepicker" />
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                            </MudGrid>
                        </MudItem>

                        <!-- Remarks Section -->
                        <MudItem xs="12">
                            <MudCard Class="mb-4 member-details-card">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <div class="card-header-content">
                                            <MudIcon Icon="@Icons.Material.Filled.Comment" Class="header-icon" />
                                            <MudText Typo="Typo.h6" Class="header-title">Remarks</MudText>
                                        </div>
                                    </CardHeaderContent>
                                    <CardHeaderActions>
                                        <MudButton Variant="Variant.Filled" 
                                                   Color="Color.Info" 
                                                   StartIcon="@Icons.Material.Filled.Add" 
                                                   OnClick="@(() => Model.Remark_A = string.Empty)"
                                                   Size="Size.Small"
                                                   Class="me-2 action-button">
                                            Add Remark
                                        </MudButton>
                                    </CardHeaderActions>
                                </MudCardHeader>
                                <MudCardContent Class="card-content-padding">
                                    <MudGrid>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="@Model.Remark_A"
                                                          Label="Remark A"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="@Model.Remark_B"
                                                          Label="Remark B"
                                                          Variant="Variant.Outlined"
                                                          Class="mb-3 modern-textfield" />
                                        </MudItem>
                                        <MudItem xs="12" md="4">
                                            <MudTextField @bind-Value="@Model.Remark_C"
                                                          Label="Remark C"
                                                          Variant="Variant.Outlined"
                                                          Class="modern-textfield" />
                                        </MudItem>
                                    </MudGrid>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    </MudGrid>
                }
                <!-- Navigation Buttons -->
                <MudPaper Class="d-flex justify-space-between align-center px-4 py-3 mt-6">
                    <div>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   OnClick="@(() => GoToStep(activeTabIndex - 1))"
                                   Disabled="@(activeTabIndex == 0)"
                                   StartIcon="@Icons.Material.Filled.ArrowBack">
                            Previous
                        </MudButton>
                    </div>
                    <div>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Success"
                                   OnClick="SaveClaim"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   Class="mx-2">
                            Save
                        </MudButton>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Error"
                                   OnClick="DeleteClaim"
                                   StartIcon="@Icons.Material.Filled.Delete"
                                   Class="mx-2">
                            Delete
                        </MudButton>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   OnClick="@(() => GoToStep(activeTabIndex + 1))"
                                   Disabled="@(activeTabIndex == 3)"
                                   EndIcon="@Icons.Material.Filled.ArrowForward">
                            Next
                        </MudButton>
                    </div>
                </MudPaper>
            </MudContainer>
        </MudPaper>
    </MudContainer>
</div>

<style>
    .institutional-claims-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .step-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .step-item {
        flex: 1;
        text-align: center;
        position: relative;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e0e0e0;
        margin: 0 auto 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        color: #fff;
        transition: background-color 0.3s;
    }

    .step-item.active .step-circle {
        background-color: #007bff;
    }

    .step-item.completed .step-circle {
        background-color: #28a745;
    }

    .step-title {
        font-weight: 500;
        margin-bottom: 4px;
    }

    .step-description {
        font-size: 14px;
        color: #666;
    }

    .step-connector {
        flex: 1;
        height: 2px;
        background-color: #e0e0e0;
        position: relative;
        top: 18px;
    }

    .step-connector.completed {
        background-color: #28a745;
    }

    .modern-content-container {
        background-color: #f9f9f9;
        border-radius: 8px;
        overflow: hidden;
    }

    .member-details-card {
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 16px;
    }

    .card-header-content {
        display: flex;
        align-items: center;
    }

    .header-icon {
        font-size: 24px;
        margin-right: 8px;
        color: #007bff;
    }

    .header-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
    }

    .card-content-padding {
        padding: 16px;
    }

    .modern-textfield {
        border-radius: 4px;
    }

    .action-button {
        border-radius: 4px;
        padding-left: 16px;
        padding-right: 16px;
    }

    .mud-datepicker-input {
        border-radius: 4px;
    }

    .mud-data-grid {
        border-radius: 4px;
        overflow: hidden;
    }

    .mud-data-grid-header {
        background-color: #007bff;
        color: #fff;
        font-weight: 500;
    }

    .mud-data-grid-cell {
        padding: 8px 12px;
        font-size: 14px;
    }

    .mud-button {
        border-radius: 4px;
    }

    .step-navigation-container {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .card-header-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .card-header-actions .mud-button {
        margin-left: 8px;
    }
</style>