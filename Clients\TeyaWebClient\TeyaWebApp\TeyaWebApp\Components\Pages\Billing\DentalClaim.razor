﻿@page "/dentalclaims"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "DentalClaimsAccessPolicy")]
@using TeyaWebApp.Components.Layout
@layout Admin
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.DropDowns
@using System.Net.Http
@using System.Net.Http.Json
@using TeyaWebApp.Services
@using TeyaWebApp.TeyaAIScribeResources
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using static TeyaUIModels.Model.DentalClaims
@inject IUserLicenseService UserLicenseService
@inject IInsuranceService InsuranceService
@inject IPlanTypeService PlanTypeService
@inject IMemberService MemberService
@inject IDentalClaimsService DentalClaimsService
@inject HttpClient Http

<div class="dental-claim-form">
    <div class="form-header">
        <h1 class="form-title">Dental Claim Form</h1>
        <div class="header-accent"></div>
    </div>
    <div class="form-group patient-selector" style="display: flex; align-items: flex-start; gap: 10px;">
        <div style="flex: 1;">
            <label class="form-label">Select Patient</label>
            <SfDropDownList TValue="Member" TItem="Member"
                            CssClass="form-control-dropdown-compact"
                            Placeholder="@Localizer["Select Patient"]"
                            DataSource="@PatientList"
                            Value="@selectedPatient"
                            ValueChanged="OnPatientChanged">
                <DropDownListFieldSettings Value="UserName" Text="UserName"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
        @if (selectedPatient != null)
        {
            <button type="button"
                    class="btn btn-outline-danger btn-sm"
                    style="height: fit-content; padding: 6px 12px; margin-top: 26px; align-self: flex-start;"
                    @onclick="RemoveSelectedPatient"
                    title="Remove selected patient">
                Remove
            </button>
        }
    </div>
    <div class="claim-header-card">
        <div class="form-group">
            <label class="form-label">Claim Number</label>
            <SfTextBox @bind-Value="@DentalClaims.ClaimNumber" Enabled="false" CssClass="form-control-compact"></SfTextBox>
        </div>
        <div class="form-group">
            <label class="form-label">Claim Date</label>
            <SfDatePicker @bind-Value="@DentalClaims.ClaimDate" Format="MM-dd-yyyy" Width="180px"></SfDatePicker>
        </div>
        <div class="form-group">
            <label class="form-label">Service Date</label>
            <SfDatePicker @bind-Value="@DentalClaims.ServiceDate" Format="MM-dd-yyyy" Width="180px"></SfDatePicker>
        </div>
    </div>

    <SfTab CssClass="custom-tabs">
        <TabItems>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="Patient Coverage"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="form-section">
                        <h2 class="section-title">
                            PATIENT COVERAGE INFORMATION
                        </h2>
                        <div class="patient-info-grid">
                            <div class="form-group">
                                <label class="form-label">Patient Info</label>
                                <SfTextBox @bind-Value="@DentalClaims.PatientInfo"
                                           Multiline="true"
                                           CssClass="form-control auto-expanding-textarea"
                                           Readonly="true">
                                </SfTextBox>
                            </div>
                            <div class="checkboxes-section">
                                <div class="checkbox-vertical-group">
                                    <SfCheckBox Label="Dentist Pretreatment Estimates" @bind-Checked="@DentalClaims.DentistPretreatmentEstimates" CssClass="custom-checkbox"></SfCheckBox>
                                    <SfCheckBox Label="Dentist Statement Of Actual Services" @bind-Checked="@DentalClaims.DentistStatementOfActualServices" CssClass="custom-checkbox"></SfCheckBox>
                                    <SfCheckBox Label="Medicaid Claim" @bind-Checked="@DentalClaims.MedicaidClaim" CssClass="custom-checkbox"></SfCheckBox>
                                    <SfCheckBox Label="EPSDT" @bind-Checked="@DentalClaims.EPSDT" CssClass="custom-checkbox"></SfCheckBox>
                                </div>
                                <!-- Add Prior Authorization Number section -->
                                <div class="prior-auth-section" style="display: flex; align-items: center; gap: 10px;">
                                    <label class="form-label" style="margin: 0;">Prior Authorization Number</label>
                                    <SfTextBox @bind-Value="@DentalClaims.PriorAuthorizationNumber" CssClass="form-control prior-auth-input"></SfTextBox>
                                </div>
                            </div>
                        </div>
                        <div class="form-section">
                            <h2 class="section-title">
                                INSURANCE INFORMATION
                            </h2>
                            @if (isLoading)
                            {
                                <div class="loading-container">
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">Loading insurance data...</div>
                                </div>
                            }
                            else
                            {
                                <div class="insurance-grid-container">
                                    <SfGrid DataSource="@InsuranceData" AllowPaging="true" CssClass="custom-grid">
                                        <GridColumns>
                                            <GridColumn Field=@nameof(Insurance.IsSelected) HeaderText="Selected" DisplayAsCheckBox="true" TextAlign="TextAlign.Center" Width="80"></GridColumn>
                                            <GridColumn Field=@nameof(Insurance.PrimaryInsuranceProvider) HeaderText="Provider" Width="120"></GridColumn>
                                            <GridColumn Field=@nameof(Insurance.Subscriber) HeaderText="Subscriber" Width="150"></GridColumn>
                                            <GridColumn Field=@nameof(Insurance.SubscriberPhone) HeaderText="Subscriber N" Width="120"></GridColumn>
                                            <GridColumn Field=@nameof(Insurance.GroupNumber) HeaderText="Group No" Width="100"></GridColumn>
                                            <GridColumn Field=@nameof(Insurance.SubscriberEmployer) HeaderText="Employer Name" Width="150"></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="claim-status-section">
                        <div class="status-header">
                            <h3 class="section-title">
                                CLAIM STATUS
                            </h3>
                            <div class="status-tabs">
                                <SfButton CssClass="status-tab active">General</SfButton>
                                <SfButton CssClass="status-tab">Error Log</SfButton>
                            </div>
                        </div>


                        <div class="status-info">
                            <div class="status-row">
                                <!-- All four elements in same row -->
                                <div class="form-row" style="display: flex; gap: 20px; align-items: center;">
                                    <div class="status-dropdown-container">
                                        <span class="status-label">@Localizer["Status"]:</span>
                                        <SfDropDownList TValue="string" TItem="string"
                                                        DataSource="@statusOptions"
                                                        @bind-Value="DentalClaims.Status"
                                                        CssClass="status-dropdown">
                                        </SfDropDownList>
                                    </div>
                                    <div class="status-checkbox-container">
                                        <span class="status-label">@Localizer["Bill to Patient"]:</span>
                                        <SfCheckBox Checked="@DentalClaims.BillToPatient"
                                                    CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnBillToPatientChanged))"
                                                    CssClass="status-checkbox">
                                        </SfCheckBox>
                                    </div>
                                    <div class="copay-container">
                                        <span class="status-label">@Localizer["Copay"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.Copay"
                                                          ValueChange="@(EventCallback.Factory.Create<decimal>(this, OnCopayChanged))"
                                                          Format="$#,##0.00"
                                                          CssClass="copay-input"
                                                          Enabled="@DentalClaims.BillToPatient">
                                        </SfNumericTextBox>
                                    </div>
                                    <div class="uncovered-container">
                                        <span class="status-label">@Localizer["Pt. Uncovered Amt"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.PatientUncoveredAmount"
                                                          Format="$#,##0.00"
                                                          CssClass="copay-input"
                                                          Enabled="false">
                                        </SfNumericTextBox>
                                    </div>
                                </div>
                            </div>
                            <div class="amounts-grid">
                                <div class="amounts-column">
                                    <div class="amount-row">
                                        <span class="amount-header">@Localizer["Patient Portion"]</span>
                                    </div>
                                    <div class="amount-row">
                                        <span class="amount-label">@Localizer["Charges"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.PatientCharges"
                                                          Format="$#,##0.00"
                                                          Enabled="false"
                                                          CssClass="amount-input"></SfNumericTextBox>
                                    </div>
                                    <div class="amount-row">
                                        <span class="amount-label">@Localizer["Payments"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.PatientPayments"
                                                          Format="$#,##0.00"
                                                          CssClass="amount-input"
                                                          Enabled="false"></SfNumericTextBox>
                                    </div>
                                    <div class="amount-row">
                                        <span class="amount-label">@Localizer["Balance"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.PatientBalance"
                                                          Format="$#,##0.00"
                                                          CssClass="amount-input"
                                                          Enabled="false"></SfNumericTextBox>
                                    </div>
                                </div>
                                <div class="amounts-column">
                                    <div class="amount-row">
                                        <span class="amount-header">@Localizer["Total"]</span>
                                    </div>
                                    <div class="amount-row">
                                        <span class="amount-label">@Localizer["Charges"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.TotalCharges"
                                                          Format="$#,##0.00"
                                                          CssClass="amount-input"
                                                          Enabled="false"></SfNumericTextBox>
                                    </div>
                                    <div class="amount-row">
                                        <span class="amount-label">@Localizer["Payments"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.TotalPayments"
                                                          ValueChange="@(EventCallback.Factory.Create<decimal>(this, OnTotalPaymentsChanged))"
                                                          Format="$#,##0.00"
                                                          CssClass="amount-input"
                                                          Enabled="false"></SfNumericTextBox>
                                    </div>
                                    <div class="amount-row">
                                        <span class="amount-label">@Localizer["Balance"]:</span>
                                        <SfNumericTextBox @bind-Value="@DentalClaims.TotalBalance"
                                                          Format="$#,##0.00"
                                                          Enabled="false"
                                                          CssClass="amount-input"></SfNumericTextBox>
                                    </div>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <SfButton CssClass="action-btn print-btn">Print</SfButton>
                                <SfButton CssClass="action-btn ok-btn"
                                          OnClick="@(EventCallback.Factory.Create(this, OnOkClicked))">OK</SfButton>
                                <SfButton CssClass="action-btn cancel-btn">Cancel</SfButton>
                                <SfButton CssClass="action-btn adjustments-btn">Adjustments</SfButton>
                                <SfButton CssClass="action-btn convert-btn">Convert HCFA</SfButton>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>

            <TabItem>
                <ChildContent>
                    <TabHeader Text="Charges"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="form-section">
                        <h2 class="section-title">
                            CHARGES
                        </h2>
                        <div class="service-rendered-section">
                            <h3 class="subsection-title">Service Rendered</h3>
                            <div class="grid-container">
                                <SfGrid @ref="ServiceGrid"
                                        TValue="DentalClaimsCharges"
                                        Style="font-size: 0.85rem; margin-top: 24px;"
                                        DataSource="@ServiceCharges"
                                        AllowPaging="true"
                                        PageSettings-PageSize="5"
                                        GridLines="GridLine.Both"
                                        AllowEditing="true"
                                        Toolbar="@(new List<string>() { "Add" })">
                                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                    <GridPageSettings PageSize="10"></GridPageSettings>
                                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="DentalClaimsCharges"></GridEvents>
                                    <GridColumns>
                                        <GridColumn Field="@nameof(DentalClaimsCharges.ChargesId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                        <GridColumn Field="@nameof(DentalClaimsCharges.ToothNumber)"
                                                    HeaderText="Tth No"
                                                    Width="15"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(DentalClaimsCharges.Surface)"
                                                    HeaderText="Srfc"
                                                    Width="15"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"></GridColumn>
                                        <GridColumn Field="@nameof(DentalClaimsCharges.Description)"
                                                    HeaderText="Description of Service"
                                                    Width="30"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    TextAlign="TextAlign.Left"></GridColumn>
                                        <GridColumn Field="@nameof(DentalClaimsCharges.DOS)"
                                                    HeaderText="DOS"
                                                    Width="20"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    TextAlign="TextAlign.Left"
                                                    Format="d"></GridColumn>
                                        <GridColumn Field="@nameof(DentalClaimsCharges.Fee)"
                                                    HeaderText="Fee"
                                                    Width="15"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center"
                                                    Format="$#,##0.00"
                                                    Type="ColumnType.Number"></GridColumn>
                                        <GridColumn HeaderText="Actions"
                                                    Width="15"
                                                    TextAlign="TextAlign.Center"
                                                    HeaderTextAlign="TextAlign.Center">
                                            <GridCommandColumns>
                                                <GridCommandColumn Type="CommandButtonType.Edit"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Delete"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Save"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                   ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                            </GridCommandColumns>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>
                        </div>

                        <!-- ICD Codes Section -->
                        <div class="icd-section">
                            <h3 class="subsection-title">ICD-10 Codes</h3>
                            <div class="icd-controls">
                                <MudGrid Spacing="3" Style="align-items: center; margin-top: 8px;">
                                    <MudItem xs="4">
                                        <MudAutocomplete T="string"
                                                         Label="@Localizer["Search ICD By Codes or Description"]"
                                                         Value="currentICDSelection"
                                                         ValueChanged="OnICDNameChanged"
                                                         SearchFunc="SearchICDCodes"
                                                         ToStringFunc="@(s => s)"
                                                         CoerceText="true"
                                                         Clearable="true"
                                                         Dense="true"
                                                         ResetValueOnEmptyText="true"
                                                         Variant="Variant.Outlined"
                                                         Margin="Margin.Dense"
                                                         MinCharacters="2"
                                                         Style="width: 100%;" />
                                    </MudItem>
                                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                                        <MudButton Color="Color.Primary"
                                                   OnClick="AddICDCode"
                                                   Variant="Variant.Filled"
                                                   Dense="true"
                                                   Style="min-width: 70px; height: 35px;">
                                            @Localizer["Add"]
                                        </MudButton>
                                    </MudItem>
                                </MudGrid>
                            </div>

                            <!-- Display Added ICD Codes -->
                            <div class="icd-list-section">
                                <h4 class="subsection-title">Added ICD Codes</h4>
                                @if (AddedICDCodes != null)
                                {
                                    <SfGrid @ref="ICDGrid"
                                            TValue="DentalClaimsICD"
                                            Style="font-size: 0.85rem;"
                                            DataSource="@AddedICDCodes"
                                            AllowPaging="false"
                                            GridLines="GridLine.Both"
                                            AllowEditing="true"
                                            Height="120">
                                        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridEvents OnActionComplete="ICDActionCompletedHandler" OnActionBegin="ICDActionBeginHandler" TValue="DentalClaimsICD"></GridEvents>
                                        <GridColumns>
                                            <GridColumn Field="@nameof(DentalClaimsICD.ICDId)" IsPrimaryKey="true" Visible="false"></GridColumn>
                                            <GridColumn Field="@nameof(DentalClaimsICD.Code)"
                                                        HeaderText="ICD Code"
                                                        Width="30"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn Field="@nameof(DentalClaimsICD.Description)"
                                                        HeaderText="Description"
                                                        Width="55"
                                                        HeaderTextAlign="TextAlign.Center"
                                                        TextAlign="TextAlign.Center"></GridColumn>
                                            <GridColumn HeaderText="Actions"
                                                        Width="15"
                                                        TextAlign="TextAlign.Center"
                                                        HeaderTextAlign="TextAlign.Center">
                                                <GridCommandColumns>
                                                    <GridCommandColumn Type="CommandButtonType.Edit"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-edit", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Delete"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Save"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat"})" />
                                                    <GridCommandColumn Type="CommandButtonType.Cancel"
                                                                       ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat"})" />
                                                </GridCommandColumns>
                                            </GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                }
                                else
                                {
                                    <p class="no-icd-message">No ICD codes added yet.</p>
                                }
                            </div>
                        </div>

                        <!-- Remarks Section -->
                        <div class="remarks-section">
                            <h3 class="subsection-title">Remarks for unusual services</h3>
                            <SfTextBox @bind-Value="@Remarks" Multiline="true" CssClass="remarks-textarea"></SfTextBox>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>



            <TabItem>
                <ChildContent>
                    <TabHeader Text="Billing Dentist"></TabHeader>
                </ChildContent>
                <ContentTemplate>
                    <div class="form-section">
                        <h2 class="section-title">
                            DENTIST INFO
                        </h2>
                        <div class="dentist-info-grid">
                            <div class="facility-dentist-container">
                                <div class="form-group">
                                    <label class="form-label">Facility Info</label>
                                    <SfTextBox @bind-Value="@DentalClaims.FacilityInfo" CssClass="form-control"></SfTextBox>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Dentist Info</label>
                                    <SfTextBox @bind-Value="@DentalClaims.DentistInfo" CssClass="form-control"></SfTextBox>
                                </div>
                                <div class="form-group" style="display: flex; align-items: baseline; gap: 20px;">
                                    <label class="form-label" style="margin: 0; min-width: 140px; font-size: 14px; font-weight: 600;">Place Of Treatment</label>
                                    <div class="radio-group" style="display: flex; gap: 20px; align-items: center; margin-top: 0;">
                                        <SfRadioButton Label="Office" Name="treatment" Value="Office" @bind-Checked="@DentalClaims.PlaceOfTreatment" CssClass="custom-radio" style="margin: 0;"></SfRadioButton>
                                        <SfRadioButton Label="Hosp" Name="treatment" Value="Hosp" @bind-Checked="@DentalClaims.PlaceOfTreatment" CssClass="custom-radio" style="margin: 0;"></SfRadioButton>
                                        <SfRadioButton Label="ECF" Name="treatment" Value="ECF" @bind-Checked="@DentalClaims.PlaceOfTreatment" CssClass="custom-radio" style="margin: 0;"></SfRadioButton>
                                        <SfRadioButton Label="Others" Name="treatment" Value="Others" @bind-Checked="@DentalClaims.PlaceOfTreatment" CssClass="custom-radio" style="margin: 0;"></SfRadioButton>
                                    </div>
                                </div>

                                <div class="form-group radiographs-section">
                                    <SfCheckBox Label="Radiographs or models enclosed"
                                                Checked="@DentalClaims.HasRadiographs"
                                                CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnRadiographsChanged))"
                                                CssClass="custom-checkbox">
                                    </SfCheckBox>
                                    <SfTextBox @bind-Value="@DentalClaims.RadiographsDetails"
                                               CssClass="radiographs-textbox"
                                               Enabled="@DentalClaims.HasRadiographs">
                                    </SfTextBox>
                                </div>
                            </div>

                            <div class="checkbox-container">
                                <div class="accident-checkboxes-section">
                                    <div class="checkbox-with-textbox">
                                        <SfCheckBox Label="Occupational Injury" Checked="@DentalClaims.IsOccupationalInjury"
                                                    CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnOccupationalInjuryChanged))"
                                                    CssClass="custom-checkbox">
                                        </SfCheckBox>
                                        <SfTextBox @bind-Value="@DentalClaims.OccupationalInjuryDetails"
                                                   CssClass="form-control accident-textbox"
                                                   Enabled="@DentalClaims.IsOccupationalInjury"></SfTextBox>
                                    </div>
                                    <div class="checkbox-with-textbox">
                                        <SfCheckBox Label="Auto Accident" Checked="@DentalClaims.IsAutoAccident"
                                                    CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnAutoAccidentChanged))"
                                                    CssClass="custom-checkbox">
                                        </SfCheckBox>
                                        <SfTextBox @bind-Value="@DentalClaims.AutoAccidentDetails"
                                                   CssClass="form-control accident-textbox"
                                                   Enabled="@DentalClaims.IsAutoAccident"></SfTextBox>
                                    </div>
                                    <div class="checkbox-with-textbox">
                                        <SfCheckBox Label="Other Accident" Checked="@DentalClaims.IsOtherAccident"
                                                    CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnOtherAccidentChanged))"
                                                    CssClass="custom-checkbox">
                                        </SfCheckBox>
                                        <SfTextBox @bind-Value="@DentalClaims.OtherAccidentDetails"
                                                   CssClass="form-control accident-textbox"
                                                   Enabled="@DentalClaims.IsOtherAccident"></SfTextBox>
                                    </div>
                                </div>


                                <div class="prosthetics-section">
                                    <SfCheckBox Label="Prosthesis (Initial Placement)"
                                                Checked="@DentalClaims.IsProsthesis"
                                                CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnProsthesisChanged))"
                                                CssClass="custom-checkbox">
                                    </SfCheckBox>
                                    <div class="form-row">
                                        <label class="form-label">@Localizer["Replacement Reason"]</label>
                                        <SfTextBox @bind-Value="@DentalClaims.ReplacementReason"
                                                   CssClass="replacement-textbox"
                                                   Enabled="@DentalClaims.IsProsthesis">
                                        </SfTextBox>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">@Localizer["Prior Date"]</label>
                                        <SfDatePicker @bind-Value="@DentalClaims.PriorDate"
                                                      Format="MM-dd-yyyy"
                                                      CssClass="prior-date-picker"
                                                      Enabled="@DentalClaims.IsProsthesis">
                                        </SfDatePicker>
                                    </div>
                                </div>

                                <div class="orthodontics-section">
                                    <SfCheckBox Label="Orthodontics Treatment"
                                                Checked="@DentalClaims.OrthodonticsTreatment"
                                                CheckedChanged="@(EventCallback.Factory.Create<bool>(this, OnOrthodonticsTreatmentChanged))"
                                                CssClass="custom-checkbox">
                                    </SfCheckBox>
                                    <div class="form-row">
                                        <label class="form-label">@Localizer["Enter Service Commenced"]</label>
                                        <SfTextBox @bind-Value="@DentalClaims.OrthodonticsTreatmentServiceCommenced"
                                                   CssClass="service-commenced-textbox"
                                                   Enabled="@DentalClaims.OrthodonticsTreatment">
                                        </SfTextBox>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">@Localizer["Treatment Remaining"]</label>
                                        <SfTextBox @bind-Value="@DentalClaims.OrthodonticsTreatmentRemaining"
                                                   CssClass="treatment-remaining-textbox"
                                                   Enabled="@DentalClaims.OrthodonticsTreatment">
                                        </SfTextBox>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">@Localizer["Date"]</label>
                                        <SfDatePicker @bind-Value="@DentalClaims.OrthodonticsTreatmentDate"
                                                      Format="MM-dd-yyyy"
                                                      CssClass="treatment-date-picker"
                                                      Enabled="@DentalClaims.OrthodonticsTreatment">
                                        </SfDatePicker>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </TabItem>
        </TabItems>
    </SfTab>



    <div class="form-actions">
        <SfButton CssClass="btn-primary" @onclick="OnSubmit">
            <span class="btn-icon">✓</span>
            Submit Claim
        </SfButton>
        <SfButton CssClass="btn-secondary" @onclick="OnCancel">
            <span class="btn-icon">✕</span>
            Cancel
        </SfButton>
    </div>
</div>

<style>
    :root {
        --primary-color: #3a7bd5;
        --primary-dark: #2c5fb3;
        --secondary-color: #00d2ff;
        --accent-color: #ff7e5f;
        --light-gray: #f8f9fa;
        --medium-gray: #e9ecef;
        --dark-gray: #495057;
        --text-color: #343a40;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
    }

    /* Fix Prior Authorization input width */
    .prior-auth-input {
        width: 140px !important;
        height: 35px !important;
        flex-shrink: 0;
        padding: 6px 10px;
        line-height: 23px !important; /* Set specific line-height to match available space */
        vertical-align: middle;
        box-sizing: border-box;
        display: flex !important;
        align-items: center !important;
    }

    /* Updated status row to accommodate all items in one row */
    .status-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 2rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }

    .status-dropdown-container,
    .status-checkbox-container,
    .copay-container,
    .uncovered-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .copay-input {
        width: 120px;
    }

    /* Base styles */
    .dental-claim-form {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        color: var(--text-color);
    }

    /* Header styles */
    .form-header {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-title {
        color: var(--primary-color);
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        letter-spacing: -0.5px;
    }

    .header-accent {
        height: 4px;
        width: 80px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
    }

    /* Card styles */
    .claim-header-card {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--light-gray);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Checkbox vertical alignment */
    .checkboxes-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .checkbox-vertical-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    /* Add to your existing CSS */
    .subsection-title {
        color: var(--primary-dark);
        font-size: 1rem;
        margin: 1.5rem 0 0.75rem 0;
        border-bottom: 1px solid var(--medium-gray);
        padding-bottom: 0.5rem;
    }

    .service-rendered-section,
    .dos-section,
    .billing-dentist-section,
    .icd-section,
    .remarks-section {
        margin-bottom: 1.5rem;
    }

    .total-charges-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 1rem;
        gap: 1rem;
    }

    .total-label {
        font-weight: 600;
    }

    .total-input {
        width: 120px;
    }

    .dentist-controls,
    .icd-controls {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .grid-actions {
        display: flex;
        justify-content: center;
    }

    .remarks-textarea {
        width: 100%;
        min-height: 80px;
    }

    .e-grid .e-rowcell {
        padding: 8px !important;
    }
    /* Section styles */
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        border: 1px solid var(--medium-gray);
    }

    .section-title {
        color: var(--primary-dark);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        font-size: 1.5rem;
    }

    /* Form control styles */
    .form-group {
        margin-bottom: 1.25rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--dark-gray);
        font-size: 0.9rem;
    }

    .form-control {
        width: 100%; /* Changed back to 100% for proper visibility */
        min-height: 35px !important; /* Use min-height instead of height */
        padding: 0.75rem;
        border: 1px solid var(--medium-gray);
        border-radius: 6px;
        font-size: 0.95rem;
        transition: all 0.2s ease;
    }

    .claim-header-card .e-datepicker {
        width: 180px !important;
    }

        .claim-header-card .e-datepicker .e-input-group {
            width: 100% !important;
            height: 35px !important;
        }

            .claim-header-card .e-datepicker .e-input-group .e-input {
                height: 33px !important;
                padding: 0 0.75rem !important;
                font-size: 0.9rem !important;
            }

    .form-control:hover {
        border-color: var(--primary-color);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
    }

    .textarea-control {
        min-height: 80px;
        resize: vertical;
    }

    /* Grid layouts */
    .patient-info-grid, .dentist-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 12rem;
    }

    /* Checkbox and radio styles */
    .checkbox-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .custom-checkbox .e-checkbox-wrapper {
        padding: 0.5rem 0;
    }

        .custom-checkbox .e-checkbox-wrapper:hover .e-frame {
            border-color: var(--primary-color);
        }

        .custom-checkbox .e-checkbox-wrapper .e-frame.e-check {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

    .radio-group {
        display: flex;
        gap: 1.5rem;
        margin-top: 0.5rem;
    }

    .custom-radio .e-radio-wrapper {
        padding: 0.5rem 0;
    }

        .custom-radio .e-radio-wrapper:hover .e-radio.e-small .e-outer-circle {
            border-color: var(--primary-color);
        }

        .custom-radio .e-radio-wrapper .e-radio.e-small.e-checked .e-inner-circle {
            background-color: var(--primary-color);
        }

        .custom-radio .e-radio-wrapper .e-radio.e-small.e-checked .e-outer-circle {
            border-color: var(--primary-color);
        }

    /* Tab styles */
    .custom-tabs .e-tab-header {
        border-bottom: 2px solid var(--medium-gray);
        margin-bottom: 1.5rem;
    }

        .custom-tabs .e-tab-header .e-toolbar-item {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }

            .custom-tabs .e-tab-header .e-toolbar-item.e-active {
                color: var(--primary-color);
            }

            .custom-tabs .e-tab-header .e-toolbar-item:hover:not(.e-active) {
                color: var(--primary-dark);
            }

            .custom-tabs .e-tab-header .e-toolbar-item.e-active .e-tab-text {
                position: relative;
            }

                .custom-tabs .e-tab-header .e-toolbar-item.e-active .e-tab-text::after {
                    content: '';
                    position: absolute;
                    bottom: -12px;
                    left: 0;
                    width: 100%;
                    height: 3px;
                    background: var(--primary-color);
                    border-radius: 3px 3px 0 0;
                }

    /* Grid styles */
    .insurance-grid-container {
        border: 1px solid var(--medium-gray);
        border-radius: 8px;
        overflow: hidden;
    }

    .custom-grid .e-gridheader {
        background: var(--light-gray);
        border-bottom: 2px solid var(--medium-gray);
    }

        .custom-grid .e-gridheader .e-headercell {
            padding: 0.75rem;
            font-weight: 600;
            color: var(--dark-gray);
        }

    .custom-grid .e-row {
        border-bottom: 1px solid var(--medium-gray);
    }

        .custom-grid .e-row:hover {
            background: rgba(58, 123, 213, 0.05);
        }

    .custom-grid .e-rowcell {
        padding: 0.75rem;
    }

    .facility-dentist-container {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
    }

        .facility-dentist-container .form-control {
            width: 125% !important;
            min-height: 130px !important;
            padding: 0.75rem !important;
            font-size: 1rem !important;
        }

    /* More specific selector for Syncfusion TextBox */
    .auto-expanding-textarea.e-input-group,
    .auto-expanding-textarea .e-input-group {
        min-height: 120px !important;
        height: auto !important;
        width: 110% !important; /* or specify a specific width like 500px */
    }

        .auto-expanding-textarea.e-input-group .e-input,
        .auto-expanding-textarea .e-input-group .e-input {
            min-height: 120px !important;
            height: auto !important;
            width: 110% !important; /* or specify a specific width like 500px */
        }

        /* Target the actual textarea element inside Syncfusion TextBox */
        .auto-expanding-textarea textarea,
        .auto-expanding-textarea .e-input-group textarea,
        .auto-expanding-textarea .e-input-group .e-input {
            min-height: 120px !important;
            height: 120px !important;
            width: 110% !important; /* or specify a specific width like 500px */
        }
    /* Add this to your existing style section */
    .auto-expanding-textarea {
        /* Container styles */
        .e-input-group

    {
        height: auto !important; /* Override Syncfusion's fixed height */
        min-height: 200px; /* Initial minimum height */
    }

    /* Textarea styles */
    .e-input-group textarea {
        min-height: 200px; /* Initial height */
        max-height: 700px; /* Maximum height before scrolling (adjust as needed) */
        overflow-y: hidden !important; /* Hide scrollbar */
        resize: none; /* Disable manual resize */
        transition: height 0.2s ease; /* Smooth expansion */
        line-height: 1.5; /* Better text spacing */
        padding: 12px !important; /* Better padding */
    }

    /* Focus state */
    .e-input-group.e-control-wrapper.e-input-focus textarea {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
    }

    }
    /* Loading indicator */
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(58, 123, 213, 0.2);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    .loading-text {
        color: var(--dark-gray);
        font-weight: 500;
    }

    /* Button styles */
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-primary, .btn-secondary {
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
    }

    .btn-primary {
        background-color: var(--primary-color);
        color: white;
        border: none;
    }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(58, 123, 213, 0.3);
        }

    .btn-secondary {
        background-color: white;
        color: var(--dark-gray);
        border: 1px solid var(--medium-gray);
    }

        .btn-secondary:hover {
            background-color: var(--light-gray);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

    .btn-icon {
        font-size: 1rem;
    }

    /* Animations */
    @@keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .claim-header-card {
            flex-direction: column;
            gap: 1rem;
        }

        .radio-group {
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-actions {
            flex-direction: column;
        }
    }

    .claim-status-section {
        margin-top: 2rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        border: 1px solid var(--medium-gray);
        padding: 1.5rem;
    }

    .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .status-tabs {
        display: flex;
        gap: 0.5rem;
    }

    .status-tab {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        background: none;
        border: 1px solid var(--medium-gray);
        color: var(--dark-gray);
        font-weight: 500;
    }

        .status-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

    .status-row {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }

    .status-label {
        font-weight: 500;
        color: var(--dark-gray);
        white-space: nowrap;
    }

    .status-value {
        font-weight: 600;
        margin-right: 1rem;
    }

    .status-checkbox {
        margin-right: 1rem;
    }

    .amounts-grid {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;
    }

    .amounts-column {
        flex: 1;
    }

    .amount-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--medium-gray);
    }

    .amount-label {
        font-weight: 500;
        width: 70px; /* Fixed width for field labels only */
        text-align: left;
        flex-shrink: 0; /* Prevent label from shrinking */
    }

    .amount-header {
        font-weight: 600;
        font-size: 1.1em;
        text-align: center;
        width: 100%;
    }

    .amount-input {
        width: 450px !important;
        flex-shrink: 0; /* Prevent input from shrinking */
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .print-btn {
        background: var(--light-gray);
        border: 1px solid var(--medium-gray);
    }

    .ok-btn {
        background: var(--success-color);
        color: white;
        border: 1px solid var(--success-color);
    }

    .cancel-btn {
        background: var(--danger-color);
        color: white;
        border: 1px solid var(--danger-color);
    }

    .adjustments-btn {
        background: var(--warning-color);
        color: var(--text-color);
        border: 1px solid var(--warning-color);
    }

    .convert-btn {
        background: var(--primary-color);
        color: white;
        border: 1px solid var(--primary-color);
    }

    /* Replace the existing .checkbox-with-textbox and .accident-textbox styles with these: */

    .checkbox-with-textbox {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        min-height: 30px; /* Ensure consistent row height */
    }

    /* Set consistent width for all checkbox labels in accident section */
    .accident-checkboxes-section .checkbox-with-textbox .custom-checkbox {
        min-width: 160px; /* Fixed width to accommodate longest label */
        flex-shrink: 0;
    }

        .accident-checkboxes-section .checkbox-with-textbox .custom-checkbox .e-checkbox-wrapper {
            min-width: 180px;
            display: flex;
            align-items: center;
        }

    .accident-checkboxes-section {
        display: flex;
        flex-direction: column;
        gap: 1.5rem; /* Increased from default to 1.5rem for more space between accident fields */
    }

    /* Update accident-textbox to increase gap and maintain consistent width */
    .accident-textbox {
        width: 275px !important;
        height: 35px !important;
        flex-shrink: 0;
        padding: 6px 10px;
        line-height: 18px !important;
        vertical-align: middle;
        box-sizing: border-box;
        display: flex !important;
        align-items: center !important;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 0.9rem;
        margin-left: 1rem; /* Add margin to create more gap from checkboxes */
    }

    .prior-auth-section {
        margin-bottom: 2.0rem !important;
    }

    .radiographs-section {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        margin-top: 15px;
        margin-bottom: 1.5rem;
    }

        /* Custom checkbox styling */
        .radiographs-section .custom-checkbox {
            margin-right: 10px;
            min-width: fit-content;
            flex-shrink: 0;
        }

            .radiographs-section .custom-checkbox label {
                font-weight: 500;
                color: #333;
                cursor: pointer;
                user-select: none;
                font-size: 0.9rem;
            }

            .radiographs-section .custom-checkbox .e-checkbox-wrapper {
                display: flex;
                align-items: center;
                gap: 8px;
            }

        /* Radiographs textbox styling - More specific to override form-control */
        .radiographs-section .radiographs-textbox {
            width: 295px !important;
            height: 35px !important;
        }

            .radiographs-section .radiographs-textbox .e-input-group {
                width: 200px !important;
                height: 35px !important;
                border: 1px solid #ccc !important;
                border-radius: 4px !important;
            }

                .radiographs-section .radiographs-textbox .e-input-group .e-input {
                    height: 33px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    border: none !important;
                    outline: none !important;
                }

            .radiographs-section .radiographs-textbox:enabled {
                background-color: #fff !important;
            }

            .radiographs-section .radiographs-textbox:disabled,
            .radiographs-section .radiographs-textbox.e-disabled .e-input-group {
                background-color: #f5f5f5 !important;
                border-color: #ddd !important;
            }

                .radiographs-section .radiographs-textbox.e-disabled .e-input-group .e-input {
                    background-color: #f5f5f5 !important;
                    color: #999 !important;
                    cursor: not-allowed;
                }

            .radiographs-section .radiographs-textbox .e-input-group.e-input-focus {
                border-color: var(--primary-color, #007bff) !important;
                box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2) !important;
            }

    .orthodontics-section {
        margin-top: 12px;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

        .orthodontics-section .form-row {
            display: flex;
            align-items: center;
            gap: 0.25rem; /* Changed from 0.75rem to 0.25rem for closer spacing */
        }

        .orthodontics-section .form-label {
            width: 180px;
            flex-shrink: 0;
            margin: 0;
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
        }

        /* Service Commenced TextBox */
        .orthodontics-section .service-commenced-textbox {
            width: 295px;
            height: 35px;
        }

            .orthodontics-section .service-commenced-textbox .e-input-group {
                width: 200px !important;
                height: 35px !important;
                border: 1px solid #ccc !important;
                border-radius: 4px !important;
            }

                .orthodontics-section .service-commenced-textbox .e-input-group .e-input {
                    height: 33px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    border: none !important;
                    outline: none !important;
                }

        /* Treatment Remaining TextBox */
        .orthodontics-section .treatment-remaining-textbox {
            width: 295px;
            height: 35px;
        }

            .orthodontics-section .treatment-remaining-textbox .e-input-group {
                width: 200px !important;
                height: 35px !important;
                border: 1px solid #ccc !important;
                border-radius: 4px !important;
            }

                .orthodontics-section .treatment-remaining-textbox .e-input-group .e-input {
                    height: 33px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    border: none !important;
                    outline: none !important;
                }

        /* Treatment Date Picker */
        .orthodontics-section .treatment-date-picker {
            width: 150px;
        }

            .orthodontics-section .treatment-date-picker .e-input-group {
                width: 180px !important;
                height: 35px !important;
                border: 1px solid #ccc !important;
                border-radius: 4px !important;
            }

                .orthodontics-section .treatment-date-picker .e-input-group .e-input {
                    height: 33px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    border: none !important;
                    outline: none !important;
                }

                /* Focus States */
                .orthodontics-section .service-commenced-textbox .e-input-group.e-input-focus,
                .orthodontics-section .treatment-remaining-textbox .e-input-group.e-input-focus,
                .orthodontics-section .treatment-date-picker .e-input-group.e-input-focus {
                    border-color: var(--primary-color, #007bff) !important;
                    box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2) !important;
                }

            /* Disabled States */
            .orthodontics-section .service-commenced-textbox.e-disabled .e-input-group,
            .orthodontics-section .treatment-remaining-textbox.e-disabled .e-input-group,
            .orthodontics-section .treatment-date-picker.e-disabled .e-input-group {
                background-color: #f5f5f5 !important;
                border-color: #ddd !important;
            }

                .orthodontics-section .service-commenced-textbox.e-disabled .e-input-group .e-input,
                .orthodontics-section .treatment-remaining-textbox.e-disabled .e-input-group .e-input,
                .orthodontics-section .treatment-date-picker.e-disabled .e-input-group .e-input {
                    background-color: #f5f5f5 !important;
                    color: #999 !important;
                    cursor: not-allowed;
                }

        /* Checkbox Styling */
        .orthodontics-section .custom-checkbox {
            margin-bottom: 10px;
        }

            .orthodontics-section .custom-checkbox .e-checkbox-wrapper {
                display: flex;
                align-items: center;
                gap: 8px;
            }

    .prosthetics-section {
        margin-top: 30px;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

        .prosthetics-section .form-row {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .prosthetics-section .form-label {
            width: 175px;
            flex-shrink: 0;
            margin: 0;
            font-weight: 500;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        /* Replacement Reason TextBox */
        .prosthetics-section .replacement-textbox {
            width: 275px;
            height: 35px;
        }

            .prosthetics-section .replacement-textbox .e-input-group {
                width: 200px !important;
                height: 35px !important;
                border: 1px solid #ccc !important;
                border-radius: 4px !important;
            }

                .prosthetics-section .replacement-textbox .e-input-group .e-input {
                    height: 30px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    border: none !important;
                    outline: none !important;
                }

        /* Prior Date Picker */
        .prosthetics-section .prior-date-picker {
            width: 150px;
        }

            .prosthetics-section .prior-date-picker .e-input-group {
                width: 180px !important;
                height: 32px !important;
                border: 1px solid #ccc !important;
                border-radius: 4px !important;
            }

                .prosthetics-section .prior-date-picker .e-input-group .e-input {
                    height: 30px !important;
                    padding: 0 0.75rem !important;
                    font-size: 0.9rem !important;
                    border: none !important;
                    outline: none !important;
                }

                /* Focus States */
                .prosthetics-section .replacement-textbox .e-input-group.e-input-focus,
                .prosthetics-section .prior-date-picker .e-input-group.e-input-focus {
                    border-color: var(--primary-color, #007bff) !important;
                    box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2) !important;
                }

            .prosthetics-section .replacement-textbox.e-disabled .e-input-group,
            .prosthetics-section .prior-date-picker.e-disabled .e-input-group {
                background-color: #dee2e6 !important;
                border-color: #adb5bd !important;
            }

                .prosthetics-section .replacement-textbox.e-disabled .e-input-group .e-input,
                .prosthetics-section .prior-date-picker.e-disabled .e-input-group .e-input {
                    background-color: #dee2e6 !important;
                    color: #495057 !important;
                    cursor: not-allowed;
                }

        /* Fix TextBox input styling */
        .prosthetics-section .form-control {
            width: 315px !important;
            height: 35px !important;
            flex-shrink: 0;
            padding: 6px 10px !important;
            line-height: 23px !important;
            vertical-align: middle;
            box-sizing: border-box !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9rem;
        }

            /* Disabled state styling */
            .prosthetics-section .form-control:disabled,
            .prosthetics-section .e-input-group.e-disabled,
            .prosthetics-section .e-input-group.e-disabled input,
            .prosthetics-section .e-datepicker.e-disabled .e-input-group .e-input {
                background-color: #f5f5f5 !important;
                color: #999 !important;
                cursor: not-allowed;
            }

            /* Fix input focus states */
            .prosthetics-section .form-control:focus,
            .prosthetics-section .e-input-group.e-input-focus .e-input {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
            }

    */

    .prosthetics-section .e-datepicker {
        width: 180px !important;
    }

    .prosthetics-section .e-datepicker .e-input-group {
        width: 100% !important;
        height: 35px !important;
    }

        .prosthetics-section .e-datepicker .e-input-group .e-input {
            height: 33px !important;
            padding: 0 0.75rem !important;
            font-size: 0.9rem !important;
        }

    /* Disabled state for prior date when prosthesis is not checked */
    .prosthetics-section .e-datepicker.e-disabled .e-input-group {
        background-color: #f5f5f5 !important;
    }

        .prosthetics-section .e-datepicker.e-disabled .e-input-group .e-input {
            background-color: #f5f5f5 !important;
            color: #999 !important;
            cursor: not-allowed;
        }

    */
    /* Ensure checkbox doesn't shrink */
    .checkbox-with-textbox .custom-checkbox {
        min-width: 150px;
        flex-shrink: 0;
    }

    /* Optional: Make checkboxes have consistent width for better alignment */
    .checkbox-with-textbox .custom-checkbox .e-checkbox-wrapper {
        min-width: 150px;
    }

    @@media (max-width: 768px) {
        .amounts-grid {
            flex-direction: column;
            gap: 1rem;
        }

        .status-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .action-buttons {
            justify-content: center;
        }
    }
</style>