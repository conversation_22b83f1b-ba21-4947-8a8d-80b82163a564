﻿@page "/license"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "licenseAccessPolicy")]
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<GenericCard Heading="@Localizer["User License Management"]">
    <MudPaper Elevation="2">
        <MudGrid Spacing="0">
            <MudItem xs="12" sm="6" md="4" lg="3">
                <MudAutocomplete T="Organization"
                                 Label="Search Organization"
                                 ToStringFunc="@(org => org == null ? string.Empty : $"{ActiveOrganizations.IndexOf(org) + 1}. {org.OrganizationName}")"
                                 @bind-Value="SelectedOrgObject"
                                 ResetValueOnEmptyText="false"
                                 Dense="true"
                                 SearchFunc="@SearchOrganizations"
                                 CoerceText="true"
                                 Clearable="true"
                                 Required="true"
                                 Adornment="Adornment.Start"
                                 AdornmentIcon="@Icons.Material.Filled.Search">
                </MudAutocomplete>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <MudDivider />

    <SfGrid DataSource="@ActiveUserLicenses" Toolbar="@ToolBarItems" @ref="licenseGrid"
            AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both">
        <GridEvents OnActionBegin="OnActionBegin"
                    OnActionComplete="OnActionComplete" TValue="UserLicense" />
        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal" />
        <GridColumns>
            <GridColumn Field="Id" HeaderText="ID" IsPrimaryKey="true" Visible="false" Width="300" />

            <GridForeignColumn TValue="PlanType" Field="PlanId" HeaderText="Plan Name"
                               ForeignKeyField="Id" ForeignKeyValue="PlanName"
                               ForeignDataSource="PlanList" Width="150" />

            <GridColumn Field="OrganizationId" HeaderText="Organization Name" AllowEditing="false" Width="200">
                <Template>
                    @GetOrganizationName((context as UserLicense)?.OrganizationId)
                </Template>
                <EditTemplate>
                    <span>@GetOrganizationName((context as UserLicense)?.OrganizationId)</span>
                </EditTemplate>
            </GridColumn>

            <GridColumn Field="OrganizationId" HeaderText="Organization Email" AllowEditing="false" Width="200">
                <Template>
                    @GetOrganizationEmail((context as UserLicense)?.OrganizationId)
                </Template>
                <EditTemplate>
                    <span>@GetOrganizationEmail((context as UserLicense)?.OrganizationId)</span>
                </EditTemplate>
            </GridColumn>

            <GridForeignColumn TValue="Product" Field="ProductId" HeaderText="Product Name"
                               ForeignKeyField="Id" ForeignKeyValue="Name"
                               ForeignDataSource="ProductList" Width="150" />

            <GridColumn Field="Seats" HeaderText="Seats" Width="130" />
            <GridColumn Field="ActiveUsers" HeaderText="Active Users" Width="120" />
            <GridColumn Field="CreatedDate" HeaderText="Created Date" AllowEditing="false" Format="d" Width="130" />
            <GridColumn Field="UpdatedDate" HeaderText="Updated Date" AllowEditing="false" Format="d" Width="130" />

            <GridColumn Field="CreatedBy" HeaderText="Created By" AllowEditing="false" Width="200">
                <Template>
                    @GetUserDisplayName((context as UserLicense)?.CreatedBy)
                </Template>
                <EditTemplate>
                    <span>@GetUserDisplayName((context as UserLicense)?.CreatedBy)</span>
                </EditTemplate>
            </GridColumn>

            <GridColumn Field="UpdatedBy" HeaderText="Updated By" AllowEditing="false" Width="200">
                <Template>
                    @GetUserDisplayName((context as UserLicense)?.UpdatedBy)
                </Template>
                <EditTemplate>
                    <span>@GetUserDisplayName((context as UserLicense)?.UpdatedBy)</span>
                </EditTemplate>
            </GridColumn>

            <GridColumn Field="Status" HeaderText="Status" Width="100" AllowEditing="false">
                <Template>
                    @{
                        var statusText = (context as UserLicense)?.Status == true ? "Active" : "Inactive";
                        var statusClass = (context as UserLicense)?.Status == true ? "text-success" : "text-danger";
                    }
                    <span class="@statusClass">@statusText</span>
                </Template>
                <EditTemplate>
                    @{
                        var statusText = (context as UserLicense)?.Status == true ? "Active" : "Inactive";
                        var statusClass = (context as UserLicense)?.Status == true ? "text-success" : "text-danger";
                    }
                    <span class="@statusClass">@statusText</span>
                </EditTemplate>
            </GridColumn>

            <GridColumn Field="ExpiryDate" HeaderText="Expiry Date" Format="d" Width="120" />
        </GridColumns>
    </SfGrid>
</GenericCard>