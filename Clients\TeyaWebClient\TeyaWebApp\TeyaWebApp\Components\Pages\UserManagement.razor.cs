﻿using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class UserManagement : ComponentBase
    {
        [Inject] private IMemberService MemberService { get; set; } = default!;
        [Inject] private ILogger<UserManagement> Logger { get; set; } = default!;
        [Inject] private IRoleService RoleService { get; set; } = default!;
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;

        private SfGrid<Member>? UserGrid;
        private Member SelectedMember = new();
        private List<string> rolesList = new();
        private List<Role> roleModel = new();
        private List<Member> allMembers = new();
        private string SelectedRoleName { get; set; } = string.Empty;

        public string[] ToolBarItems = new string[] { "Edit" };

        public enum ExcludedRoles
        {
            Patient
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                rolesList = await GetAllRoleNamesAsync();
                await LoadMembersAsync();
                StateHasChanged();
                Logger.LogInformation(Localizer["UserManagementPageInitialized"]);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDuringInitialization"]);
            }
        }

        public async Task<List<string>> GetAllRoleNamesAsync()
        {
            var response = new List<string>();
            try
            {
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";
                var roles = await RoleService.GetAllRolesByOrgIdAsync(activeUserOrganizationId, Subscription);
                response = roles
                    .Where(role => role.IsActive &&
                          !string.Equals(role.RoleName, ExcludedRoles.Patient.ToString(), StringComparison.OrdinalIgnoreCase))
                    .Select(role => role.RoleName)
                    .ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingRoles"]);
                response = new List<string>();
            }
            return response;
        }

        private RenderFragment<object> RoleNameEditTemplate => (context) => (builder) =>
        {
            if (context is not Member member) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", rolesList);
            builder.AddAttribute(2, "Value", member.RoleName);
            builder.AddAttribute(3, "ValueChanged", EventCallback.Factory.Create<string>(this, value =>
            {
                member.RoleName = value;
                SelectedRoleName = value;
                StateHasChanged(); // Ensure UI updates

                Logger.LogInformation(Localizer["SelectedRole"], SelectedRoleName);
            }));
            builder.AddAttribute(4, "Placeholder", Localizer["SelectRole"]);
            builder.CloseComponent();
        };

        private async Task OnActionBegin(Syncfusion.Blazor.Grids.ActionEventArgs<Member> args)
        {
            try
            {
                Logger.LogInformation(Localizer["ActionBeginTriggered"], args.RequestType);

                if (args.RequestType == Syncfusion.Blazor.Grids.Action.BeginEdit)
                {
                    Logger.LogInformation(Localizer["EditingMember"], args.RowData?.Email);
                    args.Cancel = true;
                    await OpenEditDialogAsync(args.RowData);
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    Logger.LogInformation(Localizer["UpdatingMember"], args.Data?.Email);
                    await UpdateMemberInDatabase(args.Data);
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Logger.LogInformation(Localizer["DeletingMember"], args.Data?.Email);
                    await DeleteMemberFromDatabase(args.Data);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorInOnActionBegin"]);
            }
        }

        private void OnActionComplete(Syncfusion.Blazor.Grids.ActionEventArgs<Member> args)
        {
            Logger.LogInformation(Localizer["ActionCompleteTriggered"], args.RequestType);
        }

        private async Task UpdateMemberInDatabase(Member member)
        {
            try
            {
                if (Guid.TryParse(User.id, out Guid userGuid) && member.Id == userGuid)
                {
                    Snackbar.Add(Localizer["CannotUpdateYourself"], Severity.Error);
                }
                else
                {
                    var roleModel = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
                    roleModel = roleModel.Where(role => role.RoleName == SelectedRoleName).ToList();
                    member.RoleID = roleModel.FirstOrDefault()?.RoleId;
                    await MemberService.UpdateMemberByIdAsync(member.Id, member);
                    Logger.LogInformation(Localizer["MemberUpdatedSuccessfully"], member.Email);
                    await LoadMembersAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorUpdatingMember"]);
            }
        }

        private async Task DeleteMemberFromDatabase(Member member)
        {
            try
            {
                if (Guid.TryParse(User.id, out Guid userGuid) && member.Id == userGuid)
                {
                    Snackbar.Add(Localizer["CannotDeleteYourself"], Severity.Error);
                    await LoadMembersAsync();
                    UserGrid?.Refresh();
                }
                else
                {
                    await MemberService.DeleteMemberByIdAsync(member.Id, activeUserOrganizationId, Subscription);
                    Logger.LogInformation(Localizer["MemberDeletedSuccessfully"], member.Email);
                    string graphIdToDeleteUser = member.Id.ToString();
                    await customAuthenticationService.DeleteUserAsync(graphIdToDeleteUser);
                    await LoadMembersAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDeletingMember"]);
            }
        }

        // Update your existing OpenDialogAsync method (for creating new members)
        private async Task OpenDialogAsync()
        {
            try
            {
                var parameters = new DialogParameters
                {
                    ["OnMemberUpdated"] = EventCallback.Factory.Create(this, UpdateAllMembers)
                };

                await DialogService.ShowAsync<CreateUser>(Localizer["AddMember"],
                    parameters,
                    new DialogOptions { CloseOnEscapeKey = true, CloseButton = true });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        // Update your existing OpenEditDialogAsync method (for editing existing members)
        private async Task OpenEditDialogAsync(Member member)
        {
            try
            {
                var parameters = new DialogParameters
                {
                    ["Member"] = member,
                    ["OnMemberUpdated"] = EventCallback.Factory.Create(this, UpdateAllMembers)
                };

                await DialogService.ShowAsync<CreateUser>(Localizer["EditMember"],
                    parameters,
                    new DialogOptions { CloseOnEscapeKey = true, CloseButton = true });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        // Update your existing UpdateAllMembers method
        private async void UpdateAllMembers()
        {
            try
            {
                var allFetchedMembers = await MemberService.GetAllMembersAsync(activeUserOrganizationId, Subscription);
                allMembers = allFetchedMembers
                    .Where(member => member.OrganizationName == User?.OrganizationName)
                    .ToList();
                Logger.LogInformation(Localizer["LoadedMembers"], allMembers.Count);
                StateHasChanged(); // Refresh the UI
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorLoadingMembers"]);
            }
        }
        private async Task OnToolbarItemClicked(Syncfusion.Blazor.Navigations.ClickEventArgs args)
        {
            if (args.Item.Text == "Refresh")
            {
                await LoadMembersAsync();
                UserGrid?.Refresh();
            }
            else if (args.Item.Text == "Save")
            {
                await UserGrid?.EndEditAsync();
            }
        }


        private async Task LoadMembersAsync()
        {
            try
            {
                var allFetchedMembers = await MemberService.GetAllMembersAsync(activeUserOrganizationId, Subscription);
                allMembers = allFetchedMembers
                    .Where(member => member.OrganizationName == User?.OrganizationName && member.IsActive)
                    .ToList();
                Logger.LogInformation(Localizer["LoadedMembers"], allMembers.Count);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorLoadingMembers"]);
            }
        }

        private Task OnRefreshClick() => LoadMembersAsync();

    }
}
