﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Graph.Models;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class PastResults : ComponentBase
    {
        [Inject] public IPastResultService PastResultService { get; set; }
        
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private List<PastResult> pastresult { get; set; }
        private List<PastResult> AddList = new();
        private List<PastResult> DeleteList = new();
        public SfGrid<PastResult> PastResultsGrid { get; set; }
        public SfRichTextEditor RichTextEditor { get; set; }
        private MudDialog _pastResults;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
        private string editorContent;
        private bool add = false;


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent { get; set; }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" },
            new ToolbarItemModel() { Name = "close" }
        };

        /// <summary>
        /// Initializes the component asynchronously and loads patient past results data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            ManualContent = Data;
            OrgID = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            pastresult = await PastResultService.GetResultByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        /// <summary>
        /// Loads past results data asynchronously for the given patient.
        /// </summary>
        private async Task LoadDataAsync()
        {
            
            pastresult = await PastResultService.GetResultByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            
            await InvokeAsync(StateHasChanged);
        }




        /// <summary>
        /// Opens the add task dialog for adding new past result records.
        /// </summary>
        private async Task OpenNewDialogBox()
        {
            await _pastResults.ShowAsync();
        }

        /// <summary>
        /// Handles the completion of actions such as Add, Save, and Delete in the past result grid.
        /// </summary>
        /// <param name="args">Action event arguments containing the details of the operation.</param>
        private void ActionCompletedHandler(ActionEventArgs<PastResult> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedresults = args.Data as PastResult;
                    var existingItem = AddList.FirstOrDefault(s => s.ResultId == deletedresults.ResultId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedresults.UpdatedBy = Guid.Parse(User.id);
                        deletedresults.UpdatedDate = DateTime.Now;
                        deletedresults.IsActive = false;
                        DeleteList.Add(deletedresults);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.ResultId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = OrgID ?? Guid.Empty;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.IsActive = true;
                add = true;
            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<PastResult> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (add)
                    {
                        if (args.Data != null)
                        {
                            var addedhistories = args.Data;
                            if (addedhistories != null)
                            {
                                AddList.Add(addedhistories);
                            }
                        }
                        add = false;
                    }
                if (args.Data.CreatedDate.Date > DateTime.Now.Date)
                {
                    Snackbar.Add(@Localizer["Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (args.Data.ResultDate.HasValue && args.Data.OrderDate.HasValue && args.Data.OrderDate > args.Data.ResultDate)
                    {
                        Snackbar.Add(@Localizer["Order Date cannot be after Result Date"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }
                    if (!string.IsNullOrEmpty(args.Data.OrderBy) && !IsAlphabeticWithSpaces(args.Data.OrderBy))
                    {
                        Snackbar.Add(@Localizer["Order By Location field should only contain alphabets and spaces"], Severity.Warning);
                        args.Cancel = true;
                        return;
                    }
                    DateTime today = DateTime.Today;
                    if (args.Data.OrderDate < today)
                    {
                        Snackbar.Add("Order Date cannot be in the past.", Severity.Warning);
                        args.Cancel = true;
                        return;
                    }
                if (!string.IsNullOrEmpty(args.Data.ViewResults) && !IsAlphabeticWithSpaces(args.Data. ViewResults))
                {
                    Snackbar.Add(@Localizer["ViewResults Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.OrderType) && !IsAlphabeticWithSpaces(args.Data.OrderType))
                {
                    Snackbar.Add(@Localizer["OrderType Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.OrderName) && !IsAlphabeticWithSpaces(args.Data.OrderName))
                {
                    Snackbar.Add(@Localizer["OrderName Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                }
            }

        /// <summary>
        /// Saves the changes made to the past results records by adding new entries and updating existing ones.
        /// </summary>
        private async Task SaveData()
        {
            bool? saveResult = await DialogService.ShowMessageBox(
               Localizer["ConfirmSave"],
               Localizer["SaveMessage"],
               yesText: Localizer["Yes"],
               noText: Localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }

            if (AddList.Count != 0)
            {
                await PastResultService.AddResultAsync(AddList, OrgID, Subscription);
            }
            await PastResultService.UpdatePastResultListAsync(DeleteList, OrgID, Subscription);
            await PastResultService.UpdatePastResultListAsync(pastresult, OrgID, Subscription);
            await LoadDataAsync();
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);
            await HandleDynamicComponentUpdate();
            
            AddList.Clear();
            DeleteList.Clear();
            await _pastResults.CloseAsync();
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
        }

        /// <summary>
        /// Cancels changes and reloads the original past results data.
        /// </summary>
        private async Task CancelData()
        {
            await LoadDataAsync();
            editorContent = GenerateRichTextContent(ManualContent);
            await _pastResults.CloseAsync();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s/,.\\\(\)-]+$");
        }
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }


        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = pastresult != null
                ? string.Join(" ", pastresult
                    .Where(display => !string.IsNullOrEmpty(display.OrderName))
                    .Select(display => $@"<ul><li style='margin-left: 20px;'>
                    <b>Created Date:</b> {display.CreatedDate:MM-dd-yyyy}&emsp;
                    <b>Order Name:</b> {display.OrderName}&emsp;
                    <b>Order Date:</b> {(display.OrderDate?.ToString("MM-dd-yyyy") ?? "N/A")}&emsp;
                    <b>Ordered By:</b> {display.OrderBy}&emsp;
                    <b>Result Date:</b> {(display.ResultDate?.ToString("MM-dd-yyyy") ?? "N/A")}
                </li></ul>"))
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }


        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}