using Markdig.Helpers;
using Microsoft.AspNetCore.Components;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Sprache;
using Syncfusion.Blazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Assessments
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public IAssessmentsService _AssessmentsService { get; set; }
        [Inject] public IMemberService memberService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        //[Inject] IOrderSetService orderSetService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private ISnackbar Snackbar { get; set; }
        
        [Inject] private IChiefComplaintService ChiefComplaintService { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ILogger<Assessments> _logger { get; set; }
        [Inject] public ILabTestsService _labTestsService { get; set; }
        [Inject] public IImmunizationService _ImmunizationService { get; set; }
        [Inject] public ICurrentMedicationService CurrentMedicationService { get; set; }
        [Inject] public IProcedureService ProcedureService { get; set; }
        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        [Inject] private UserContext UserContext { get; set; }

        private List<ChiefComplaintDTO> chiefComplaints = new();
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        private List<string> chiefComplaintDescriptions = new List<string>();

        public string ICDName { get; set; }
        private bool showGrid = false;
        //private List<CompleteOrderSet> filteredOrderSets = new();
        private SfGrid<CompleteOrderSet> orderSetGrid { get; set; }

        public enum Source { FDB, CMS }
       
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __Assessments;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;
        private Guid? OrganizationID { get; set; }
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();

        private List<FDB_ICD> fdb_ICD { get; set; } = new List<FDB_ICD>();
        private string selectedDatabase = Source.CMS.ToString();

        public SfGrid<TeyaUIModels.Model.AssessmentsData> AssessmentsGrid { get; set; }

        private List<TeyaUIModels.Model.AssessmentsData> AddList = new();
        private List<TeyaUIModels.Model.AssessmentsData> _Assessments { get; set; }
        //private List<CompleteOrderSet> OrdersetList;

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        //[Parameter] public Guid OrdersetId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        public string? ManualContent {  get; set; }


        
        private List<TeyaUIModels.Model.AssessmentsData> deleteAssessmentslist { get; set; } = new List<TeyaUIModels.Model.AssessmentsData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" },
            new ToolbarItemModel() { Name = "close" },
         };

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>


        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            ManualContent = Data;
            PatientId = PatientID;
            OrganizationID = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            _Assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrganizationID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
            // Start Phase 2 in background without awaiting
            _ = LoadAssessmentDataAsync();
        }

        private async Task LoadAssessmentDataAsync()
        {
            LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrganizationID, Subscription))
                   .GroupBy(c => c.Description)
                   .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
                   .ToList();

            SharedNotesService.OnChange += UpdateComplaints;

            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();

        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();  
        }

        private void OnDatabaseChanged(string newDatabase)
        {
            selectedDatabase = newDatabase;
            ICDName = null; // Reset the drug name when database changes
            StateHasChanged(); // Ensure UI updates
        }

        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not AssessmentsData assessment) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
            builder.AddAttribute(2, "Value", assessment.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    assessment.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        assessment.CheifComplaintId = selectedComplaint.Id;
                        Console.WriteLine(assessment.CheifComplaintId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };
        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _Assessments
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])}  <br><strong>{Localizer["Diagnosis"]}:</strong> {s.Diagnosis} <br><strong>{Localizer["Specify"]}:</strong> {s.Specify}</p>"));
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>

        private async Task OpenNewDialogBox()
        {
            await __Assessments.ShowAsync();
        }

        
        /// <summary>
        /// Asks the GPT model a question and returns the response
        /// </summary>
        /// <param name="prompt">Question to ask the GPT model</param>
        /// <returns>GPT model response</returns>
        private async Task<string> AskGptModel(string prompt)
        {
            try
            {
                string systemMessage = "You are a medical assistant with expertise in pediatric and adult immunizations. " +
                                        "Provide concise, accurate clinical information about vaccines, their recommended age ranges, " +
                                        "and potential risks of administering vaccines outside their recommended age ranges.";

                return await MeasureService.AskGptAsync(systemMessage, prompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling GPT model: {Message}", ex.Message);
                return "Error determining age appropriateness.";
            }
        }
        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __Assessments.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private CancellationTokenSource _searchICDCancellationTokenSource;
        private CancellationTokenSource _searchFDBCancellationTokenSource;

        protected async Task<IEnumerable<string>> SearchICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<string>();

            if (selectedDatabase == Source.CMS.ToString())
            {
                return await SearchCMSICDCodes(searchTerm, cancellationToken);
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                return await SearchFDBICDCodes(searchTerm, cancellationToken);
            }

            return Enumerable.Empty<string>();
        }

        private async Task<IEnumerable<string>> SearchCMSICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            // Cancel previous search if still running
            _searchICDCancellationTokenSource?.Cancel();
            _searchICDCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine the external cancellation token with our local one
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchICDCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with the current search term
                var results = await _ICDService.GetAllICDCodesBySearchTermAsync(searchTerm);

                return results
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search CMS ICD codes");
                return Enumerable.Empty<string>();
            }
        }

        private async Task<IEnumerable<string>> SearchFDBICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            // Cancel previous search if still running
            _searchFDBCancellationTokenSource?.Cancel();
            _searchFDBCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine the external cancellation token with our local one
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchFDBCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with the current search term
                var results = await FDBService.GetICDBySearchTerm(searchTerm);

                return results
                    .Where(icd => icd.ICD_CD_TYPE == "06" || icd.ICD_CD_TYPE == "05") // Maintain your filter
                    .Select(icd => $"{icd.ICD_CD} - {icd.ICD_DESC ?? ""}")
                    .ToList();
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search FDB ICD codes");
                return Enumerable.Empty<string>();
            }
        }

        private async void OnAddClicked()
        {
            // TODO: Handle saving logic if needed
            var completeOrderSets = await orderSetGrid.GetSelectedRecordsAsync();
            foreach (var completeOrderSet in completeOrderSets)
            {
                // 1. Immunizations
                var immunizationList = completeOrderSet.orderSetImmunizationData?
                    .Select(x => new ImmunizationData
                    {
                        ImmunizationId = Guid.NewGuid(),
                        PatientId = PatientID,
                        OrganizationId = OrganizationID ?? Guid.Empty,
                        CreatedBy = Guid.Parse(User.id),
                        UpdatedBy = Guid.Parse(User.id),
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow,
                        Immunizations = x.Immunizations,
                        CPTCode = x.CPTCode,
                        CVXCode = x.CVXCode,
                        Comments = x.Comments,
                        CPTDescription = x.CPTDescription,
                        GivenDate = x.GivenDate,
                        PCPId = Guid.Parse(User.id),
                        IsActive = true,
                        Subscription = Subscription
                    }).ToList();

                if (immunizationList?.Any() == true)
                    await _ImmunizationService.AddImmunizationAsync(immunizationList, OrganizationID, Subscription);

                // 2. Procedures
                var procedureList = completeOrderSet.orderSetProcedures?
                    .Select(x => new Procedures
                    {
                        Id = Guid.NewGuid(),
                        PatientId = PatientID,
                        OrganizationId = OrganizationID ?? Guid.Empty,
                        CreatedByUserId = Guid.Parse(User.id),
                        UpdatedByUserId = Guid.Parse(User.id),
                        OrderDate = DateTime.UtcNow,
                        LastUpdatedDate = DateTime.UtcNow,
                        CPTCode = x.CPTCode,
                        Notes = x.Notes,
                        Description = x.Description,
                        AssessmentData = x.AssessmentData,
                        AssessmentId = x.AssessmentId ?? Guid.Empty,
                        ChiefComplaint = x.ChiefComplaint,
                        ChiefComplaintId = x.ChiefComplaintId,
                        PcpId = Guid.Parse(User.id),
                        OrderedBy = User.givenName + " " + User.surname,
                        IsDeleted = true,
                        Subscription = Subscription
                    }).ToList();

                if (procedureList?.Any() == true)
                    await ProcedureService.AddProcedureAsync(procedureList, OrganizationID, Subscription);

                // 3. Diagnostic Imaging
                var diagnosticImaging = completeOrderSet.orderSetDiagnosticImaging;
                if (diagnosticImaging != null)
                {
                    var diRecord = new DiagnosticImage
                    {
                        RecordID = Guid.NewGuid(),
                        PatientId = PatientID,
                        OrganizationID = OrganizationID,
                        CreatedBy = Guid.Parse(User.id),
                        UpdatedBy = Guid.Parse(User.id),
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow,
                        DiCompany = diagnosticImaging.DiCompany,
                        ccResults = diagnosticImaging.ccResults,
                        Type = diagnosticImaging.Type,
                        Lookup = diagnosticImaging.Lookup,
                        OrderName = diagnosticImaging.OrderName,
                        StartsWith = diagnosticImaging.StartsWith,
                        Subscription = Subscription,
                        IsActive = true,
                    };

                    await DiagnosticImagingService.CreateDiagnosticImagingAsync(new List<DiagnosticImage> { diRecord }, OrganizationID, Subscription);
                }

                // 4. Lab Tests
                var labsList = completeOrderSet.orderSetLabTests?
                    .Select(x => new LabTests
                    {
                        LabTestsId = Guid.NewGuid(),
                        PatientId = PatientID,
                        OrganizationId = OrganizationID ?? Guid.Empty,
                        PcpId = Guid.Parse(User.id),
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow,
                        LabTest1 = x.LabTest1,
                        LabTest2 = x.LabTest2,
                        TestOrganization = x.TestOrganization,
                        AssessmentData = x.AssessmentData,
                        AssessmentId = x.AssessmentId ?? Guid.Empty,
                        IsActive = true,
                    }).ToList();

                if (labsList?.Any() == true)
                    await _labTestsService.AddLabTestsAsync(labsList);

                // 5. Medications
                var medicationList = completeOrderSet.orderSetActiveMedication?
                    .Select(x => new ActiveMedication
                    {
                        MedicineId = Guid.NewGuid(),
                        PatientId = PatientID,
                        OrganizationId = OrganizationID ?? Guid.Empty,
                        CreatedBy = Guid.Parse(User.id),
                        UpdatedBy = Guid.Parse(User.id),
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow,
                        BrandName = x.BrandName,
                        DrugDetails = x.DrugDetails,
                        Quantity = x.Quantity,
                        Frequency = x.Frequency,
                        Route = x.Route,
                        Take = x.Take,
                        PCPId = Guid.Parse(User.id),
                        Strength = x.Strength,
                        StartDate = x.StartDate,
                        EndDate = x.EndDate,
                        isActive = true,
                        CheifComplaint = x.CheifComplaint,
                        CheifComplaintId = x.CheifComplaintId ?? Guid.Empty,
                        Subscription = Subscription
                    }).ToList();

                if (medicationList?.Any() == true)
                    await CurrentMedicationService.AddMedicationAsync(medicationList, OrganizationID, Subscription);
            }

            showGrid = false;
        }

        private void OnCancelClicked()
        {
            // Simply hide the grid
            showGrid = false;
        }
        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewDiagnosis()
        {
            var newDiagnosis = new TeyaUIModels.Model.AssessmentsData
            {
                AssessmentsID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrganizationID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Diagnosis = ICDName,
                IsActive = true,
            };

            //OrdersetList = (await orderSetService.GetAllOrderSetAsync()).ToList();

            //var matchingOrderSets = OrdersetList
            //                      .Where(x => !string.IsNullOrEmpty(x.orderSet?.Diagnosis) &&
            //                                  newDiagnosis.Diagnosis?.Contains(x.orderSet.Diagnosis, StringComparison.OrdinalIgnoreCase) == true)
            //                      .ToList();

            //filteredOrderSets = matchingOrderSets;

            //if(filteredOrderSets.Any())
            //{
            //    showGrid = true;
            //}
            //else
            //{
            //    showGrid = false;
            //}
            AddList.Add(newDiagnosis);
            _Assessments.Add(newDiagnosis);
            await AssessmentsGrid.Refresh();

            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            ICDName = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.AssessmentsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleleAssessments = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.AssessmentsID == deleleAssessments.AssessmentsID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    deleleAssessments.IsActive = false;
                    deleleAssessments.UpdatedDate = DateTime.Now;
                    deleteAssessmentslist.Add(deleleAssessments);
                }

            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<TeyaUIModels.Model.AssessmentsData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }
        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Any(assessment => string.IsNullOrWhiteSpace(assessment.CheifComplaint)))
            {
                Snackbar.Add("Each assessment must have a Chief Complaint.", Severity.Warning);
                return;
            }
            if (AddList.Count != 0)
            {
                await _AssessmentsService.AddAssessmentsAsync(AddList, OrganizationID, Subscription);
                SharedNotesService.AssessmentsChanged();
            }
            await _AssessmentsService.UpdateAssessmentsListAsync(_Assessments, OrganizationID, Subscription);
            await _AssessmentsService.UpdateAssessmentsListAsync(deleteAssessmentslist, OrganizationID, Subscription);
            deleteAssessmentslist.Clear();
            AddList.Clear();
            //UpdateEditorContent();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deleteAssessmentslist.Clear();
            AddList.Clear();
            _Assessments = await _AssessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrganizationID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }

        private string GenerateRichTextContent(string manualData)
        {
            string assessmentContent = _Assessments != null
                ? string.Join(" ", _Assessments
                    .Where(a => !string.IsNullOrEmpty(a.Diagnosis))
                    .OrderByDescending(a => a.CreatedDate)
                    .Select(a => $"<ul><li style='margin-left: 20px;'><b>{a.CreatedDate:yyyy-MM-dd}</b> : " +
                                 $"Diagnosis - {a.Diagnosis}, " +
                                 $"Specify - {a.Specify}</li></ul>"))
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {assessmentContent}
            </div>
            </div>";
        }



        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}