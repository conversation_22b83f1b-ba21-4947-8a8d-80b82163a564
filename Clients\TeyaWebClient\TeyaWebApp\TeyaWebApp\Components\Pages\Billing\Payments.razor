﻿@page "/BillingPayments"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.Services
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IOrganizationService OrganizationService
@inject IUserLicenseService UserLicenseService
@inject IPlanTypeService PlanTypeService
@inject IPaymentService PaymentService
@inject HttpClient Http
@layout Admin
@using Markdig

<GenericCard>
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4 my-gen-card">
        <div class="filter-container mb-3">
            <MudPaper Class="pa-2" Elevation="1">
                <div class="d-flex flex-wrap align-items-center gap-2">
                    <MudTextField @bind-Value="paymentFromFilter"
                                  Placeholder="Search By Name . . . "
                                  Label="Payment From"
                                  Variant="Variant.Outlined"
                                  Class="me-2"
                                  Immediate="true"
                                  Margin="Margin.Dense"
                                  Style=" height: 40px;" />

                    <MudTextField @bind-Value="postedByFilter"
                                  Placeholder="Search By Name . . . "
                                  Label="Posted By"
                                  Variant="Variant.Outlined"
                                  Class="me-2"
                                  Immediate="true"
                                  Margin="Margin.Dense"
                                  Style="height: 40px;" />
                    <MudTextField @bind-Value="paymentNoFilter"
                                  Placeholder="Search By Payment Number . . . "
                                  Label="Payment Number"
                                  Variant="Variant.Outlined"
                                  Class="me-2"
                                  Immediate="true"
                                  Margin="Margin.Dense"
                                  Style="height: 40px;" />
                    <MudTextField @bind-Value="checkNoFilter"
                                  Placeholder="Search By Check No . . . "
                                  Label="Check No"
                                  Variant="Variant.Outlined"
                                  Class="me-2"
                                  Immediate="true"
                                  Margin="Margin.Dense"
                                  Style="height: 40px;" />
                    
                    <MudDatePicker @bind-Date="checkDateFilter"
                                   Label="Check Date"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense" />
                    <MudDatePicker @bind-Date="dateFromFilter"
                                   Label="From Date"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense" />

                    <MudDatePicker @bind-Date="dateToFilter"
                                   Label="To Date"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense" />
                    <!-- Editable Records Filter -->
                    
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               Size="Size.Small"
                               Style="height:33px;"
                               OnClick="ResetFilters">
                        Reset
                    </MudButton>
                </div>
            </MudPaper>
        </div>

        <div class="grid-container">
            <SfGrid DataSource="@filteredPayments" AllowPaging="true" GridLines="GridLine.Both">
                <GridEvents TValue="PaymentsData"></GridEvents>

                <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="false"></GridEditSettings>
                <GridColumns>
                    <GridColumn Field=@nameof(PaymentsData.PaymentNo) HeaderText="PaymentNo" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">
                    </GridColumn>
                    <GridColumn Field=@nameof(PaymentsData.PostedBy) HeaderText="PostedBy" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">
                    </GridColumn>
                    <GridColumn Field=@nameof(PaymentsData.PostedDate) HeaderText="Date" Width="100" AllowEditing="false" TextAlign="TextAlign.Center"
                                Format="MMM dd, yyyy">
                    </GridColumn>
                   
                    <GridColumn Field=@nameof(PaymentsData.CheckNo) HeaderText="Patient" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">
                    </GridColumn>
                    <GridColumn Field=@nameof(PaymentsData.CheckDate) HeaderText="Patient" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </MudContainer>
</GenericCard>



<style>

    .my-gen-card {
        padding: 0px !important;
    }

    .mud-card-header {
        padding: 0 !important;
    }

    .mud-card-content {
        padding: 0px !important;
        padding-left: 2% !important;
    }

    /* Grid Rows */
    .e-row {
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
    }

        .e-row:hover {
            background-color: #f5faff;
            cursor: pointer;
        }

    .e-altrow {
        background-color: #fcfcfc;
    }
</style>
