﻿using System.Reflection.Emit;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Inputs;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static TeyaUIModels.Model.ProfessionalClaims;
using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;


namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class ProfessionalClaim : ComponentBase
    {
        private CompleteProfessionalClaims CompleteProfessionalClaims = new();
        private ProfessionalClaims ProfessionalClaims = new();
        private Member Patient = new();

        [Inject]
        private ActiveUser User { get; set; }

        [Inject]
        private IOrganizationService OrganizationService { get; set; }
        [Inject]
        private ISnackbar Snackbar { get; set; }
        [Inject] public IVaccineService VaccineService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        [Inject] private ILogger<Immunization> _logger { get; set; }
        [Inject] public IProfessionalClaimsService ProfessionalClaimsService { get; set; }

        private Member selectedPatient { get; set; }
        private string? Remarks { get; set; }

        private bool isLoading = true;
        private bool isSubmitting = false;
        private bool Subscription = false;
        private Guid OrganizationID { get; set; }
        private string selectedDatabase = Source.CMS.ToString();


        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        private List<FDB_ICD> fdb_ICD { get; set; } = new List<FDB_ICD>();
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public ICPTService _CPTService { get; set; }
        public List<ProfessionalClaimsICD> AddedICDCodes { get; set; } = new List<ProfessionalClaimsICD>();
        private List<ProfessionalClaimsImmunization> AddedVaccines = new List<ProfessionalClaimsImmunization>();
        private SfGrid<ProfessionalClaimsICD> ICDGrid;
        private SfGrid<ProfessionalClaimsImmunization> VaccineGrid;
        private string currentICDSelection = "";
        private string currentVaccineSelection = "";

        private bool isClaimHeaderDialogVisible = false;
        private bool isClaimDataDialogVisible = false;
        private DialogOptions dialogOptions = new()
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseOnEscapeKey = true,
            BackdropClick = false
        };


        private Dictionary<string, object> TempClaimHeaderData { get; set; } = new();
        private Dictionary<string, object> TempClaimData { get; set; } = new();
        private bool IsClaimHeaderSaved { get; set; } = false;
        private bool IsClaimDataSaved { get; set; } = false;

        private List<CPT> _cptCodes { get; set; } = new List<CPT>();
        private List<ProfessionalClaimsCPT> AddedCPTCodes = new List<ProfessionalClaimsCPT>();
        private SfGrid<ProfessionalClaimsCPT> CPTGrid;
        private string currentCPTSelection = "";
        // Claim header fields
        private string? ClaimNumber { get; set; }
        private DateTime? ClaimDate { get; set; } = DateTime.Today;
        private DateTime? ServiceDate { get; set; } = DateTime.Today;
        private string? AppointmentFacility { get; set; }
        private string? POS { get; set; }

        private int currentClaimNumber = 0;
        private int lastUsedClaimNumber = 0;
        private List<Insurance> InsuranceData = new();
        public List<ProfessionalClaimsPayments> PaymentsData { get; set; } = new List<ProfessionalClaimsPayments>();
        public SfGrid<ProfessionalClaimsPayments> PaymentsGrid;


        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrganizationID);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                _icdCodes = await _ICDService.GetAllICDCodesAsync();
                _cptCodes = await _CPTService.GetAllCPTCodesAsync();
                await LoadInsuranceData();

                InitializeCompleteProfessionalClaims();

                await GenerateClaimNumber();

                
               await LoadClaimData();
                InitializeTempData();

                isLoading = false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during initialization: {ex.Message}");
                //Snackbar?.Add("Error loading page data", Severity.Error);
                isLoading = false;
            }
        }

        private void InitializeTempData()
        {
            TempClaimHeaderData = new Dictionary<string, object>();
            TempClaimData = new Dictionary<string, object>();
            IsClaimHeaderSaved = false;
            IsClaimDataSaved = false;
        }

        // Modified SaveClaimHeaderData method
        private async Task SaveClaimHeaderData()
        {
            try
            {
                // Temporarily save the claim header data in dictionary
                TempClaimHeaderData["ResidentType"] = ProfessionalClaims.ResidentType;
                TempClaimHeaderData["StudentStatus"] = ProfessionalClaims.StudentStatus;
                TempClaimHeaderData["EmploymentStatus"] = ProfessionalClaims.EmploymentStatus;
                TempClaimHeaderData["HealthyKidServiceYes"] = ProfessionalClaims.HealthyKidServiceYes;
                TempClaimHeaderData["HealthyKidServiceNo"] = ProfessionalClaims.HealthyKidServiceNo;
                TempClaimHeaderData["FamilyPlanningYes"] = ProfessionalClaims.FamilyPlanningYes;
                TempClaimHeaderData["FamilyPlanningNo"] = ProfessionalClaims.FamilyPlanningNo;
                TempClaimHeaderData["SterilizationAbortionYes"] = ProfessionalClaims.SterilizationAbortionYes;
                TempClaimHeaderData["SterilizationAbortionNo"] = ProfessionalClaims.SterilizationAbortionNo;
                TempClaimHeaderData["ClaimEditingIndicator"] = ProfessionalClaims.ClaimEditingIndicator;
                TempClaimHeaderData["ClaimType"] = ProfessionalClaims.ClaimType;
                TempClaimHeaderData["FacilityLabId"] = ProfessionalClaims.FacilityLabId;
                TempClaimHeaderData["FacilityType"] = ProfessionalClaims.FacilityType;
                TempClaimHeaderData["IsResubmittal"] = ProfessionalClaims.IsResubmittal;
                TempClaimHeaderData["ResubmissionCode"] = ProfessionalClaims.ResubmissionCode;
                TempClaimHeaderData["ResubmissionRefNumber"] = ProfessionalClaims.ResubmissionRefNumber;
                TempClaimHeaderData["Hcfa10d"] = ProfessionalClaims.Hcfa10d;
                TempClaimHeaderData["Hcfa19"] = ProfessionalClaims.Hcfa19;
                TempClaimHeaderData["ClaimData"] = ProfessionalClaims.ClaimData;
                TempClaimHeaderData["ProviderAssignmentIndicator"] = ProfessionalClaims.ProviderAssignmentIndicator;
                TempClaimHeaderData["DelayReason"] = ProfessionalClaims.DelayReason;

                IsClaimHeaderSaved = true;

                Snackbar?.Add("Claim Header added Successfully", Severity.Info);
                CloseClaimHeaderDialog();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving claim header data: {ex.Message}");
                Snackbar?.Add($"Error saving claim header data: {ex.Message}", Severity.Error);
            }
        }

        // Modified SaveClaimData method
        private async Task SaveClaimData()
        {
            try
            {
                // Temporarily save the claim data in dictionary
                TempClaimData["EmploymentRelatedYes"] = ProfessionalClaims.EmploymentRelatedYes;
                TempClaimData["EmploymentRelatedNo"] = ProfessionalClaims.EmploymentRelatedNo;
                TempClaimData["AccidentAuto"] = ProfessionalClaims.AccidentAuto;
                TempClaimData["AccidentNonAuto"] = ProfessionalClaims.AccidentNonAuto;
                TempClaimData["AccidentNo"] = ProfessionalClaims.AccidentNo;
                TempClaimData["AccidentPlace"] = ProfessionalClaims.AccidentPlace;
                TempClaimData["AccidentHour"] = ProfessionalClaims.AccidentHour;
                TempClaimData["ExternalCauseOfAccident"] = ProfessionalClaims.ExternalCauseOfAccident;
                TempClaimData["ResponsibilityIndicator"] = ProfessionalClaims.ResponsibilityIndicator;
                TempClaimData["DocumentationIndicator"] = ProfessionalClaims.DocumentationIndicator;
                TempClaimData["DocumentationType"] = ProfessionalClaims.DocumentationType;
                TempClaimData["AttachmentControlNumber"] = ProfessionalClaims.AttachmentControlNumber;
                TempClaimData["DateDocumentationSent"] = ProfessionalClaims.DateDocumentationSent;
                TempClaimData["ReleaseOfInformation"] = ProfessionalClaims.ReleaseOfInformation;
                TempClaimData["SignatureDate"] = ProfessionalClaims.SignatureDate;
                TempClaimData["UnableToWorkFromDate"] = ProfessionalClaims.UnableToWorkFromDate;
                TempClaimData["UnableToWorkToDate"] = ProfessionalClaims.UnableToWorkToDate;
                TempClaimData["HospitalizationFromDate"] = ProfessionalClaims.HospitalizationFromDate;
                TempClaimData["HospitalizationToDate"] = ProfessionalClaims.HospitalizationToDate;
                TempClaimData["OutsideLabYes"] = ProfessionalClaims.OutsideLabYes;
                TempClaimData["OutsideLabNo"] = ProfessionalClaims.OutsideLabNo;
                TempClaimData["LabCharges"] = ProfessionalClaims.LabCharges;
                TempClaimData["Symptom"] = ProfessionalClaims.Symptom;
                TempClaimData["AccidentSymptomDate"] = ProfessionalClaims.AccidentSymptomDate;
                TempClaimData["DateLastSeen"] = ProfessionalClaims.DateLastSeen;
                TempClaimData["InitialTreatmentDate"] = ProfessionalClaims.InitialTreatmentDate;
                TempClaimData["SimilarSymptom"] = ProfessionalClaims.SimilarSymptom;
                TempClaimData["SimilarSymptomDate"] = ProfessionalClaims.SimilarSymptomDate;
                TempClaimData["SpecialProgramCode"] = ProfessionalClaims.SpecialProgramCode;
                TempClaimData["EPSDTReferralGiven"] = ProfessionalClaims.EPSDTReferralGiven;
                TempClaimData["EPSDTReferralCode"] = ProfessionalClaims.EPSDTReferralCode;
                TempClaimData["ReferringProviderANSI"] = ProfessionalClaims.ReferringProviderANSI;
                TempClaimData["ReferringProviderName"] = ProfessionalClaims.ReferringProviderName;
                TempClaimData["ReferringProviderID"] = ProfessionalClaims.ReferringProviderID;

                IsClaimDataSaved = true;

                Snackbar?.Add("Claim Data added Successfully", Severity.Info);
                CloseClaimDataDialog();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving claim data: {ex.Message}");
                Snackbar?.Add($"Error saving claim data: {ex.Message}", Severity.Error);
            }
        }
        private async Task LoadInsuranceDataForPatient(Member patient)
        {
            try
            {
                if (patient.InsuranceId.HasValue)
                {
                    var insurance = await InsuranceService.GetInsuranceByIdAsync(
                        patient.InsuranceId.Value,
                        OrganizationID,
                        Subscription);

                    if (insurance != null)
                    {
                        InsuranceData = new List<Insurance> { insurance };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading insurance data: {ex.Message}");
                Snackbar?.Add("Error loading insurance data", Severity.Warning);
            }
        }
        private async Task LoadInsuranceData()
        {
            try
            {
                if (Patient?.InsuranceId != null)
                {
                    var insurance = await InsuranceService.GetInsuranceByIdAsync(
                        Patient.InsuranceId.Value,
                        OrganizationID,
                        Subscription);

                    if (insurance != null)
                    {
                        InsuranceData = new List<Insurance> { insurance };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading insurance data: {ex.Message}");
            }
        }

        private void InitializeCompleteProfessionalClaims()
        {
            CompleteProfessionalClaims = new CompleteProfessionalClaims
            {
                Id = Guid.NewGuid(),
                professionalclaims = new ProfessionalClaims(),
                professionalClaimsCPT = new List<ProfessionalClaimsCPT>(),
                professionalClaimsICD = new List<ProfessionalClaimsICD>(),
                professionalClaimsPayments = new List<ProfessionalClaimsPayments>(),
                professionalClaimsImmunization = new List<ProfessionalClaimsImmunization>(),
                OrganizationID = OrganizationID,
                Subscription = Subscription
            };

            ProfessionalClaims = CompleteProfessionalClaims.professionalclaims;
            AddedICDCodes = CompleteProfessionalClaims.professionalClaimsICD;
            AddedVaccines = CompleteProfessionalClaims.professionalClaimsImmunization;
            AddedCPTCodes = CompleteProfessionalClaims.professionalClaimsCPT;
            PaymentsData = CompleteProfessionalClaims.professionalClaimsPayments;
        }

        private async Task LoadClaimData()
        {
            try
            {
                if (CompleteProfessionalClaims.Id != Guid.Empty)
                {
                    var existingClaims = await ProfessionalClaimsService.GetProfessionalClaimsByIdAsync(
                        CompleteProfessionalClaims.Id,
                        OrganizationID,
                        Subscription);

                    if (existingClaims != null && existingClaims.Any())
                    {
                        var existingClaim = existingClaims.First();
                        CompleteProfessionalClaims = existingClaim;
                        ProfessionalClaims = CompleteProfessionalClaims.professionalclaims ?? new ProfessionalClaims();

                        // Update local properties from loaded data
                        ClaimNumber = ProfessionalClaims.ClaimNumber;
                        ClaimDate = ProfessionalClaims.ClaimDate;
                        ServiceDate = ProfessionalClaims.ServiceDate;
                        AppointmentFacility = ProfessionalClaims.AppointmentFacility;
                        POS = ProfessionalClaims.POS;

                        StateHasChanged();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading claim data: {ex.Message}");
            }
        }

        private async Task GenerateClaimNumber()
        {
            if (string.IsNullOrEmpty(ClaimNumber))
            {
                try
                {
                    // Get the next claim number from the database
                    int nextClaimNumber = await GetNextClaimNumber();
                    ClaimNumber = nextClaimNumber.ToString();

                    Console.WriteLine($"Generated claim number: {ClaimNumber}");

                    // Update the ProfessionalClaims object immediately
                    ProfessionalClaims.ClaimNumber = ClaimNumber;
                    StateHasChanged(); // Trigger UI update
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error generating claim number: {ex.Message}");
                    // Fallback to session-based incrementing
                    int fallbackNumber = Math.Max(1, lastUsedClaimNumber + 1);
                    ClaimNumber = fallbackNumber.ToString();
                    Console.WriteLine($"Using fallback claim number: {ClaimNumber}");
                    ProfessionalClaims.ClaimNumber = ClaimNumber;
                    StateHasChanged();
                }
            }
        }

        private async Task<int> GetNextClaimNumber()
        {
            try
            {
                // Get existing claims for the current organization
                var existingClaims = await ProfessionalClaimsService.GetAllProfessionalClaimsAsync(OrganizationID, Subscription);

                if (existingClaims != null && existingClaims.Any())
                {
                    int maxClaimNumber = 0;

                    // Find max claim number for this organization
                    foreach (var claim in existingClaims)
                    {
                        if (claim.professionalclaims != null &&
                            !string.IsNullOrEmpty(claim.professionalclaims.ClaimNumber) &&
                            int.TryParse(claim.professionalclaims.ClaimNumber, out int claimNum))
                        {
                            if (claimNum > maxClaimNumber)
                            {
                                maxClaimNumber = claimNum;
                            }
                        }
                    }

                    // Increment from this organization's maximum claim number
                    int nextNumber = maxClaimNumber + 1;
                    Console.WriteLine($"Organization {OrganizationID} - Database max: {maxClaimNumber}, Next: {nextNumber}");

                    // Update session tracking for this organization
                    lastUsedClaimNumber = maxClaimNumber;

                    return nextNumber;
                }
                else
                {
                    // If no existing claims for this organization, start from 1
                    Console.WriteLine($"No existing claims for organization {OrganizationID}, starting from: 1");
                    lastUsedClaimNumber = 0;
                    return 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting next claim number for organization {OrganizationID}: {ex.Message}");

                try
                {
                    // Fallback: try to get max claim number for current organization
                    var existingClaims = await ProfessionalClaimsService.GetAllProfessionalClaimsAsync(OrganizationID, Subscription);
                    var maxFromDb = existingClaims?
                        .Where(c => c.professionalclaims != null &&
                                   !string.IsNullOrEmpty(c.professionalclaims.ClaimNumber))
                        .Select(c => int.TryParse(c.professionalclaims.ClaimNumber, out int num) ? num : 0)
                        .DefaultIfEmpty(0)
                        .Max() ?? 0;

                    Console.WriteLine($"Fallback for organization {OrganizationID}: max = {maxFromDb}, returning {maxFromDb + 1}");
                    return maxFromDb + 1;
                }
                catch
                {
                    // Ultimate fallback - start from 1 for new organization
                    Console.WriteLine($"Ultimate fallback for organization {OrganizationID}: starting from 1");
                    return 1;
                }
            }
        }

        private void PopulateClaimData()
        {
            try
            {
                if (string.IsNullOrEmpty(ClaimNumber))
                {
                    GenerateClaimNumber();
                }
                ProfessionalClaims.Id = CompleteProfessionalClaims.Id;
                ProfessionalClaims.ClaimNumber = ClaimNumber;
                ProfessionalClaims.ClaimDate = ClaimDate ?? DateTime.Today;
                ProfessionalClaims.ServiceDate = ServiceDate ?? DateTime.Today;
                ProfessionalClaims.AppointmentFacility = AppointmentFacility ?? "";
                ProfessionalClaims.POS = POS ?? "";
                ProfessionalClaims.PatientInfo = ProfessionalClaims.PatientInfo ?? "";
                ProfessionalClaims.CoPay = ProfessionalClaims.CoPay;
                ProfessionalClaims.PatientUncoveredAmount = ProfessionalClaims.PatientUncoveredAmount;
                ProfessionalClaims.Billing = ProfessionalClaims.Billing;
                ProfessionalClaims.Supervisor = ProfessionalClaims.Supervisor;
                ProfessionalClaims.Rendering = ProfessionalClaims.Rendering;
                ProfessionalClaims.ClaimStatus = ProfessionalClaims.ClaimStatus;
                ProfessionalClaims.OrganizationID = OrganizationID;
                ProfessionalClaims.Subscription = Subscription;

                // Set ICD codes
                CompleteProfessionalClaims.professionalClaimsICD = AddedICDCodes.ToList();

                // Ensure PatientInfo is properly formatted (this will already be set from OnPatientChanged)
                if (selectedPatient != null && string.IsNullOrEmpty(ProfessionalClaims.PatientInfo))
                {
                    ProfessionalClaims.PatientInfo = FormatPatientInfo(selectedPatient);
                }


                // Process ICD codes
                foreach (var icdCode in AddedICDCodes)
                {
                    icdCode.ProfessionalClaimsId = CompleteProfessionalClaims.Id;
                    if (icdCode.ICDId == Guid.Empty)
                    {
                        icdCode.ICDId = Guid.NewGuid();
                    }
                }

                // Process immunization/vaccine data
                foreach (var vaccine in AddedVaccines)
                {
                    vaccine.ProfessionalClaimsId = CompleteProfessionalClaims.Id;
                    if (vaccine.VaccineId == Guid.Empty)
                    {
                        vaccine.VaccineId = Guid.NewGuid();
                    }
                }
                foreach (var cpt in AddedCPTCodes)
                {
                    cpt.ProfessionalClaimsId = CompleteProfessionalClaims.Id;
                    if (cpt.CPTId == Guid.Empty)
                    {
                        cpt.CPTId = Guid.NewGuid();
                    }
                }
                foreach (var payment in PaymentsData)
                {
                    payment.ProfessionalClaimsId = CompleteProfessionalClaims.Id;
                    if (payment.PaymentsId == Guid.Empty)
                    {
                        payment.PaymentsId = Guid.NewGuid();
                    }
                }

                CompleteProfessionalClaims.professionalclaims = ProfessionalClaims;
                CompleteProfessionalClaims.professionalClaimsICD = AddedICDCodes;
                CompleteProfessionalClaims.professionalClaimsImmunization = AddedVaccines;
                CompleteProfessionalClaims.professionalClaimsCPT = AddedCPTCodes;
                CompleteProfessionalClaims.professionalClaimsPayments = PaymentsData;
                CompleteProfessionalClaims.OrganizationID = OrganizationID;
                CompleteProfessionalClaims.Subscription = Subscription;

                if (!string.IsNullOrEmpty(Remarks))
                {
                    try
                    {
                        var remarksProperty = typeof(ProfessionalClaims).GetProperty("Remarks");
                        remarksProperty?.SetValue(ProfessionalClaims, Remarks);
                    }
                    catch
                    {
                        // Ignore if property doesn't exist
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error populating claim data: {ex.Message}");
                throw;
            }
        }

        private bool ValidateForm()
        {
            try
            {
                if (OrganizationID == Guid.Empty)
                {
                    Snackbar?.Add("Organization information is missing. Please reload the page.", Severity.Error);
                    return false;
                }

                if (string.IsNullOrWhiteSpace(ClaimNumber))
                {
                    Snackbar?.Add("Claim number is required.", Severity.Warning);
                    return false;
                }

                if (!ClaimDate.HasValue)
                {
                    Snackbar?.Add("Claim date is required.", Severity.Warning);
                    return false;
                }

                if (!ServiceDate.HasValue)
                {
                    Snackbar?.Add("Service date is required.", Severity.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Validation error: {ex.Message}");
                return false;
            }
        }

        private async Task OnSubmit()
        {
            if (isSubmitting) return;
            try
            {
                isSubmitting = true;
                Console.WriteLine("Starting form submission...");

                if (!ValidateForm())
                {
                    Console.WriteLine("Form validation failed");
                    return;
                }

                Console.WriteLine("Form validation passed");

                // Merge temporary data with main data before submission
                MergeTempDataWithMainData();

                PopulateClaimData();
                Console.WriteLine($"Submitting CompleteProfessionalClaims: ID={CompleteProfessionalClaims.Id}");
                Console.WriteLine($"Current claim number being submitted: {ClaimNumber}");

                await ProfessionalClaimsService.AddProfessionalClaimsAsync(new List<CompleteProfessionalClaims> { CompleteProfessionalClaims }, OrganizationID, Subscription);
                Console.WriteLine("Successfully submitted to database");

                // Update the last used claim number
                if (int.TryParse(ClaimNumber, out int submittedClaimNumber))
                {
                    lastUsedClaimNumber = submittedClaimNumber;
                    Console.WriteLine($"Updated lastUsedClaimNumber to: {lastUsedClaimNumber}");
                }

                Snackbar?.Add("Professional claim submitted successfully!", Severity.Success);

                // After successful submission, prepare for next claim
                await ResetFormForNextClaim();

                // Generate the next claim number immediately and update the UI
                ClaimNumber = null; // Clear current claim number
                await GenerateClaimNumber(); // Generate next sequential number
                Console.WriteLine($"Next claim number generated: {ClaimNumber}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error submitting professional claim: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                var errorMessage = $"Error submitting claim: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $" Inner exception: {ex.InnerException.Message}";
                }
                Snackbar?.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isSubmitting = false;
                StateHasChanged();
            }
        }

        // Method to merge temporary data with main data
        private void MergeTempDataWithMainData()
        {
            try
            {
                // Merge Claim Header data if it was saved
                if (IsClaimHeaderSaved)
                {
                    RestoreClaimHeaderData();
                    Console.WriteLine("Merged claim header data for submission");
                }

                // Merge Claim Data if it was saved
                if (IsClaimDataSaved)
                {
                    RestoreClaimData();
                    Console.WriteLine("Merged claim data for submission");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error merging temporary data: {ex.Message}");
            }
        }

        private async Task ResetForm()
        {
            try
            {
                // Clear claim number so new one gets generated
                ClaimNumber = null;
                ClaimDate = DateTime.Today;
                ServiceDate = DateTime.Today;
                AppointmentFacility = "";
                POS = "";
                InitializeCompleteProfessionalClaims();

                // Clear temporary data
                InitializeTempData();

                // Generate new claim number for the reset form
                await GenerateClaimNumber();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error resetting form: {ex.Message}");
            }
        }

        // Modified ResetFormForNextClaim method to clear temporary data
        private async Task ResetFormForNextClaim()
        {
            try
            {
                // Reset main form data
                ClaimDate = DateTime.Today;
                ServiceDate = DateTime.Today;
                AppointmentFacility = "";
                POS = "";
                InitializeCompleteProfessionalClaims();

                // Clear temporary data
                InitializeTempData();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error resetting form for next claim: {ex.Message}");
            }
        }

        private void OnCancel()
        {
            _ = ResetForm();
            Console.WriteLine("Form cancelled");
        }

        // Updated FormatPatientInfo method to include all required fields
        private string FormatPatientInfo(Member patient)
        {
            if (patient == null) return "No Patient Selected";
            var patientDetails = new List<string>();

            // Add patient name
            if (!string.IsNullOrEmpty(patient.UserName))
            {
                patientDetails.Add(patient.UserName);
            }

            // Format and add DOB
            if (patient.DateOfBirth != null)
            {
                if (DateTime.TryParse(patient.DateOfBirth.ToString(), out DateTime dob))
                {
                    patientDetails.Add(dob.ToString("MM/dd/yyyy"));
                }
                else
                {
                    patientDetails.Add(patient.DateOfBirth.ToString());
                }
            }

            // Add Sexual Orientation/Gender
            if (!string.IsNullOrEmpty(patient.SexualOrientation))
            {
                patientDetails.Add(patient.SexualOrientation);
            }


            // Add Country
            if (!string.IsNullOrEmpty(patient.Country))
            {
                patientDetails.Add(patient.Country);
            }

            // Join all details with new lines
            return patientDetails.Any() ? string.Join(Environment.NewLine, patientDetails) : "Patient Information Not Available";
        }

        // Event handlers for field changes
        protected Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            IEnumerable<string> searchResults = Enumerable.Empty<string>();

            if (selectedDatabase == Source.CMS.ToString())
            {
                searchResults = _icdCodes
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (
                            (!string.IsNullOrEmpty(icd.Code) && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.Description) && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                searchResults = fdb_ICD
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (icd.ICD_CD_TYPE == "06" || icd.ICD_CD_TYPE == "05") &&
                        (
                            (!string.IsNullOrEmpty(icd.ICD_CD) && icd.ICD_CD.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.ICD_DESC) && icd.ICD_DESC.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.ICD_CD} - {icd.ICD_DESC ?? ""}")
                    .ToList();
            }

            return Task.FromResult(searchResults);
        }
        private async Task OnICDNameChanged(string value)
        {
            currentICDSelection = value;
            StateHasChanged();
        }

        private async Task AddICDCode()
        {
            if (!string.IsNullOrEmpty(currentICDSelection))
            {
                // Parse the selected ICD code (assuming format like "A00.0 - Cholera due to Vibrio cholerae")
                var parts = currentICDSelection.Split(" - ", 2);
                if (parts.Length >= 2)
                {
                    var code = parts[0].Trim();
                    var description = parts[1].Trim();

                    // Check if this ICD code is already added
                    if (AddedICDCodes.Any(x => x.Code == code))
                    {
                        // Show message that code is already added
                        // You can use your notification system here
                        return;
                    }

                    // Create new ICD entry
                    var newICD = new ProfessionalClaimsICD
                    {
                        ICDId = Guid.NewGuid(), // or however you generate IDs
                        Code = code,
                        Description = description
                    };

                    // Add to the list
                    AddedICDCodes.Add(newICD);

                    // Clear the selection
                    currentICDSelection = string.Empty;

                    // Refresh the grid
                    if (ICDGrid != null)
                    {
                        await ICDGrid.Refresh();
                    }

                    StateHasChanged();
                }
            }
        }

        private async Task AddVaccineCode()
        {
            if (!string.IsNullOrEmpty(currentVaccineSelection))
            {
                var vaccineName = currentVaccineSelection.Trim();

                // Check if this vaccine is already added
                if (AddedVaccines.Any(x => x.VaccineName == vaccineName))
                {
                    Snackbar?.Add("Vaccine already added", Severity.Warning);
                    return;
                }

                // Create new vaccine entry
                var newVaccine = new ProfessionalClaimsImmunization
                {
                    VaccineId = Guid.NewGuid(),
                    VaccineName = vaccineName,
                    VaccineDate = DateTime.Today,
                    // Remove this line if CompleteProfessionalClaims is not properly initialized
                    ProfessionalClaimsId = CompleteProfessionalClaims.Id
                };

                // Add to the list
                AddedVaccines.Add(newVaccine);

                // Clear the selection
                currentVaccineSelection = string.Empty;

                // Refresh the grid
                if (VaccineGrid != null)
                {
                    await VaccineGrid.Refresh();
                }

                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task ICDActionBeginHandler(ActionEventArgs<ProfessionalClaimsICD> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    // Validate ICD data if needed
                    if (string.IsNullOrEmpty(args.Data.Code))
                    {
                        args.Cancel = true;
                        Snackbar?.Add("ICD Code is required", Severity.Error);
                        return;
                    }
                    if (args.Data.ICDId == Guid.Empty)
                    {
                        args.Data.ICDId = Guid.NewGuid();
                    }
                    args.Data.ProfessionalClaimsId = CompleteProfessionalClaims.Id;
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting ICD code: {args.Data?.Code}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ProfessionalClaimsICD
                    {
                        ICDId = Guid.NewGuid(),
                        ProfessionalClaimsId = CompleteProfessionalClaims.Id,
                        Code = "",
                        Description = ""
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ICDActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing ICD grid action", Severity.Error);
            }
        }
        // Keep your existing action completed handler
        private async Task ICDActionCompletedHandler(ActionEventArgs<ProfessionalClaimsICD> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                // Handle any post-delete logic if needed
                StateHasChanged();
            }
        }

        private async Task VaccineActionBeginHandler(ActionEventArgs<ProfessionalClaimsImmunization> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    // Validate vaccine data
                    if (string.IsNullOrEmpty(args.Data.VaccineName))
                    {
                        args.Cancel = true;
                        Snackbar?.Add("Vaccine Name is required", Severity.Error);
                        return;
                    }

                    if (args.Data.VaccineDate == null || args.Data.VaccineDate == DateTime.MinValue)
                    {
                        args.Cancel = true;
                        Snackbar?.Add("Vaccine Date is required", Severity.Error);
                        return;
                    }

                    if (args.Data.VaccineId == Guid.Empty)
                    {
                        args.Data.VaccineId = Guid.NewGuid();
                    }

                    args.Data.ProfessionalClaimsId = CompleteProfessionalClaims.Id;

                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting vaccine: {args.Data?.VaccineName}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ProfessionalClaimsImmunization
                    {
                        VaccineId = Guid.NewGuid(),
                        // Only set this if CompleteProfessionalClaims is properly initialized
                        ProfessionalClaimsId = CompleteProfessionalClaims.Id,
                        VaccineName = "",
                        VaccineDate = DateTime.Today
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in VaccineActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing vaccine grid action", Severity.Error);
            }
        }

        // Corrected VaccineActionCompletedHandler method
        private async Task VaccineActionCompletedHandler(ActionEventArgs<ProfessionalClaimsImmunization> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                // Handle any post-delete logic if needed
                await InvokeAsync(StateHasChanged);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Handle any post-save logic if needed
                await InvokeAsync(StateHasChanged);
            }
        }

        protected Task<IEnumerable<string>> SearchCPTCodes(string value, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (string.IsNullOrWhiteSpace(value))
            {
                return Task.FromResult(Enumerable.Empty<string>());
            }

            var searchResults = _cptCodes
                .Where(cpt =>
                    (!string.IsNullOrEmpty(cpt.CPTCode) && cpt.CPTCode.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(cpt.Description) && cpt.Description.Contains(value, StringComparison.OrdinalIgnoreCase))
                )
                .Select(cpt => $"{cpt.CPTCode} - {cpt.Description ?? "No description available"}")
                .ToList();

            return Task.FromResult<IEnumerable<string>>(searchResults);
        }

        private async Task OnCPTNameChanged(string value)
        {
            currentCPTSelection = value;
            StateHasChanged();
        }

        private async Task AddCPTCode()
        {
            if (!string.IsNullOrEmpty(currentCPTSelection))
            {
                var parts = currentCPTSelection.Split(" - ", 2);
                if (parts.Length >= 2)
                {
                    var code = parts[0].Trim();
                    var description = parts[1].Trim();

                    if (AddedCPTCodes.Any(x => x.Code == code))
                    {
                        Snackbar?.Add("CPT code already added", Severity.Warning);
                        return;
                    }

                    var newCPT = new ProfessionalClaimsCPT
                    {
                        SerialNumber = AddedCPTCodes.Count + 1,
                        CPTId = Guid.NewGuid(),
                        Code = code,
                        Description = description,
                        POS = POS ?? "",
                        TOS = "",
                        SDOS = ServiceDate ?? DateTime.Today,
                        EDOS = ServiceDate ?? DateTime.Today,
                        M1 = "",
                        M2 = "",
                        M3 = "",
                        ICD1 = "",
                        ICD2 = "",
                        ICD3 = "",
                        ICD4 = "",
                        Units = 1,
                        BilledFee = 0.00m,
                        PcpId = "",
                        IsSelected = false
                    };

                    AddedCPTCodes.Add(newCPT);
                    currentCPTSelection = string.Empty;

                    if (CPTGrid != null)
                    {
                        await CPTGrid.Refresh();
                    }

                    StateHasChanged();
                }
            }
        }

        private async Task CPTActionBeginHandler(ActionEventArgs<ProfessionalClaimsCPT> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (string.IsNullOrEmpty(args.Data.Code))
                    {
                        args.Cancel = true;
                        Snackbar?.Add("CPT Code is required", Severity.Error);
                        return;
                    }

                    if (args.Data.CPTId == Guid.Empty)
                    {
                        args.Data.CPTId = Guid.NewGuid();
                    }
                    args.Data.ProfessionalClaimsId = CompleteProfessionalClaims.Id;

                    // Ensure dates are set if not provided
                    if (args.Data.SDOS == DateTime.MinValue)
                        args.Data.SDOS = ServiceDate ?? DateTime.Today;
                    if (args.Data.EDOS == DateTime.MinValue)
                        args.Data.EDOS = ServiceDate ?? DateTime.Today;

                    // Ensure units is at least 1
                    if (args.Data.Units <= 0)
                        args.Data.Units = 1;
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting CPT code: {args.Data?.Code}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ProfessionalClaimsCPT
                    {
                        CPTId = Guid.NewGuid(),
                        ProfessionalClaimsId = CompleteProfessionalClaims.Id,
                        Code = "",
                        Description = "",
                        POS = POS ?? "",
                        TOS = "",
                        SDOS = ServiceDate ?? DateTime.Today,
                        EDOS = ServiceDate ?? DateTime.Today,
                        M1 = "",
                        M2 = "",
                        M3 = "",
                        ICD1 = "",
                        ICD2 = "",
                        ICD3 = "",
                        ICD4 = "",
                        Units = 1,
                        BilledFee = 0.00m,
                        PcpId = "",
                        IsSelected = false
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CPTActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing CPT grid action", Severity.Error);
            }
        }

        private async Task CPTActionCompletedHandler(ActionEventArgs<ProfessionalClaimsCPT> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                for (int i = 0; i < AddedCPTCodes.Count; i++)
                {
                    AddedCPTCodes[i].SerialNumber = i + 1;
                }
                StateHasChanged();
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Handle any post-save logic if needed
                StateHasChanged();
            }
        }

        private async Task PaymentsActionBeginHandler(ActionEventArgs<ProfessionalClaimsPayments> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (string.IsNullOrEmpty(args.Data.From))
                    {
                        args.Cancel = true;
                        Snackbar?.Add("From field is required", Severity.Error);
                        return;
                    }

                    if (args.Data.PaymentsId == Guid.Empty)
                    {
                        args.Data.PaymentsId = Guid.NewGuid();
                    }
                    args.Data.ProfessionalClaimsId = CompleteProfessionalClaims.Id;

                    if (args.Data.Date == DateTime.MinValue)
                        args.Data.Date = DateTime.Today;

                    if (args.Data.Allowed < 0) args.Data.Allowed = 0;
                    if (args.Data.Deduct < 0) args.Data.Deduct = 0;
                    if (args.Data.CoIns < 0) args.Data.CoIns = 0;
                    if (args.Data.Copay < 0) args.Data.Copay = 0;
                    if (args.Data.Paid < 0) args.Data.Paid = 0;
                    if (args.Data.Adjust < 0) args.Data.Adjust = 0;
                    if (args.Data.Withheld < 0) args.Data.Withheld = 0;
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting payment record: {args.Data?.Id}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new ProfessionalClaimsPayments
                    {
                        PaymentsId = Guid.NewGuid(),
                        Id = PaymentsData.Count > 0 ? PaymentsData.Max(x => x.Id) + 1 : 1,
                        Date = DateTime.Today,
                        From = "",
                        Allowed = 0.00m,
                        Deduct = 0.00m,
                        CoIns = 0.00m,
                        Copay = 0.00m,
                        Paid = 0.00m,
                        Adjust = 0.00m,
                        Withheld = 0.00m
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in PaymentsActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing payment grid action", Severity.Error);
            }
        }

        private async Task PaymentsActionCompletedHandler(ActionEventArgs<ProfessionalClaimsPayments> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                // Re-sequence IDs if needed (optional)
                for (int i = 0; i < PaymentsData.Count; i++)
                {
                    PaymentsData[i].Id = i + 1;
                }
                StateHasChanged();
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Handle any post-save logic if needed
                StateHasChanged();
            }
        }
        private void OnClaimHeaderClicked()
        {
            // Restore previously saved data when opening the dialog
            if (IsClaimHeaderSaved)
            {
                RestoreClaimHeaderData();
            }
            isClaimHeaderDialogVisible = true;
            StateHasChanged();
        }

        private void OnClaimDataClicked()
        {
            // Restore previously saved data when opening the dialog
            if (IsClaimDataSaved)
            {
                RestoreClaimData();
            }
            isClaimDataDialogVisible = true;
            StateHasChanged();
        }


        private void RestoreClaimHeaderData()
        {
            try
            {
                if (TempClaimHeaderData.ContainsKey("ResidentType"))
                    ProfessionalClaims.ResidentType = TempClaimHeaderData["ResidentType"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("StudentStatus"))
                    ProfessionalClaims.StudentStatus = TempClaimHeaderData["StudentStatus"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("EmploymentStatus"))
                    ProfessionalClaims.EmploymentStatus = TempClaimHeaderData["EmploymentStatus"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("HealthyKidServiceYes"))
                    ProfessionalClaims.HealthyKidServiceYes = (bool)(TempClaimHeaderData["HealthyKidServiceYes"] ?? false);
                if (TempClaimHeaderData.ContainsKey("HealthyKidServiceNo"))
                    ProfessionalClaims.HealthyKidServiceNo = (bool)(TempClaimHeaderData["HealthyKidServiceNo"] ?? false);
                if (TempClaimHeaderData.ContainsKey("FamilyPlanningYes"))
                    ProfessionalClaims.FamilyPlanningYes = (bool)(TempClaimHeaderData["FamilyPlanningYes"] ?? false);
                if (TempClaimHeaderData.ContainsKey("FamilyPlanningNo"))
                    ProfessionalClaims.FamilyPlanningNo = (bool)(TempClaimHeaderData["FamilyPlanningNo"] ?? false);
                if (TempClaimHeaderData.ContainsKey("SterilizationAbortionYes"))
                    ProfessionalClaims.SterilizationAbortionYes = (bool)(TempClaimHeaderData["SterilizationAbortionYes"] ?? false);
                if (TempClaimHeaderData.ContainsKey("SterilizationAbortionNo"))
                    ProfessionalClaims.SterilizationAbortionNo = (bool)(TempClaimHeaderData["SterilizationAbortionNo"] ?? false);
                if (TempClaimHeaderData.ContainsKey("ClaimEditingIndicator"))
                    ProfessionalClaims.ClaimEditingIndicator = TempClaimHeaderData["ClaimEditingIndicator"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("ClaimType"))
                    ProfessionalClaims.ClaimType = TempClaimHeaderData["ClaimType"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("FacilityLabId"))
                    ProfessionalClaims.FacilityLabId = TempClaimHeaderData["FacilityLabId"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("FacilityType"))
                    ProfessionalClaims.FacilityType = TempClaimHeaderData["FacilityType"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("IsResubmittal"))
                    ProfessionalClaims.IsResubmittal = (bool)(TempClaimHeaderData["IsResubmittal"] ?? false);
                if (TempClaimHeaderData.ContainsKey("ResubmissionCode"))
                    ProfessionalClaims.ResubmissionCode = TempClaimHeaderData["ResubmissionCode"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("ResubmissionRefNumber"))
                    ProfessionalClaims.ResubmissionRefNumber = TempClaimHeaderData["ResubmissionRefNumber"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("Hcfa10d"))
                    ProfessionalClaims.Hcfa10d = TempClaimHeaderData["Hcfa10d"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("Hcfa19"))
                    ProfessionalClaims.Hcfa19 = TempClaimHeaderData["Hcfa19"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("ClaimData"))
                    ProfessionalClaims.ClaimData = TempClaimHeaderData["ClaimData"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("ProviderAssignmentIndicator"))
                    ProfessionalClaims.ProviderAssignmentIndicator = TempClaimHeaderData["ProviderAssignmentIndicator"]?.ToString();
                if (TempClaimHeaderData.ContainsKey("DelayReason"))
                    ProfessionalClaims.DelayReason = TempClaimHeaderData["DelayReason"]?.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error restoring claim header data: {ex.Message}");
            }
        }

        // Method to restore claim data from temporary storage
        private void RestoreClaimData()
        {
            try
            {
                if (TempClaimData.ContainsKey("EmploymentRelatedYes"))
                    ProfessionalClaims.EmploymentRelatedYes = (bool)(TempClaimData["EmploymentRelatedYes"] ?? false);
                if (TempClaimData.ContainsKey("EmploymentRelatedNo"))
                    ProfessionalClaims.EmploymentRelatedNo = (bool)(TempClaimData["EmploymentRelatedNo"] ?? false);
                if (TempClaimData.ContainsKey("AccidentAuto"))
                    ProfessionalClaims.AccidentAuto = (bool)(TempClaimData["AccidentAuto"] ?? false);
                if (TempClaimData.ContainsKey("AccidentNonAuto"))
                    ProfessionalClaims.AccidentNonAuto = (bool)(TempClaimData["AccidentNonAuto"] ?? false);
                if (TempClaimData.ContainsKey("AccidentNo"))
                    ProfessionalClaims.AccidentNo = (bool)(TempClaimData["AccidentNo"] ?? false);
                if (TempClaimData.ContainsKey("AccidentPlace"))
                    ProfessionalClaims.AccidentPlace = TempClaimData["AccidentPlace"]?.ToString();
                if (TempClaimData.ContainsKey("AccidentHour"))
                    ProfessionalClaims.AccidentHour = TempClaimData["AccidentHour"]?.ToString();
                if (TempClaimData.ContainsKey("ExternalCauseOfAccident"))
                    ProfessionalClaims.ExternalCauseOfAccident = TempClaimData["ExternalCauseOfAccident"]?.ToString();
                if (TempClaimData.ContainsKey("ResponsibilityIndicator"))
                    ProfessionalClaims.ResponsibilityIndicator = TempClaimData["ResponsibilityIndicator"]?.ToString();
                if (TempClaimData.ContainsKey("DocumentationIndicator"))
                    ProfessionalClaims.DocumentationIndicator = TempClaimData["DocumentationIndicator"]?.ToString();
                if (TempClaimData.ContainsKey("DocumentationType"))
                    ProfessionalClaims.DocumentationType = TempClaimData["DocumentationType"]?.ToString();
                if (TempClaimData.ContainsKey("AttachmentControlNumber"))
                    ProfessionalClaims.AttachmentControlNumber = TempClaimData["AttachmentControlNumber"]?.ToString();
                if (TempClaimData.ContainsKey("DateDocumentationSent"))
                    ProfessionalClaims.DateDocumentationSent = (DateTime?)(TempClaimData["DateDocumentationSent"]);
                if (TempClaimData.ContainsKey("ReleaseOfInformation"))
                    ProfessionalClaims.ReleaseOfInformation = TempClaimData["ReleaseOfInformation"]?.ToString();
                if (TempClaimData.ContainsKey("SignatureDate"))
                    ProfessionalClaims.SignatureDate = (DateTime?)(TempClaimData["SignatureDate"]);
                if (TempClaimData.ContainsKey("UnableToWorkFromDate"))
                    ProfessionalClaims.UnableToWorkFromDate = (DateTime?)(TempClaimData["UnableToWorkFromDate"]);
                if (TempClaimData.ContainsKey("UnableToWorkToDate"))
                    ProfessionalClaims.UnableToWorkToDate = (DateTime?)(TempClaimData["UnableToWorkToDate"]);
                if (TempClaimData.ContainsKey("HospitalizationFromDate"))
                    ProfessionalClaims.HospitalizationFromDate = (DateTime?)(TempClaimData["HospitalizationFromDate"]);
                if (TempClaimData.ContainsKey("HospitalizationToDate"))
                    ProfessionalClaims.HospitalizationToDate = (DateTime?)(TempClaimData["HospitalizationToDate"]);
                if (TempClaimData.ContainsKey("OutsideLabYes"))
                    ProfessionalClaims.OutsideLabYes = (bool)(TempClaimData["OutsideLabYes"] ?? false);
                if (TempClaimData.ContainsKey("OutsideLabNo"))
                    ProfessionalClaims.OutsideLabNo = (bool)(TempClaimData["OutsideLabNo"] ?? false);
                if (TempClaimData.ContainsKey("LabCharges"))
                    ProfessionalClaims.LabCharges = (decimal)(TempClaimData["LabCharges"]);
                if (TempClaimData.ContainsKey("Symptom"))
                    ProfessionalClaims.Symptom = TempClaimData["Symptom"]?.ToString();
                if (TempClaimData.ContainsKey("AccidentSymptomDate"))
                    ProfessionalClaims.AccidentSymptomDate = (DateTime?)(TempClaimData["AccidentSymptomDate"]);
                if (TempClaimData.ContainsKey("DateLastSeen"))
                    ProfessionalClaims.DateLastSeen = (DateTime?)(TempClaimData["DateLastSeen"]);
                if (TempClaimData.ContainsKey("InitialTreatmentDate"))
                    ProfessionalClaims.InitialTreatmentDate = (DateTime?)(TempClaimData["InitialTreatmentDate"]);
                if (TempClaimData.ContainsKey("SimilarSymptom"))
                    ProfessionalClaims.SimilarSymptom = TempClaimData["SimilarSymptom"]?.ToString();
                if (TempClaimData.ContainsKey("SimilarSymptomDate"))
                    ProfessionalClaims.SimilarSymptomDate = (DateTime?)(TempClaimData["SimilarSymptomDate"]);
                if (TempClaimData.ContainsKey("SpecialProgramCode"))
                    ProfessionalClaims.SpecialProgramCode = TempClaimData["SpecialProgramCode"]?.ToString();
                if (TempClaimData.ContainsKey("EPSDTReferralGiven"))
                    ProfessionalClaims.EPSDTReferralGiven = TempClaimData["EPSDTReferralGiven"]?.ToString();
                if (TempClaimData.ContainsKey("EPSDTReferralCode"))
                    ProfessionalClaims.EPSDTReferralCode = TempClaimData["EPSDTReferralCode"]?.ToString();
                if (TempClaimData.ContainsKey("ReferringProviderANSI"))
                    ProfessionalClaims.ReferringProviderANSI = TempClaimData["ReferringProviderANSI"]?.ToString();
                if (TempClaimData.ContainsKey("ReferringProviderName"))
                    ProfessionalClaims.ReferringProviderName = TempClaimData["ReferringProviderName"]?.ToString();
                if (TempClaimData.ContainsKey("ReferringProviderID"))
                    ProfessionalClaims.ReferringProviderID = TempClaimData["ReferringProviderID"]?.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error restoring claim data: {ex.Message}");
            }
        }

        private void CloseClaimHeaderDialog()
        {
            // Clear all claim header data
            ProfessionalClaims.ResidentType = string.Empty;
            ProfessionalClaims.StudentStatus = string.Empty;
            ProfessionalClaims.EmploymentStatus = string.Empty;
            ProfessionalClaims.HealthyKidServiceYes = false;
            ProfessionalClaims.HealthyKidServiceNo = false;
            ProfessionalClaims.FamilyPlanningYes = false;
            ProfessionalClaims.FamilyPlanningNo = false;
            ProfessionalClaims.SterilizationAbortionYes = false;
            ProfessionalClaims.SterilizationAbortionNo = false;
            ProfessionalClaims.ClaimEditingIndicator = string.Empty;
            ProfessionalClaims.ClaimType = string.Empty;
            ProfessionalClaims.FacilityLabId = string.Empty;
            ProfessionalClaims.FacilityType = string.Empty;
            ProfessionalClaims.IsResubmittal = false;
            ProfessionalClaims.ResubmissionCode = string.Empty;
            ProfessionalClaims.ResubmissionRefNumber = string.Empty;
            ProfessionalClaims.Hcfa10d = string.Empty;
            ProfessionalClaims.Hcfa19 = string.Empty;
            ProfessionalClaims.ClaimData = string.Empty;
            ProfessionalClaims.ProviderAssignmentIndicator = string.Empty;
            ProfessionalClaims.DelayReason = string.Empty;

            isClaimHeaderDialogVisible = false;
            StateHasChanged();
        }

        private void CloseClaimDataDialog()
        {
            // Clear all claim data
            ProfessionalClaims.EmploymentRelatedYes = false;
            ProfessionalClaims.EmploymentRelatedNo = false;
            ProfessionalClaims.AccidentAuto = false;
            ProfessionalClaims.AccidentNonAuto = false;
            ProfessionalClaims.AccidentNo = false;
            ProfessionalClaims.AccidentPlace = string.Empty;
            ProfessionalClaims.AccidentHour = string.Empty;
            ProfessionalClaims.ExternalCauseOfAccident = string.Empty;
            ProfessionalClaims.ResponsibilityIndicator = string.Empty;
            ProfessionalClaims.DocumentationIndicator = string.Empty;
            ProfessionalClaims.DocumentationType = string.Empty;
            ProfessionalClaims.AttachmentControlNumber = string.Empty;
            ProfessionalClaims.DateDocumentationSent = null;
            ProfessionalClaims.ReleaseOfInformation = string.Empty;
            ProfessionalClaims.SignatureDate = null;
            ProfessionalClaims.UnableToWorkFromDate = null;
            ProfessionalClaims.UnableToWorkToDate = null;
            ProfessionalClaims.HospitalizationFromDate = null;
            ProfessionalClaims.HospitalizationToDate = null;
            ProfessionalClaims.OutsideLabYes = false;
            ProfessionalClaims.OutsideLabNo = false;
            ProfessionalClaims.LabCharges = decimal.Zero;
            ProfessionalClaims.Symptom = string.Empty;
            ProfessionalClaims.AccidentSymptomDate = null;
            ProfessionalClaims.DateLastSeen = null;
            ProfessionalClaims.InitialTreatmentDate = null;
            ProfessionalClaims.SimilarSymptom = string.Empty;
            ProfessionalClaims.SimilarSymptomDate = null;
            ProfessionalClaims.SpecialProgramCode = string.Empty;
            ProfessionalClaims.EPSDTReferralGiven = string.Empty;
            ProfessionalClaims.EPSDTReferralCode = string.Empty;
            ProfessionalClaims.ReferringProviderANSI = string.Empty;
            ProfessionalClaims.ReferringProviderName = string.Empty;
            ProfessionalClaims.ReferringProviderID = string.Empty;

            isClaimDataDialogVisible = false;
            StateHasChanged();
        }

        protected async Task OnOptionsClicked()
        {
            try
            {
                // Open options/settings dialog
                await ShowOptionsDialog();
            }
            catch (Exception ex)
            {
                await HandleError("Error opening options", ex);
            }
        }

        protected async Task OnPrintHCFAClicked()
        {
            try
            {
                // Generate and print HCFA form
                await GenerateHCFAForm();
            }
            catch (Exception ex)
            {
                await HandleError("Error printing HCFA form", ex);
            }
        }

        protected async Task OnAdjustmentsClicked()
        {
            try
            {
                // Open adjustments management dialog
                await ShowAdjustmentsDialog();
            }
            catch (Exception ex)
            {
                await HandleError("Error opening adjustments", ex);
            }
        }

        protected async Task OnProgNotesClicked()
        {
            try
            {
                // Open progress notes (currently disabled)
                await ShowProgressNotesDialog();
            }
            catch (Exception ex)
            {
                await HandleError("Error opening progress notes", ex);
            }
        }

        protected async Task OnCPTPayersClicked()
        {
            try
            {
                // Open CPT payers management
                await ShowCPTPayersDialog();
            }
            catch (Exception ex)
            {
                await HandleError("Error opening CPT payers", ex);
            }
        }

        protected async Task OnOkClicked()
        {
            try
            {
                // Save changes and close dialog
                await SaveChanges();
                await CloseDialog();
            }
            catch (Exception ex)
            {
                await HandleError("Error saving changes", ex);
            }
        }

        protected async Task OnCancelClicked()
        {
            try
            {
                // Discard changes and close dialog
                await DiscardChanges();
                await CloseDialog();
            }
            catch (Exception ex)
            {
                await HandleError("Error canceling", ex);
            }
        }

        // Private helper methods

        private async Task ShowClaimHeaderDialog()
        {
            // Implementation for showing claim header editing dialog
            // This would typically navigate to a new component or show a modal
            await InvokeAsync(StateHasChanged);
        }

        private async Task ShowClaimDataDialog()
        {
            // Implementation for showing claim data editing dialog
            await InvokeAsync(StateHasChanged);
        }

        private async Task ShowOptionsDialog()
        {
            // Implementation for showing options dialog
            await InvokeAsync(StateHasChanged);
        }

        private async Task GenerateHCFAForm()
        {
            // Implementation for generating HCFA form
            // This would typically call a service to generate PDF or print
            await Task.Delay(1000); // Simulate processing time
        }

        private async Task ShowAdjustmentsDialog()
        {
            // Implementation for showing adjustments dialog
            await InvokeAsync(StateHasChanged);
        }

        private async Task ShowProgressNotesDialog()
        {
            // Implementation for showing progress notes dialog
            await InvokeAsync(StateHasChanged);
        }

        private async Task ShowCPTPayersDialog()
        {
            // Implementation for showing CPT payers dialog
            await InvokeAsync(StateHasChanged);
        }

        private async Task SaveChanges()
        {
            // Implementation for saving changes
            // This would typically call a service to persist data
            await Task.Delay(500); // Simulate save operation
        }

        private async Task DiscardChanges()
        {
            // Implementation for discarding changes
            // Reset form state or reload original data
            await Task.CompletedTask;
        }

        private async Task CloseDialog()
        {
            // Implementation for closing the current dialog
            // This would typically navigate back or hide modal
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleError(string message, Exception ex)
        {
            // Implementation for error handling
            // Log error and show user-friendly message
            Console.WriteLine($"{message}: {ex.Message}");
            // You might want to show a toast notification or error dialog here
            await InvokeAsync(StateHasChanged);
        }

        private CancellationTokenSource _searchVaccineCancellationTokenSource;
        private CancellationTokenSource _searchFDBVaccineCancellationTokenSource;

        protected async Task<IEnumerable<string>> SearchVaccines(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return Enumerable.Empty<string>();

            try
            {
                // Cancel previous search if still running
                _searchVaccineCancellationTokenSource?.Cancel();
                _searchVaccineCancellationTokenSource = new CancellationTokenSource();

                // Combine cancellation tokens
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchVaccineCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with search term
                var results = await VaccineService.GetAllVaccinesDataBySearchTermAsync(value);

                return results
                    .Where(t => !string.IsNullOrEmpty(t.VaccineName))
                    .Select(t => t.VaccineName)
                    .Distinct()
                    .ToList();
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search vaccines");
                return Enumerable.Empty<string>();
            }
        }

        // Corrected OnVaccineNameChanged method
        private async Task OnVaccineNameChanged(string value)
        {
            currentVaccineSelection = value;
            await InvokeAsync(StateHasChanged);
        }

        private async Task<IEnumerable<FDBVaccines>> SearchFDBVaccine(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<FDBVaccines>();

            // Cancel previous search if still running
            _searchFDBVaccineCancellationTokenSource?.Cancel();
            _searchFDBVaccineCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine cancellation tokens
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchFDBVaccineCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with search term
                return await FDBService.GetVaccinesBySearchTerm(searchTerm);
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<FDBVaccines>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search FDB vaccines");
                return Enumerable.Empty<FDBVaccines>();
            }
        }
        public bool HealthyKidServiceYes
        {
            get => ProfessionalClaims.HealthyKidServiceYes;
            set
            {
                ProfessionalClaims.HealthyKidServiceYes = value;
                if (value)
                {
                    ProfessionalClaims.HealthyKidServiceNo = false;
                }
                StateHasChanged();
            }
        }

        public bool HealthyKidServiceNo
        {
            get => ProfessionalClaims.HealthyKidServiceNo;
            set
            {
                ProfessionalClaims.HealthyKidServiceNo = value;
                if (value)
                {
                    ProfessionalClaims.HealthyKidServiceYes = false;
                }
                StateHasChanged();
            }
        }

        public bool FamilyPlanningYes
        {
            get => ProfessionalClaims.FamilyPlanningYes;
            set
            {
                ProfessionalClaims.FamilyPlanningYes = value;
                if (value)
                {
                    ProfessionalClaims.FamilyPlanningNo = false;
                }
                StateHasChanged();
            }
        }

        public bool FamilyPlanningNo
        {
            get => ProfessionalClaims.FamilyPlanningNo;
            set
            {
                ProfessionalClaims.FamilyPlanningNo = value;
                if (value)
                {
                    ProfessionalClaims.FamilyPlanningYes = false;
                }
                StateHasChanged();
            }
        }
        public bool SterilizationAbortionYes
        {
            get => ProfessionalClaims.SterilizationAbortionYes;
            set
            {
                ProfessionalClaims.SterilizationAbortionYes = value;
                if (value)
                {
                    ProfessionalClaims.SterilizationAbortionNo = false;
                }
                StateHasChanged();
            }
        }

        public bool SterilizationAbortionNo
        {
            get => ProfessionalClaims.SterilizationAbortionNo;
            set
            {
                ProfessionalClaims.SterilizationAbortionNo = value;
                if (value)
                {
                    ProfessionalClaims.SterilizationAbortionYes = false;
                }
                StateHasChanged();
            }
        }
        public bool EmploymentRelatedYes
        {
            get => ProfessionalClaims.EmploymentRelatedYes;
            set
            {
                ProfessionalClaims.EmploymentRelatedYes = value;
                if (value)
                {
                    ProfessionalClaims.EmploymentRelatedNo = false;
                }
                StateHasChanged();
            }
        }

        public bool EmploymentRelatedNo
        {
            get => ProfessionalClaims.EmploymentRelatedNo;
            set
            {
                ProfessionalClaims.EmploymentRelatedNo = value;
                if (value)
                {
                    ProfessionalClaims.EmploymentRelatedYes = false;
                }
                StateHasChanged();
            }
        }
        public bool AccidentAuto
        {
            get => ProfessionalClaims.AccidentAuto;
            set
            {
                ProfessionalClaims.AccidentAuto = value;
                if (value)
                {
                    ProfessionalClaims.AccidentNonAuto = false;
                    ProfessionalClaims.AccidentNo = false;
                }
                StateHasChanged();
            }
        }

        public bool AccidentNonAuto
        {
            get => ProfessionalClaims.AccidentNonAuto;
            set
            {
                ProfessionalClaims.AccidentNonAuto = value;
                if (value)
                {
                    ProfessionalClaims.AccidentAuto = false;
                    ProfessionalClaims.AccidentNo = false;
                }
                StateHasChanged();
            }
        }

        public bool AccidentNo
        {
            get => ProfessionalClaims.AccidentNo;
            set
            {
                ProfessionalClaims.AccidentNo = value;
                if (value)
                {
                    ProfessionalClaims.AccidentAuto = false;
                    ProfessionalClaims.AccidentNonAuto = false;
                }
                StateHasChanged();
            }
        }
    }

}